#!/usr/bin/env python3
"""
第三阶段高级工作流引擎演示
展示工作流引擎、DSL、监控和增强版Supervisor的完整功能
"""

import asyncio
import json
import yaml
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.langgraph_system.workflow import (
    AdvancedWorkflowEngine, WorkflowDSLParser, WorkflowTemplateLibrary,
    WorkflowMonitor, MonitorEventType, AlertRule
)
from src.langgraph_system.agents.enhanced_supervisor_agent import EnhancedSupervisorAgent


class Phase3WorkflowDemo:
    """第三阶段高级工作流引擎演示类"""
    
    def __init__(self):
        """初始化演示系统"""
        self.supervisor = None
        self.workflow_engine = None
        self.dsl_parser = WorkflowDSLParser()
        self.template_library = WorkflowTemplateLibrary()
        self.monitor = WorkflowMonitor()
        
        print("🚀 第三阶段高级工作流引擎演示初始化完成")
    
    async def initialize_system(self):
        """初始化系统组件"""
        print("\n📋 正在初始化系统组件...")
        
        try:
            # 初始化增强版Supervisor
            print("  ⚡ 初始化增强版Supervisor...")
            self.supervisor = EnhancedSupervisorAgent()
            self.workflow_engine = self.supervisor.workflow_engine
            
            print("    ✅ 增强版Supervisor初始化完成")
            
            # 启动监控
            print("  📊 启动工作流监控...")
            self.monitor.start_monitoring()
            
            # 设置告警规则
            self._setup_alert_rules()
            
            print("✅ 系统组件初始化完成")
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            raise
    
    def _setup_alert_rules(self):
        """设置告警规则"""
        # 执行时间过长告警
        execution_time_rule = AlertRule(
            id="long_execution_time",
            name="执行时间过长告警",
            condition="execution_time_exceeded",
            threshold=300.0,  # 5分钟
            severity="warning"
        )
        self.monitor.add_alert_rule(execution_time_rule)
        
        # 错误率过高告警
        error_rate_rule = AlertRule(
            id="high_error_rate",
            name="错误率过高告警",
            condition="error_rate_exceeded",
            threshold=0.2,  # 20%
            severity="error"
        )
        self.monitor.add_alert_rule(error_rate_rule)
        
        print("    ✅ 告警规则设置完成")
    
    async def demo_dsl_workflow_creation(self):
        """演示DSL工作流创建"""
        print("\n" + "="*60)
        print("🎯 演示DSL工作流创建")
        print("="*60)
        
        # 1. YAML格式工作流
        print("\n📝 创建YAML格式工作流...")
        yaml_workflow = """
id: yaml_demo_workflow
name: YAML演示工作流
description: 使用YAML DSL定义的复杂工作流
version: "1.0.0"
global_timeout: 3600
max_parallel_tasks: 5

nodes:
  requirements_analysis:
    name: 需求分析
    type: task
    agent_id: product_manager
    task_config:
      type: analyze_requirements
      description: 分析项目需求和目标
      parameters:
        depth: detailed
        stakeholders: [users, business, technical]
    timeout: 300
    max_retries: 2

  architecture_design:
    name: 系统架构设计
    type: task
    agent_id: architect
    task_config:
      type: design_system
      description: 设计系统整体架构
      parameters:
        architecture_type: microservices
        scalability: high
    dependencies:
      - requirements_analysis
    timeout: 600

  parallel_development:
    name: 并行开发阶段
    type: parallel

  frontend_development:
    name: 前端开发
    type: task
    agent_id: coder
    task_config:
      type: generate_code
      description: 开发前端用户界面
      parameters:
        framework: react
        component_type: frontend
    dependencies:
      - architecture_design
    timeout: 1200

  backend_development:
    name: 后端开发
    type: task
    agent_id: coder
    task_config:
      type: generate_code
      description: 开发后端API服务
      parameters:
        framework: fastapi
        component_type: backend
    dependencies:
      - architecture_design
    timeout: 1200

  database_setup:
    name: 数据库设置
    type: task
    agent_id: devops
    task_config:
      type: setup_database
      description: 配置数据库和数据模型
      parameters:
        database_type: postgresql
        migration: true
    dependencies:
      - architecture_design
    timeout: 600

  integration_test:
    name: 集成测试
    type: task
    agent_id: qa
    task_config:
      type: integration_test
      description: 执行系统集成测试
      parameters:
        test_scope: full_system
        coverage_threshold: 80
    dependencies:
      - frontend_development
      - backend_development
      - database_setup
    timeout: 900

  quality_gate:
    name: 质量门禁
    type: condition
    conditions:
      high_quality:
        condition:
          type: greater_than
          field: integration_test.coverage
          value: 80
        next_nodes:
          - deployment
      medium_quality:
        condition:
          type: greater_than
          field: integration_test.coverage
          value: 60
        next_nodes:
          - additional_testing
      default:
        next_nodes:
          - fix_issues
    dependencies:
      - integration_test

  additional_testing:
    name: 补充测试
    type: task
    agent_id: qa
    task_config:
      type: additional_testing
      description: 执行补充测试以提高覆盖率
    timeout: 600

  fix_issues:
    name: 修复问题
    type: task
    agent_id: coder
    task_config:
      type: fix_issues
      description: 修复测试中发现的问题
    timeout: 900

  deployment:
    name: 生产部署
    type: task
    agent_id: devops
    task_config:
      type: deploy_production
      description: 部署到生产环境
      parameters:
        environment: production
        strategy: blue_green
    timeout: 1800

  documentation:
    name: 文档生成
    type: task
    agent_id: documentation
    task_config:
      type: generate_documentation
      description: 生成项目文档
      parameters:
        doc_types: [api, user_guide, deployment]
    dependencies:
      - deployment
    timeout: 600

edges:
  - from: requirements_analysis
    to: architecture_design
  - from: architecture_design
    to: frontend_development
  - from: architecture_design
    to: backend_development
  - from: architecture_design
    to: database_setup
  - from: frontend_development
    to: integration_test
  - from: backend_development
    to: integration_test
  - from: database_setup
    to: integration_test
  - from: integration_test
    to: quality_gate
  - from: quality_gate
    to: deployment
  - from: quality_gate
    to: additional_testing
  - from: quality_gate
    to: fix_issues
  - from: additional_testing
    to: deployment
  - from: fix_issues
    to: integration_test
  - from: deployment
    to: documentation

start_nodes:
  - requirements_analysis

end_nodes:
  - documentation

metadata:
  project_type: web_application
  complexity: high
  estimated_duration: 7200
  team_size: 6
"""
        
        try:
            workflow_id = await self.supervisor.create_workflow_from_yaml(yaml_workflow)
            print(f"  ✅ YAML工作流创建成功: {workflow_id}")
            
            # 显示工作流信息
            workflow_def = self.supervisor.workflow_cache[workflow_id]
            print(f"    📊 节点数量: {len(workflow_def.nodes)}")
            print(f"    🔗 边数量: {len(workflow_def.edges)}")
            print(f"    ⏱️ 预计执行时间: {workflow_def.metadata.get('estimated_duration', 'N/A')}秒")
            
        except Exception as e:
            print(f"  ❌ YAML工作流创建失败: {e}")
        
        # 2. JSON格式工作流
        print("\n📄 创建JSON格式工作流...")
        json_workflow = {
            "id": "json_demo_workflow",
            "name": "JSON演示工作流",
            "description": "使用JSON DSL定义的简单工作流",
            "version": "1.0.0",
            "nodes": {
                "data_processing": {
                    "name": "数据处理",
                    "type": "task",
                    "agent_id": "coder",
                    "task_config": {
                        "type": "process_data",
                        "description": "处理输入数据",
                        "parameters": {
                            "format": "json",
                            "validation": True
                        }
                    }
                },
                "result_validation": {
                    "name": "结果验证",
                    "type": "task",
                    "agent_id": "qa",
                    "task_config": {
                        "type": "validate_results",
                        "description": "验证处理结果"
                    },
                    "dependencies": ["data_processing"]
                }
            },
            "edges": [
                {"from": "data_processing", "to": "result_validation"}
            ],
            "start_nodes": ["data_processing"],
            "end_nodes": ["result_validation"]
        }
        
        try:
            workflow_id = await self.supervisor.create_workflow_from_json(json.dumps(json_workflow))
            print(f"  ✅ JSON工作流创建成功: {workflow_id}")
            
        except Exception as e:
            print(f"  ❌ JSON工作流创建失败: {e}")
    
    async def demo_template_workflows(self):
        """演示模板工作流"""
        print("\n" + "="*60)
        print("📚 演示模板工作流")
        print("="*60)
        
        # 1. 列出可用模板
        print("\n📋 可用工作流模板:")
        templates = self.supervisor.get_workflow_templates()
        for i, template in enumerate(templates, 1):
            print(f"  {i}. {template['title']}")
            print(f"     描述: {template['description']}")
        
        # 2. 从模板创建工作流
        print("\n🏗️ 从模板创建自定义工作流...")
        
        try:
            # 使用软件开发模板
            workflow_id = await self.supervisor.create_workflow_from_template(
                "software_development",
                "custom_software_project",
                {
                    "name": "自定义软件项目",
                    "description": "基于模板的自定义软件开发工作流",
                    "global_timeout": 7200,
                    "nodes": {
                        "requirements_analysis": {
                            "task_config": {
                                "parameters": {
                                    "project_type": "web_application",
                                    "complexity": "medium"
                                }
                            }
                        },
                        "architecture_design": {
                            "task_config": {
                                "parameters": {
                                    "architecture_style": "microservices",
                                    "scalability_requirements": "high"
                                }
                            }
                        }
                    }
                }
            )
            
            print(f"  ✅ 模板工作流创建成功: {workflow_id}")
            
            # 显示自定义配置
            workflow_def = self.supervisor.workflow_cache[workflow_id]
            print(f"    📝 工作流名称: {workflow_def.name}")
            print(f"    ⏰ 全局超时: {workflow_def.global_timeout}秒")
            
        except Exception as e:
            print(f"  ❌ 模板工作流创建失败: {e}")
        
        # 3. 创建并行处理工作流
        print("\n⚡ 创建并行处理工作流...")
        
        try:
            parallel_workflow_id = await self.supervisor.create_workflow_from_template(
                "parallel_processing",
                "data_analysis_pipeline",
                {
                    "name": "数据分析管道",
                    "description": "并行数据处理和分析工作流",
                    "max_parallel_tasks": 8,
                    "nodes": {
                        "task_1": {
                            "task_config": {
                                "type": "analyze_dataset",
                                "description": "分析数据集A",
                                "parameters": {"dataset": "dataset_a.csv"}
                            }
                        },
                        "task_2": {
                            "task_config": {
                                "type": "analyze_dataset", 
                                "description": "分析数据集B",
                                "parameters": {"dataset": "dataset_b.csv"}
                            }
                        },
                        "task_3": {
                            "task_config": {
                                "type": "analyze_dataset",
                                "description": "分析数据集C", 
                                "parameters": {"dataset": "dataset_c.csv"}
                            }
                        }
                    }
                }
            )
            
            print(f"  ✅ 并行工作流创建成功: {parallel_workflow_id}")
            
        except Exception as e:
            print(f"  ❌ 并行工作流创建失败: {e}")
    
    async def demo_workflow_execution(self):
        """演示工作流执行"""
        print("\n" + "="*60)
        print("▶️ 演示工作流执行")
        print("="*60)
        
        # 1. 执行简单工作流
        print("\n🎯 执行简单工作流...")
        
        try:
            # 创建简单工作流用于演示
            simple_workflow_id = await self.supervisor.create_workflow_from_template(
                "simple_task",
                "demo_simple_execution",
                {
                    "name": "演示简单执行",
                    "nodes": {
                        "main_task": {
                            "task_config": {
                                "type": "demo_task",
                                "description": "演示任务执行",
                                "parameters": {"demo": True}
                            }
                        }
                    }
                }
            )
            
            # 执行工作流
            print(f"  🚀 开始执行工作流: {simple_workflow_id}")
            execution = await self.supervisor.execute_advanced_workflow(
                simple_workflow_id,
                {"input_data": "演示数据", "timestamp": datetime.now().isoformat()}
            )
            
            print(f"  ✅ 工作流执行完成")
            print(f"    📊 执行ID: {execution.id}")
            print(f"    📈 状态: {execution.status.value}")
            print(f"    ⏱️ 执行时间: {execution.execution_time:.2f}秒" if execution.execution_time else "    ⏱️ 执行时间: N/A")
            print(f"    📋 完成节点: {len(execution.completed_nodes)}")
            
        except Exception as e:
            print(f"  ❌ 简单工作流执行失败: {e}")
        
        # 2. 并行工作流执行
        print("\n⚡ 执行并行工作流...")
        
        try:
            # 创建多个工作流配置用于并行执行
            workflow_configs = []
            
            for i in range(3):
                workflow_id = await self.supervisor.create_workflow_from_template(
                    "simple_task",
                    f"parallel_demo_{i}",
                    {
                        "name": f"并行演示工作流 {i}",
                        "nodes": {
                            "main_task": {
                                "task_config": {
                                    "type": "parallel_demo",
                                    "description": f"并行任务 {i}",
                                    "parameters": {"task_id": i}
                                }
                            }
                        }
                    }
                )
                
                workflow_configs.append({
                    "workflow_id": workflow_id,
                    "input_data": {"task_index": i, "batch_id": "demo_batch"}
                })
            
            # 并行执行
            print(f"  🚀 开始并行执行 {len(workflow_configs)} 个工作流...")
            executions = await self.supervisor.execute_parallel_workflow(workflow_configs)
            
            print(f"  ✅ 并行执行完成")
            print(f"    📊 成功执行: {len(executions)} 个工作流")
            
            for i, execution in enumerate(executions):
                print(f"    • 工作流 {i}: {execution.status.value}")
            
        except Exception as e:
            print(f"  ❌ 并行工作流执行失败: {e}")
    
    async def demo_workflow_monitoring(self):
        """演示工作流监控"""
        print("\n" + "="*60)
        print("📊 演示工作流监控")
        print("="*60)
        
        # 1. 监控仪表板
        print("\n📈 监控仪表板数据:")
        dashboard_data = self.supervisor.get_monitoring_dashboard()
        
        print(f"  📊 系统统计:")
        stats = dashboard_data["statistics"]
        print(f"    • 总执行数: {stats['total_executions']}")
        print(f"    • 成功执行: {stats['successful_executions']}")
        print(f"    • 失败执行: {stats['failed_executions']}")
        print(f"    • 平均执行时间: {stats['average_execution_time']:.2f}秒")
        print(f"    • 错误率: {stats['error_rate']:.2%}")
        
        print(f"\n  🔄 实时状态:")
        print(f"    • 活跃执行: {dashboard_data['active_executions']}")
        print(f"    • 活跃告警: {dashboard_data['active_alerts']}")
        
        performance = dashboard_data["performance_summary"]
        print(f"\n  📈 性能摘要:")
        print(f"    • 成功率: {performance['success_rate']:.1f}%")
        print(f"    • 总节点数: {performance['total_nodes']}")
        
        # 2. 最近事件
        print(f"\n📋 最近事件:")
        recent_events = dashboard_data["recent_events"]
        if recent_events:
            for event in recent_events[-5:]:  # 显示最近5个事件
                print(f"    • [{event['severity'].upper()}] {event['type']}: {event['message']}")
        else:
            print("    暂无事件记录")
        
        # 3. 设置调试断点演示
        print(f"\n🐛 调试功能演示:")
        
        # 创建一个工作流用于调试演示
        try:
            debug_workflow_id = await self.supervisor.create_workflow_from_template(
                "simple_task",
                "debug_demo_workflow",
                {"name": "调试演示工作流"}
            )
            
            # 模拟设置断点
            execution_id = "demo_execution_123"
            node_id = "main_task"
            
            print(f"    🔍 在节点 '{node_id}' 设置断点")
            self.supervisor.set_workflow_breakpoint(execution_id, node_id)
            
            print(f"    👣 启用单步调试模式")
            self.supervisor.enable_step_mode(execution_id)
            
            print(f"    📝 获取执行跟踪")
            trace = self.supervisor.get_execution_trace(execution_id)
            print(f"      跟踪记录数: {len(trace)}")
            
        except Exception as e:
            print(f"    ❌ 调试功能演示失败: {e}")
    
    async def demo_workflow_optimization(self):
        """演示工作流优化"""
        print("\n" + "="*60)
        print("🔧 演示工作流优化")
        print("="*60)
        
        # 1. 创建一个复杂工作流用于优化分析
        print("\n📋 创建复杂工作流用于优化分析...")
        
        try:
            complex_workflow_id = await self.supervisor.create_workflow_from_template(
                "software_development",
                "optimization_demo_workflow",
                {
                    "name": "优化演示工作流",
                    "description": "用于演示优化功能的复杂工作流"
                }
            )
            
            print(f"  ✅ 复杂工作流创建成功: {complex_workflow_id}")
            
            # 2. 执行优化分析
            print(f"\n🔍 执行工作流优化分析...")
            optimization_result = self.supervisor.optimize_workflow(complex_workflow_id)
            
            print(f"  📊 优化分析结果:")
            print(f"    工作流ID: {optimization_result['workflow_id']}")
            
            suggestions = optimization_result["optimization_suggestions"]
            print(f"    优化建议数量: {len(suggestions)}")
            
            for i, suggestion in enumerate(suggestions, 1):
                print(f"\n    建议 {i}: {suggestion['type']}")
                print(f"      描述: {suggestion['description']}")
                if 'nodes' in suggestion:
                    print(f"      涉及节点: {suggestion['nodes']}")
                if 'estimated_speedup' in suggestion:
                    print(f"      预计加速: {suggestion['estimated_speedup']:.1%}")
            
            # 3. 显示预期改进
            improvement = optimization_result["estimated_improvement"]
            print(f"\n  📈 预期改进:")
            print(f"    预计加速: {improvement['estimated_speedup']:.1%}")
            print(f"    复杂度降低: {improvement['complexity_reduction']:.1%}")
            print(f"    整体改进: {improvement['overall_improvement']:.1%}")
            
        except Exception as e:
            print(f"  ❌ 工作流优化演示失败: {e}")
    
    async def demo_dynamic_workflow_creation(self):
        """演示动态工作流创建"""
        print("\n" + "="*60)
        print("🤖 演示动态工作流创建")
        print("="*60)
        
        print("\n🧠 基于任务描述动态生成工作流...")
        
        try:
            # 动态创建工作流
            task_description = "开发一个用户管理系统，包括用户注册、登录、个人信息管理功能"
            requirements = {
                "project_type": "web_application",
                "technology_stack": "Python + React",
                "database": "PostgreSQL",
                "deployment": "Docker + Kubernetes",
                "timeline": "4周",
                "team_size": 4
            }
            
            print(f"  📝 任务描述: {task_description}")
            print(f"  📋 需求详情:")
            for key, value in requirements.items():
                print(f"    • {key}: {value}")
            
            dynamic_workflow_id = await self.supervisor.create_dynamic_workflow(
                task_description, requirements
            )
            
            print(f"\n  ✅ 动态工作流创建成功: {dynamic_workflow_id}")
            
            # 显示生成的工作流信息
            workflow_def = self.supervisor.workflow_cache[dynamic_workflow_id]
            print(f"    📊 生成的工作流信息:")
            print(f"      节点数量: {len(workflow_def.nodes)}")
            print(f"      边数量: {len(workflow_def.edges)}")
            print(f"      开始节点: {workflow_def.start_nodes}")
            print(f"      结束节点: {workflow_def.end_nodes}")
            
            # 显示节点详情
            print(f"\n    📋 工作流节点:")
            for node_id, node in workflow_def.nodes.items():
                print(f"      • {node.name} ({node.agent_id})")
            
        except Exception as e:
            print(f"  ❌ 动态工作流创建失败: {e}")
    
    async def demo_system_integration(self):
        """演示系统集成功能"""
        print("\n" + "="*60)
        print("🔗 演示系统集成功能")
        print("="*60)
        
        # 1. 系统状态概览
        print("\n📊 系统状态概览:")
        system_status = self.supervisor.get_system_status()
        
        print(f"  🎛️ Supervisor状态:")
        print(f"    名称: {system_status['supervisor_name']}")
        print(f"    可用智能体: {len(system_status['available_agents'])}")
        print(f"    模型: {system_status['model']}")
        print(f"    状态: {system_status['status']}")
        
        print(f"\n  🔄 工作流引擎状态:")
        workflow_engine = system_status['workflow_engine']
        print(f"    总工作流数: {workflow_engine['total_workflows']}")
        print(f"    活跃执行: {workflow_engine['active_executions']}")
        print(f"    完成执行: {workflow_engine['completed_executions']}")
        print(f"    失败执行: {workflow_engine['failed_executions']}")
        
        print(f"\n  📊 监控状态:")
        monitoring = system_status['monitoring']
        print(f"    活跃执行: {monitoring['active_executions']}")
        print(f"    活跃告警: {monitoring['active_alerts']}")
        print(f"    成功率: {monitoring['success_rate']:.1f}%")
        
        print(f"\n  📚 模板库:")
        print(f"    可用模板: {system_status['templates']} 个")
        
        # 2. 智能体能力展示
        print(f"\n🤖 智能体能力展示:")
        agent_capabilities = self.supervisor.get_agent_capabilities()
        
        for agent_name, capabilities in agent_capabilities.items():
            print(f"  • {capabilities['name']}:")
            print(f"    描述: {capabilities['description']}")
            print(f"    支持任务: {', '.join(capabilities['supported_tasks'])}")
        
        # 3. 工作流模板展示
        print(f"\n📚 工作流模板展示:")
        templates = self.supervisor.get_workflow_templates()
        
        for template in templates:
            print(f"  • {template['title']}:")
            print(f"    描述: {template['description']}")
    
    async def run_complete_demo(self):
        """运行完整演示"""
        print("🎬 开始第三阶段高级工作流引擎完整演示")
        print("=" * 80)
        
        try:
            # 1. 初始化系统
            await self.initialize_system()
            
            # 2. DSL工作流创建演示
            await self.demo_dsl_workflow_creation()
            
            # 3. 模板工作流演示
            await self.demo_template_workflows()
            
            # 4. 工作流执行演示
            await self.demo_workflow_execution()
            
            # 5. 工作流监控演示
            await self.demo_workflow_monitoring()
            
            # 6. 工作流优化演示
            await self.demo_workflow_optimization()
            
            # 7. 动态工作流创建演示
            await self.demo_dynamic_workflow_creation()
            
            # 8. 系统集成演示
            await self.demo_system_integration()
            
            print("\n" + "="*80)
            print("🎉 第三阶段高级工作流引擎演示完成！")
            print("✅ 所有功能模块运行正常")
            print("🚀 高级工作流引擎已准备好投入生产使用")
            print("="*80)
            
        except Exception as e:
            print(f"\n❌ 演示过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            # 清理资源
            if self.supervisor:
                self.supervisor.shutdown()


async def main():
    """主函数"""
    demo = Phase3WorkflowDemo()
    await demo.run_complete_demo()


if __name__ == "__main__":
    asyncio.run(main())