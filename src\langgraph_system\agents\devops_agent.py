#!/usr/bin/env python3
"""
LangGraph多智能体系统 v0.3 - DevOps智能体 (重构)
负责部署自动化、基础设施管理、监控和运维
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from enum import Enum
import logging

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.tools import tool

from .base_v3 import SpecialistAgent, AgentCapability

logger = logging.getLogger(__name__)

# ============================================================================
# DevOps智能体专用数据结构
# ============================================================================

class DeploymentStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    FAILED = "failed"

class InfrastructureStatus(str, Enum):
    RUNNING = "running"
    STOPPED = "stopped"
    MAINTENANCE = "maintenance"
    FAILED = "failed"

class AlertSeverity(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class DeploymentPipeline:
    """部署管道"""
    id: str
    name: str
    stages: List[Dict[str, Any]] = field(default_factory=list)
    environment: str = "staging"
    status: DeploymentStatus = DeploymentStatus.ACTIVE

@dataclass
class Infrastructure:
    """基础设施"""
    id: str
    name: str
    type: str  # e.g., server, database
    provider: str  # e.g., aws, azure, gcp
    status: InfrastructureStatus = InfrastructureStatus.RUNNING

@dataclass
class MonitoringAlert:
    """监控告警"""
    id: str
    name: str
    severity: AlertSeverity
    metric: str
    threshold: float

# ============================================================================
# DevOps智能体实现 (v3)
# ============================================================================

class DevOpsAgent(SpecialistAgent):
    """
    DevOps智能体 v3
    
    核心职责：
    - CI/CD管道设计和管理
    - 基础设施自动化部署
    - 系统监控和告警
    """
    
    def __init__(self, model: BaseLanguageModel, custom_tools: List = None, **kwargs):
        self.deployment_pipelines: Dict[str, DeploymentPipeline] = {}
        self.infrastructure_resources: Dict[str, Infrastructure] = {}
        self.monitoring_alerts: Dict[str, MonitoringAlert] = {}
        
        agent_tools = [
            self.setup_cicd_pipeline,
            self.deploy_infrastructure,
            self.configure_monitoring,
            self.troubleshoot_deployment_issue,
        ]
        if custom_tools:
            agent_tools.extend(custom_tools)

        super().__init__(
            agent_id="devops_001",
            name="DevOps工程师",
            capabilities=[AgentCapability.DEVOPS_DEPLOYMENT],
            model=model,
            tools=agent_tools,
            **kwargs,
        )

    # ========================================================================
    # 智能体工具定义
    # ========================================================================

    @tool
    async def setup_cicd_pipeline(self, project_name: str, platform: str, environments: List[str]) -> str:
        """
        为项目设置CI/CD管道。
        Args:
            project_name (str): 项目名称.
            platform (str): CI/CD平台 (e.g., 'jenkins', 'gitlab_ci').
            environments (List[str]): 需要部署的环境列表 (e.g., ['staging', 'production']).
        Returns:
            str: JSON格式的管道设置结果.
        """
        try:
            logger.info(f"开始为 '{project_name}' 设置CI/CD管道")
            
            pipeline_id = f"pipeline_{project_name.lower().replace(' ', '_')}_{int(time.time())}"
            pipeline = DeploymentPipeline(
                id=pipeline_id,
                name=f"{project_name} Pipeline",
                stages=await self._design_pipeline_stages(platform),
                environment=environments[0] if environments else "staging"
            )
            self.deployment_pipelines[pipeline_id] = pipeline
            
            result = {
                "pipeline_id": pipeline.id,
                "platform": platform,
                "stages_count": len(pipeline.stages),
                "environments": environments,
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"CI/CD设置失败: {e}", exc_info=True)
            return json.dumps({"error": f"CI/CD设置失败: {str(e)}"})

    @tool
    async def deploy_infrastructure(self, infrastructure_spec: str, provider: str) -> str:
        """
        根据规格说明书部署基础设施。
        Args:
            infrastructure_spec (str): JSON格式的基础设施规格说明.
            provider (str): 云服务提供商 (e.g., 'aws', 'gcp').
        Returns:
            str: JSON格式的部署结果.
        """
        try:
            spec_dict = json.loads(infrastructure_spec)
            logger.info(f"开始部署基础设施到 {provider}")
            
            deployed_resources = []
            for resource_type, resource_list in spec_dict.items():
                for item in resource_list:
                    resource_id = f"{resource_type}_{item['name']}_{int(time.time())}"
                    resource = Infrastructure(
                        id=resource_id,
                        name=item['name'],
                        type=resource_type,
                        provider=provider,
                    )
                    self.infrastructure_resources[resource_id] = resource
                    deployed_resources.append({"id": resource.id, "name": resource.name, "type": resource.type})
            
            result = {
                "provider": provider,
                "deployed_resources": deployed_resources,
                "status": "deployment_initiated",
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"基础设施部署失败: {e}", exc_info=True)
            return json.dumps({"error": f"基础设施部署失败: {str(e)}"})

    @tool
    async def configure_monitoring(self, resource_ids: List[str], alert_channels: List[str]) -> str:
        """
        为指定资源配置监控和告警。
        Args:
            resource_ids (List[str]): 需要监控的基础设施资源ID列表.
            alert_channels (List[str]): 告警通知渠道 (e.g., ['email', 'slack']).
        Returns:
            str: JSON格式的配置结果.
        """
        try:
            logger.info("开始配置系统监控")
            
            created_alerts = []
            for resource_id in resource_ids:
                if resource_id in self.infrastructure_resources:
                    resource = self.infrastructure_resources[resource_id]
                    alerts = await self._create_default_alerts_for_resource(resource, alert_channels)
                    for alert in alerts:
                        self.monitoring_alerts[alert.id] = alert
                        created_alerts.append({"id": alert.id, "name": alert.name, "resource_id": resource_id})

            result = {
                "monitored_resources_count": len(resource_ids),
                "created_alerts_count": len(created_alerts),
                "created_alerts": created_alerts,
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"监控配置失败: {e}", exc_info=True)
            return json.dumps({"error": f"监控配置失败: {str(e)}"})

    @tool
    async def troubleshoot_deployment_issue(self, deployment_id: str, error_log: str) -> str:
        """
        排查部署问题。
        Args:
            deployment_id (str): 发生问题的部署ID.
            error_log (str): 相关的错误日志或描述.
        Returns:
            str: JSON格式的排查结果, 包括可能的原因和解决方案.
        """
        try:
            logger.info(f"开始排查部署问题: {deployment_id}")
            
            # (简化的) 调用LLM分析
            analysis = await self._invoke_llm_for_troubleshooting(deployment_id, error_log)
            
            return json.dumps(analysis, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"故障排查失败: {e}", exc_info=True)
            return json.dumps({"error": f"故障排查失败: {str(e)}"})

    # ========================================================================
    # 内部辅助方法
    # ========================================================================

    async def _design_pipeline_stages(self, platform: str) -> List[Dict[str, Any]]:
        """设计管道阶段 (简化)"""
        return [
            {"name": "build", "tool": "docker"},
            {"name": "test", "tool": "pytest"},
            {"name": "security_scan", "tool": "snyk"},
            {"name": "deploy_staging", "tool": platform},
            {"name": "smoke_test", "tool": "curl"},
            {"name": "deploy_production", "tool": platform},
        ]

    async def _create_default_alerts_for_resource(self, resource: Infrastructure, channels: List[str]) -> List[MonitoringAlert]:
        """为资源创建默认告警 (简化)"""
        alerts = []
        if resource.type == "server":
            alert_id = f"alert_cpu_{resource.id}"
            alerts.append(MonitoringAlert(id=alert_id, name=f"{resource.name} CPU High", severity=AlertSeverity.HIGH, metric="cpu_utilization", threshold=90.0))
        elif resource.type == "database":
            alert_id = f"alert_conn_{resource.id}"
            alerts.append(MonitoringAlert(id=alert_id, name=f"{resource.name} High Connections", severity=AlertSeverity.MEDIUM, metric="db_connections", threshold=100.0))
        return alerts

    async def _invoke_llm_for_troubleshooting(self, deployment_id: str, error_log: str) -> Dict[str, Any]:
        """使用LLM进行故障排查 (简化)"""
        prompt = f"""As a DevOps expert, analyze the deployment issue.
Deployment ID: {deployment_id}
Error Log:
{error_log}
Provide a JSON object with 'possible_causes' as a list of strings and 'recommended_solutions' as a list of strings."""
        
        response = await self.model.ainvoke(prompt)
        try:
            return json.loads(response.content)
        except json.JSONDecodeError:
            return {
                "possible_causes": ["LLM response format error."],
                "recommended_solutions": [f"Review LLM raw output: {response.content}"]
            }

__all__ = ['DevOpsAgent', 'DeploymentPipeline', 'Infrastructure', 'MonitoringAlert']