<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能待办事项管理器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .todo-form { margin-bottom: 30px; padding: 20px; background: #f9f9f9; border-radius: 5px; }
        .todo-item { padding: 15px; margin: 10px 0; background: white; border-left: 4px solid #007bff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .todo-item.completed { opacity: 0.6; border-left-color: #28a745; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        input, textarea, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">🎯 智能待办事项管理器</h1>
        
        <div class="todo-form">
            <h3>添加新任务</h3>
            <input type="text" id="todoTitle" placeholder="任务标题" required>
            <textarea id="todoDescription" placeholder="任务描述" rows="3"></textarea>
            <select id="todoPriority">
                <option value="1">低优先级</option>
                <option value="2" selected>中优先级</option>
                <option value="3">高优先级</option>
            </select>
            <input type="datetime-local" id="todoDueDate">
            <button class="btn btn-primary" onclick="addTodo()">添加任务</button>
        </div>
        
        <div id="todoList">
            <!-- 待办事项列表将在这里显示 -->
        </div>
    </div>

    <script>
        let todos = [];
        
        async function loadTodos() {
            try {
                const response = await fetch('/todos');
                todos = await response.json();
                renderTodos();
            } catch (error) {
                console.error('加载待办事项失败:', error);
            }
        }
        
        async function addTodo() {
            const title = document.getElementById('todoTitle').value;
            const description = document.getElementById('todoDescription').value;
            const priority = parseInt(document.getElementById('todoPriority').value);
            const dueDate = document.getElementById('todoDueDate').value;
            
            if (!title.trim()) {
                alert('请输入任务标题');
                return;
            }
            
            const todoData = {
                title: title,
                description: description,
                priority: priority,
                due_date: dueDate || null
            };
            
            try {
                const response = await fetch('/todos', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(todoData)
                });
                
                if (response.ok) {
                    document.getElementById('todoTitle').value = '';
                    document.getElementById('todoDescription').value = '';
                    document.getElementById('todoDueDate').value = '';
                    loadTodos();
                } else {
                    alert('添加任务失败');
                }
            } catch (error) {
                console.error('添加任务失败:', error);
            }
        }
        
        function renderTodos() {
            const todoList = document.getElementById('todoList');
            todoList.innerHTML = '';
            
            todos.sort((a, b) => b.priority - a.priority).forEach(todo => {
                const todoElement = document.createElement('div');
                todoElement.className = `todo-item ${todo.completed ? 'completed' : ''}`;
                todoElement.innerHTML = `
                    <h4>${todo.title}</h4>
                    <p>${todo.description || '无描述'}</p>
                    <small>优先级: ${todo.priority} | 创建时间: ${new Date(todo.created_at).toLocaleString()}</small>
                    ${todo.due_date ? `<br><small>截止时间: ${new Date(todo.due_date).toLocaleString()}</small>` : ''}
                    <div style="margin-top: 10px;">
                        <button class="btn btn-success" onclick="toggleTodo(${todo.id})">
                            ${todo.completed ? '取消完成' : '标记完成'}
                        </button>
                        <button class="btn btn-danger" onclick="deleteTodo(${todo.id})">删除</button>
                    </div>
                `;
                todoList.appendChild(todoElement);
            });
        }
        
        // 页面加载时获取待办事项
        window.onload = loadTodos;
    </script>
</body>
</html>