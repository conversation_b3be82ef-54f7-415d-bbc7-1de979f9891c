#!/usr/bin/env python3
"""
第二阶段智能体系统快速演示
快速验证核心功能和基本协作
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.langgraph_system.agents import (
    ArchitectAgent, ProductManagerAgent, CoderAgent
)
from src.langgraph_system.agents.base_agent import TaskRequest, Priority
from src.langgraph_system.agents.collaboration import AgentCollaborationManager


async def quick_demo():
    """快速演示核心功能"""
    print("🚀 第二阶段智能体系统快速演示")
    print("=" * 50)
    
    try:
        # 1. 创建和初始化智能体
        print("\n📋 初始化智能体...")
        
        architect = ArchitectAgent()
        pm = ProductManagerAgent()
        coder = CoderAgent()
        
        await architect.initialize()
        await pm.initialize()
        await coder.initialize()
        
        print(f"  ✅ {architect.name} 初始化完成")
        print(f"  ✅ {pm.name} 初始化完成")
        print(f"  ✅ {coder.name} 初始化完成")
        
        # 2. 测试单个智能体功能
        print("\n🎯 测试智能体功能...")
        
        # 架构师设计系统
        print("  🏗️ 架构师设计系统...")
        design_task = TaskRequest(
            type="design_system",
            description="设计简单的博客系统",
            parameters={
                "requirements": {
                    "name": "博客系统",
                    "type": "web_application",
                    "expected_users": 1000
                }
            },
            priority=Priority.HIGH
        )
        
        result = await architect.process_task(design_task)
        print(f"    ✅ 系统设计完成: {result.result['system_name']}")
        
        # 产品经理分析需求
        print("  📋 产品经理分析需求...")
        requirements = [
            {
                "id": "req_001",
                "title": "用户注册",
                "description": "用户可以注册账号",
                "type": "functional",
                "business_impact": "high"
            }
        ]
        
        analysis_task = TaskRequest(
            type="analyze_requirements",
            description="分析博客系统需求",
            parameters={"requirements": requirements},
            priority=Priority.HIGH
        )
        
        result = await pm.process_task(analysis_task)
        print(f"    ✅ 需求分析完成: {result.result['total_requirements']} 个需求")
        
        # 编程智能体生成代码
        print("  💻 编程智能体生成代码...")
        code_task = TaskRequest(
            type="generate_code",
            description="生成用户模型",
            parameters={
                "requirements": "创建一个用户模型类",
                "language": "python",
                "code_type": "class"
            },
            priority=Priority.HIGH
        )
        
        result = await coder.process_task(code_task)
        print(f"    ✅ 代码生成完成: {result.result['code_snippet_id']}")
        
        # 3. 测试协作功能
        print("\n🤝 测试智能体协作...")
        
        collaboration = AgentCollaborationManager()
        
        # 注册智能体
        await collaboration.register_agent(architect)
        await collaboration.register_agent(pm)
        await collaboration.register_agent(coder)
        
        print("  ✅ 智能体注册完成")
        
        # 创建简单工作流
        workflow_id = await collaboration.create_workflow(
            "simple_development",
            {
                "project_name": "博客系统开发",
                "analyze_requirements": {
                    "requirements": requirements
                }
            }
        )
        
        print(f"  ✅ 工作流创建完成: {workflow_id}")
        
        # 4. 显示系统状态
        print("\n📊 系统状态:")
        registered_agents = collaboration.get_registered_agents()
        print(f"  • 注册智能体: {len(registered_agents)} 个")
        
        for agent in registered_agents:
            print(f"    - {agent.name} (ID: {agent.agent_id[:8]}...)")
        
        print("\n✅ 快速演示完成！")
        print("🎉 第二阶段智能体系统核心功能正常")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(quick_demo())