#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangGraph CLI启动脚本 - 修复编码问题
"""

import os
import sys
import subprocess
from pathlib import Path


def setup_encoding():
    """设置正确的编码环境"""
    # 设置环境变量
    env_vars = {
        'PYTHONIOENCODING': 'utf-8',
        'PYTHONLEGACYWINDOWSSTDIO': '0',
        'LANG': 'en_US.UTF-8',
        'LC_ALL': 'en_US.UTF-8'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    # Windows特殊处理
    if sys.platform.startswith('win'):
        # 设置控制台代码页为UTF-8
        try:
            subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
        except:
            pass


def run_cli(args):
    """运行CLI命令"""
    # 设置编码
    setup_encoding()
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    main_script = project_root / "src" / "main.py"
    
    # 构建命令
    cmd = [sys.executable, str(main_script)] + args
    
    # 设置环境
    env = os.environ.copy()
    env.update({
        'PYTHONIOENCODING': 'utf-8',
        'PYTHONLEGACYWINDOWSSTDIO': '0'
    })
    
    # 运行命令
    try:
        result = subprocess.run(cmd, env=env, cwd=str(project_root))
        return result.returncode
    except KeyboardInterrupt:
        print("\n用户中断")
        return 1
    except Exception as e:
        print(f"运行失败: {e}")
        return 1


def main():
    """主函数"""
    # 获取命令行参数
    args = sys.argv[1:] if len(sys.argv) > 1 else ['--help']
    
    # 运行CLI
    exit_code = run_cli(args)
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
