#!/usr/bin/env python3
"""
高级工作流引擎
基于现有Supervisor框架的扩展，提供复杂工作流编排能力
"""

import asyncio
import uuid
from typing import Dict, List, Any, Optional, Union, Callable, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)


class WorkflowStatus(Enum):
    """工作流状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NodeType(Enum):
    """节点类型枚举"""
    TASK = "task"
    PARALLEL = "parallel"
    CONDITION = "condition"
    LOOP = "loop"
    MERGE = "merge"
    SPLIT = "split"
    DELAY = "delay"
    HUMAN_APPROVAL = "human_approval"


class ExecutionMode(Enum):
    """执行模式枚举"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    PIPELINE = "pipeline"


@dataclass
class WorkflowNode:
    """工作流节点"""
    id: str
    name: str
    node_type: NodeType
    agent_id: Optional[str] = None
    task_config: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    conditions: Dict[str, Any] = field(default_factory=dict)
    timeout: Optional[int] = None
    retry_count: int = 0
    max_retries: int = 3
    status: WorkflowStatus = WorkflowStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_time: Optional[float] = None


@dataclass
class WorkflowDefinition:
    """工作流定义"""
    id: str
    name: str
    description: str
    version: str = "1.0.0"
    nodes: Dict[str, WorkflowNode] = field(default_factory=dict)
    edges: List[Dict[str, str]] = field(default_factory=list)
    start_nodes: List[str] = field(default_factory=list)
    end_nodes: List[str] = field(default_factory=list)
    global_timeout: Optional[int] = None
    max_parallel_tasks: int = 10
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class WorkflowExecution:
    """工作流执行实例"""
    id: str
    workflow_id: str
    status: WorkflowStatus = WorkflowStatus.PENDING
    input_data: Dict[str, Any] = field(default_factory=dict)
    output_data: Dict[str, Any] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)
    node_executions: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_time: Optional[float] = None
    error: Optional[str] = None
    progress: float = 0.0
    current_nodes: Set[str] = field(default_factory=set)
    completed_nodes: Set[str] = field(default_factory=set)
    failed_nodes: Set[str] = field(default_factory=set)


class ParallelExecutionEngine:
    """并行执行引擎"""
    
    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.active_tasks: Dict[str, asyncio.Task] = {}
    
    async def execute_parallel_nodes(
        self, 
        nodes: List[WorkflowNode], 
        context: Dict[str, Any],
        agent_executor: Callable
    ) -> Dict[str, Dict[str, Any]]:
        """并行执行多个节点"""
        tasks = {}
        results = {}
        
        try:
            # 创建并行任务
            for node in nodes:
                task = asyncio.create_task(
                    self._execute_single_node(node, context, agent_executor)
                )
                tasks[node.id] = task
                self.active_tasks[node.id] = task
            
            # 等待所有任务完成
            for node_id, task in tasks.items():
                try:
                    result = await task
                    results[node_id] = result
                except Exception as e:
                    logger.error(f"并行节点 {node_id} 执行失败: {e}")
                    results[node_id] = {
                        "status": "failed",
                        "error": str(e)
                    }
                finally:
                    if node_id in self.active_tasks:
                        del self.active_tasks[node_id]
            
            return results
            
        except Exception as e:
            logger.error(f"并行执行失败: {e}")
            # 取消所有未完成的任务
            for task in tasks.values():
                if not task.done():
                    task.cancel()
            raise
    
    async def _execute_single_node(
        self, 
        node: WorkflowNode, 
        context: Dict[str, Any],
        agent_executor: Callable
    ) -> Dict[str, Any]:
        """执行单个节点"""
        try:
            node.start_time = datetime.now()
            node.status = WorkflowStatus.RUNNING
            
            # 根据节点类型执行不同逻辑
            if node.node_type == NodeType.TASK:
                result = await agent_executor(node.agent_id, node.task_config, context)
            elif node.node_type == NodeType.DELAY:
                delay_seconds = node.task_config.get("delay_seconds", 1)
                await asyncio.sleep(delay_seconds)
                result = {"status": "completed", "message": f"延迟 {delay_seconds} 秒"}
            else:
                result = {"status": "completed", "message": "节点执行完成"}
            
            node.end_time = datetime.now()
            node.execution_time = (node.end_time - node.start_time).total_seconds()
            node.status = WorkflowStatus.COMPLETED
            node.result = result
            
            return result
            
        except Exception as e:
            node.end_time = datetime.now()
            node.status = WorkflowStatus.FAILED
            node.error = str(e)
            raise
    
    def cancel_all_tasks(self):
        """取消所有活跃任务"""
        for task in self.active_tasks.values():
            if not task.done():
                task.cancel()
        self.active_tasks.clear()
    
    def get_active_task_count(self) -> int:
        """获取活跃任务数量"""
        return len(self.active_tasks)


class ConditionalBranchEngine:
    """条件分支引擎"""
    
    def __init__(self):
        self.condition_evaluators = {
            "equals": self._equals,
            "not_equals": self._not_equals,
            "greater_than": self._greater_than,
            "less_than": self._less_than,
            "contains": self._contains,
            "exists": self._exists,
            "custom": self._custom_condition
        }
    
    def evaluate_condition(
        self, 
        condition: Dict[str, Any], 
        context: Dict[str, Any]
    ) -> bool:
        """评估条件表达式"""
        try:
            condition_type = condition.get("type", "equals")
            evaluator = self.condition_evaluators.get(condition_type)
            
            if not evaluator:
                logger.warning(f"未知的条件类型: {condition_type}")
                return False
            
            return evaluator(condition, context)
            
        except Exception as e:
            logger.error(f"条件评估失败: {e}")
            return False
    
    def get_next_nodes(
        self, 
        node: WorkflowNode, 
        context: Dict[str, Any],
        workflow_def: WorkflowDefinition
    ) -> List[str]:
        """根据条件获取下一个节点"""
        if node.node_type != NodeType.CONDITION:
            return self._get_direct_next_nodes(node.id, workflow_def)
        
        conditions = node.conditions
        for condition_name, condition_config in conditions.items():
            if self.evaluate_condition(condition_config["condition"], context):
                return condition_config.get("next_nodes", [])
        
        # 如果没有条件匹配，返回默认路径
        return conditions.get("default", {}).get("next_nodes", [])
    
    def _get_direct_next_nodes(self, node_id: str, workflow_def: WorkflowDefinition) -> List[str]:
        """获取直接连接的下一个节点"""
        next_nodes = []
        for edge in workflow_def.edges:
            if edge["from"] == node_id:
                next_nodes.append(edge["to"])
        return next_nodes
    
    # 条件评估器
    def _equals(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        field = condition["field"]
        expected = condition["value"]
        actual = self._get_field_value(field, context)
        return actual == expected
    
    def _not_equals(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        return not self._equals(condition, context)
    
    def _greater_than(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        field = condition["field"]
        threshold = condition["value"]
        actual = self._get_field_value(field, context)
        return actual > threshold
    
    def _less_than(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        field = condition["field"]
        threshold = condition["value"]
        actual = self._get_field_value(field, context)
        return actual < threshold
    
    def _contains(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        field = condition["field"]
        substring = condition["value"]
        actual = self._get_field_value(field, context)
        return substring in str(actual)
    
    def _exists(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        field = condition["field"]
        return self._field_exists(field, context)
    
    def _custom_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """自定义条件评估"""
        expression = condition.get("expression", "")
        # 这里可以实现更复杂的表达式评估
        # 为了安全起见，这里只是简单示例
        try:
            # 替换上下文变量
            for key, value in context.items():
                expression = expression.replace(f"${key}", str(value))
            
            # 简单的表达式评估（生产环境需要更安全的实现）
            return eval(expression)
        except:
            return False
    
    def _get_field_value(self, field: str, context: Dict[str, Any]) -> Any:
        """获取字段值，支持嵌套字段"""
        keys = field.split(".")
        value = context
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        return value
    
    def _field_exists(self, field: str, context: Dict[str, Any]) -> bool:
        """检查字段是否存在"""
        return self._get_field_value(field, context) is not None


class AdvancedWorkflowEngine:
    """高级工作流引擎"""
    
    def __init__(self, supervisor_agent=None):
        self.supervisor_agent = supervisor_agent
        self.parallel_engine = ParallelExecutionEngine()
        self.condition_engine = ConditionalBranchEngine()
        
        # 工作流存储
        self.workflow_definitions: Dict[str, WorkflowDefinition] = {}
        self.workflow_executions: Dict[str, WorkflowExecution] = {}
        
        # 执行状态
        self.active_executions: Set[str] = set()
        self.execution_history: List[str] = []
        
        logger.info("高级工作流引擎初始化完成")
    
    def register_workflow(self, workflow_def: WorkflowDefinition) -> str:
        """注册工作流定义"""
        workflow_id = workflow_def.id
        self.workflow_definitions[workflow_id] = workflow_def
        logger.info(f"工作流已注册: {workflow_id}")
        return workflow_id
    
    async def execute_workflow(
        self, 
        workflow_id: str, 
        input_data: Dict[str, Any] = None
    ) -> WorkflowExecution:
        """执行工作流"""
        if workflow_id not in self.workflow_definitions:
            raise ValueError(f"工作流不存在: {workflow_id}")
        
        workflow_def = self.workflow_definitions[workflow_id]
        execution_id = str(uuid.uuid4())
        
        # 创建执行实例
        execution = WorkflowExecution(
            id=execution_id,
            workflow_id=workflow_id,
            input_data=input_data or {},
            context={"input": input_data or {}}
        )
        
        self.workflow_executions[execution_id] = execution
        self.active_executions.add(execution_id)
        
        try:
            execution.start_time = datetime.now()
            execution.status = WorkflowStatus.RUNNING
            
            # 执行工作流
            await self._execute_workflow_nodes(workflow_def, execution)
            
            execution.end_time = datetime.now()
            execution.execution_time = (execution.end_time - execution.start_time).total_seconds()
            execution.status = WorkflowStatus.COMPLETED
            
            logger.info(f"工作流执行完成: {execution_id}")
            
        except Exception as e:
            execution.end_time = datetime.now()
            execution.status = WorkflowStatus.FAILED
            execution.error = str(e)
            logger.error(f"工作流执行失败: {execution_id}, 错误: {e}")
            
        finally:
            self.active_executions.discard(execution_id)
            self.execution_history.append(execution_id)
        
        return execution
    
    async def _execute_workflow_nodes(
        self, 
        workflow_def: WorkflowDefinition, 
        execution: WorkflowExecution
    ):
        """执行工作流节点"""
        current_nodes = set(workflow_def.start_nodes)
        execution.current_nodes = current_nodes
        
        while current_nodes and execution.status == WorkflowStatus.RUNNING:
            # 检查依赖关系
            ready_nodes = self._get_ready_nodes(current_nodes, execution, workflow_def)
            
            if not ready_nodes:
                break
            
            # 分类节点：并行执行 vs 顺序执行
            parallel_nodes = []
            sequential_nodes = []
            
            for node_id in ready_nodes:
                node = workflow_def.nodes[node_id]
                if node.node_type in [NodeType.TASK, NodeType.DELAY]:
                    parallel_nodes.append(node)
                else:
                    sequential_nodes.append(node)
            
            # 并行执行任务节点
            if parallel_nodes:
                results = await self.parallel_engine.execute_parallel_nodes(
                    parallel_nodes, 
                    execution.context,
                    self._execute_agent_task
                )
                
                # 更新执行结果
                for node_id, result in results.items():
                    execution.node_executions[node_id] = result
                    execution.completed_nodes.add(node_id)
                    if result.get("status") == "failed":
                        execution.failed_nodes.add(node_id)
            
            # 顺序执行控制节点
            for node in sequential_nodes:
                try:
                    result = await self._execute_control_node(node, execution, workflow_def)
                    execution.node_executions[node.id] = result
                    execution.completed_nodes.add(node.id)
                except Exception as e:
                    execution.failed_nodes.add(node.id)
                    execution.node_executions[node.id] = {
                        "status": "failed",
                        "error": str(e)
                    }
            
            # 更新进度
            execution.progress = len(execution.completed_nodes) / len(workflow_def.nodes)
            
            # 获取下一批节点
            next_nodes = set()
            for node_id in ready_nodes:
                if node_id in execution.completed_nodes:
                    node = workflow_def.nodes[node_id]
                    next_node_ids = self.condition_engine.get_next_nodes(
                        node, execution.context, workflow_def
                    )
                    next_nodes.update(next_node_ids)
            
            # 移除已完成的节点，添加新节点
            current_nodes = (current_nodes - ready_nodes) | next_nodes
            execution.current_nodes = current_nodes
            
            # 检查是否有失败的关键节点
            if execution.failed_nodes:
                critical_failed = any(
                    workflow_def.nodes[node_id].task_config.get("critical", False)
                    for node_id in execution.failed_nodes
                )
                if critical_failed:
                    raise Exception("关键节点执行失败")
    
    def _get_ready_nodes(
        self, 
        current_nodes: Set[str], 
        execution: WorkflowExecution,
        workflow_def: WorkflowDefinition
    ) -> Set[str]:
        """获取准备执行的节点"""
        ready_nodes = set()
        
        for node_id in current_nodes:
            node = workflow_def.nodes[node_id]
            
            # 检查依赖关系
            dependencies_met = all(
                dep_id in execution.completed_nodes 
                for dep_id in node.dependencies
            )
            
            if dependencies_met:
                ready_nodes.add(node_id)
        
        return ready_nodes
    
    async def _execute_agent_task(
        self, 
        agent_id: str, 
        task_config: Dict[str, Any], 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行智能体任务"""
        if not self.supervisor_agent:
            return {
                "status": "completed",
                "result": "模拟执行结果",
                "message": f"智能体 {agent_id} 执行任务"
            }
        
        try:
            # 构建任务请求
            task_request = {
                "id": str(uuid.uuid4()),
                "agent_id": agent_id,
                "type": task_config.get("type", "general"),
                "description": task_config.get("description", ""),
                "parameters": task_config.get("parameters", {}),
                "context": context
            }
            
            # 通过supervisor执行任务
            result = await self.supervisor_agent.process_task(task_request)
            return result
            
        except Exception as e:
            logger.error(f"智能体任务执行失败: {e}")
            return {
                "status": "failed",
                "error": str(e)
            }
    
    async def _execute_control_node(
        self, 
        node: WorkflowNode, 
        execution: WorkflowExecution,
        workflow_def: WorkflowDefinition
    ) -> Dict[str, Any]:
        """执行控制节点"""
        if node.node_type == NodeType.CONDITION:
            # 条件节点只是评估条件，不执行具体任务
            return {
                "status": "completed",
                "message": "条件节点评估完成"
            }
        elif node.node_type == NodeType.MERGE:
            # 合并节点等待所有输入完成
            return {
                "status": "completed",
                "message": "合并节点完成"
            }
        elif node.node_type == NodeType.SPLIT:
            # 分割节点创建多个并行路径
            return {
                "status": "completed",
                "message": "分割节点完成"
            }
        elif node.node_type == NodeType.HUMAN_APPROVAL:
            # 人工审批节点（这里简化处理）
            return {
                "status": "completed",
                "message": "人工审批通过",
                "approved": True
            }
        else:
            return {
                "status": "completed",
                "message": f"控制节点 {node.node_type.value} 执行完成"
            }
    
    def pause_workflow(self, execution_id: str) -> bool:
        """暂停工作流执行"""
        if execution_id in self.workflow_executions:
            execution = self.workflow_executions[execution_id]
            if execution.status == WorkflowStatus.RUNNING:
                execution.status = WorkflowStatus.PAUSED
                return True
        return False
    
    def resume_workflow(self, execution_id: str) -> bool:
        """恢复工作流执行"""
        if execution_id in self.workflow_executions:
            execution = self.workflow_executions[execution_id]
            if execution.status == WorkflowStatus.PAUSED:
                execution.status = WorkflowStatus.RUNNING
                return True
        return False
    
    def cancel_workflow(self, execution_id: str) -> bool:
        """取消工作流执行"""
        if execution_id in self.workflow_executions:
            execution = self.workflow_executions[execution_id]
            execution.status = WorkflowStatus.CANCELLED
            self.active_executions.discard(execution_id)
            return True
        return False
    
    def get_workflow_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流执行状态"""
        if execution_id not in self.workflow_executions:
            return None
        
        execution = self.workflow_executions[execution_id]
        return {
            "execution_id": execution_id,
            "workflow_id": execution.workflow_id,
            "status": execution.status.value,
            "progress": execution.progress,
            "start_time": execution.start_time.isoformat() if execution.start_time else None,
            "end_time": execution.end_time.isoformat() if execution.end_time else None,
            "execution_time": execution.execution_time,
            "current_nodes": list(execution.current_nodes),
            "completed_nodes": list(execution.completed_nodes),
            "failed_nodes": list(execution.failed_nodes),
            "error": execution.error
        }
    
    def get_engine_statistics(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return {
            "total_workflows": len(self.workflow_definitions),
            "total_executions": len(self.workflow_executions),
            "active_executions": len(self.active_executions),
            "completed_executions": len([
                e for e in self.workflow_executions.values() 
                if e.status == WorkflowStatus.COMPLETED
            ]),
            "failed_executions": len([
                e for e in self.workflow_executions.values() 
                if e.status == WorkflowStatus.FAILED
            ]),
            "parallel_active_tasks": self.parallel_engine.get_active_task_count()
        }