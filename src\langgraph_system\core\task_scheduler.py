"""
异步任务调度器 - v0.3增强版
支持优先级队列、任务依赖、重试机制和负载均衡
"""

import asyncio
import logging
import time
import heapq
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Set, Union, Type
from enum import Enum
from dataclasses import dataclass, field
import uuid
import json

from pydantic import BaseModel, Field

from .exceptions import TaskSchedulerError, TaskNotFoundError, TaskDependencyError

logger = logging.getLogger(__name__)


class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"
    WAITING_DEPENDENCY = "waiting_dependency"


class TaskPriority(int, Enum):
    """任务优先级"""
    CRITICAL = 1
    HIGH = 2
    NORMAL = 3
    LOW = 4
    BACKGROUND = 5


class RetryStrategy(str, Enum):
    """重试策略"""
    NONE = "none"
    FIXED_DELAY = "fixed_delay"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    CUSTOM = "custom"


@dataclass
class RetryConfig:
    """重试配置"""
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    max_retries: int = 3
    initial_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True
    retry_on_exceptions: List[Type[Exception]] = field(default_factory=list)


@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_time: Optional[float] = None
    retry_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


class Task(BaseModel):
    """调度任务"""
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    func: Any  # 任务函数（不能直接序列化）
    args: List[Any] = Field(default_factory=list)
    kwargs: Dict[str, Any] = Field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    
    # 调度配置
    scheduled_time: Optional[datetime] = None
    timeout: Optional[float] = None
    retry_config: RetryConfig = Field(default_factory=RetryConfig)
    
    # 依赖关系
    dependencies: List[str] = Field(default_factory=list)
    dependents: List[str] = Field(default_factory=list)
    
    # 状态信息
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 执行信息
    worker_id: Optional[str] = None
    retry_count: int = 0
    last_error: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict)
    tags: List[str] = Field(default_factory=list)
    
    class Config:
        arbitrary_types_allowed = True
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.created_at < other.created_at
    
    @property
    def is_ready(self) -> bool:
        """是否准备执行"""
        if self.status != TaskStatus.QUEUED:
            return False
        
        # 检查调度时间
        if self.scheduled_time and datetime.now() < self.scheduled_time:
            return False
        
        return True
    
    def can_retry(self) -> bool:
        """是否可以重试"""
        return (self.retry_config.strategy != RetryStrategy.NONE and 
                self.retry_count < self.retry_config.max_retries)
    
    def calculate_retry_delay(self) -> float:
        """计算重试延迟"""
        if self.retry_config.strategy == RetryStrategy.FIXED_DELAY:
            delay = self.retry_config.initial_delay
        
        elif self.retry_config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.retry_config.initial_delay * (
                self.retry_config.backoff_factor ** self.retry_count
            )
        
        elif self.retry_config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.retry_config.initial_delay * (1 + self.retry_count)
        
        else:
            delay = self.retry_config.initial_delay
        
        # 限制最大延迟
        delay = min(delay, self.retry_config.max_delay)
        
        # 添加抖动
        if self.retry_config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        return delay


class TaskWorker:
    """任务工作器"""
    
    def __init__(self, worker_id: str, max_concurrent_tasks: int = 5):
        self.worker_id = worker_id
        self.max_concurrent_tasks = max_concurrent_tasks
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.completed_tasks: int = 0
        self.failed_tasks: int = 0
        self.total_execution_time: float = 0.0
        self.created_at = datetime.now()
        self.last_active = datetime.now()
        self._shutdown = False
    
    @property
    def is_available(self) -> bool:
        """是否可用"""
        return (not self._shutdown and 
                len(self.running_tasks) < self.max_concurrent_tasks)
    
    @property
    def load_factor(self) -> float:
        """负载因子"""
        return len(self.running_tasks) / self.max_concurrent_tasks
    
    @property
    def average_execution_time(self) -> float:
        """平均执行时间"""
        total_tasks = self.completed_tasks + self.failed_tasks
        if total_tasks == 0:
            return 0.0
        return self.total_execution_time / total_tasks
    
    async def execute_task(self, task: Task) -> TaskResult:
        """执行任务"""
        if not self.is_available:
            raise TaskSchedulerError(f"Worker {self.worker_id} is not available")
        
        task_id = task.task_id
        start_time = datetime.now()
        
        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_at = start_time
            task.worker_id = self.worker_id
            
            # 创建执行任务
            if asyncio.iscoroutinefunction(task.func):
                execution_task = asyncio.create_task(
                    task.func(*task.args, **task.kwargs)
                )
            else:
                execution_task = asyncio.create_task(
                    asyncio.to_thread(task.func, *task.args, **task.kwargs)
                )
            
            self.running_tasks[task_id] = execution_task
            
            # 执行任务（带超时）
            if task.timeout:
                result = await asyncio.wait_for(execution_task, timeout=task.timeout)
            else:
                result = await execution_task
            
            # 任务成功完成
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            task.status = TaskStatus.COMPLETED
            task.completed_at = end_time
            
            self.completed_tasks += 1
            self.total_execution_time += execution_time
            self.last_active = end_time
            
            return TaskResult(
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                result=result,
                start_time=start_time,
                end_time=end_time,
                execution_time=execution_time,
                retry_count=task.retry_count
            )
            
        except asyncio.TimeoutError:
            # 任务超时
            task.status = TaskStatus.FAILED
            task.last_error = f"Task timeout after {task.timeout}s"
            
            self.failed_tasks += 1
            
            return TaskResult(
                task_id=task_id,
                status=TaskStatus.FAILED,
                error=asyncio.TimeoutError(f"Task timeout after {task.timeout}s"),
                start_time=start_time,
                end_time=datetime.now(),
                retry_count=task.retry_count
            )
            
        except Exception as e:
            # 任务执行失败
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            task.status = TaskStatus.FAILED
            task.last_error = str(e)
            
            self.failed_tasks += 1
            self.total_execution_time += execution_time
            
            return TaskResult(
                task_id=task_id,
                status=TaskStatus.FAILED,
                error=e,
                start_time=start_time,
                end_time=end_time,
                execution_time=execution_time,
                retry_count=task.retry_count
            )
            
        finally:
            # 清理
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.running_tasks:
            execution_task = self.running_tasks[task_id]
            execution_task.cancel()
            
            try:
                await execution_task
            except asyncio.CancelledError:
                pass
            
            del self.running_tasks[task_id]
            return True
        
        return False
    
    async def shutdown(self):
        """关闭工作器"""
        self._shutdown = True
        
        # 取消所有运行中的任务
        for task_id in list(self.running_tasks.keys()):
            await self.cancel_task(task_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取工作器统计信息"""
        return {
            "worker_id": self.worker_id,
            "running_tasks": len(self.running_tasks),
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "load_factor": self.load_factor,
            "average_execution_time": self.average_execution_time,
            "created_at": self.created_at.isoformat(),
            "last_active": self.last_active.isoformat(),
            "is_available": self.is_available
        }


class AsyncTaskScheduler:
    """异步任务调度器"""
    
    def __init__(self, max_workers: int = 10, max_queue_size: int = 1000):
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        
        # 任务存储
        self.tasks: Dict[str, Task] = {}
        self.task_queue: List[Task] = []  # 优先级队列
        self.dependency_graph: Dict[str, Set[str]] = {}  # 依赖图
        
        # 工作器管理
        self.workers: Dict[str, TaskWorker] = {}
        self.worker_assignment: Dict[str, str] = {}  # task_id -> worker_id
        
        # 调度器状态
        self._running = False
        self._scheduler_task: Optional[asyncio.Task] = None
        self._dependency_resolver_task: Optional[asyncio.Task] = None
        
        # 统计信息
        self.total_tasks_scheduled = 0
        self.total_tasks_completed = 0
        self.total_tasks_failed = 0
        
        # 事件回调
        self.task_callbacks: Dict[str, List[Callable]] = {
            "task_started": [],
            "task_completed": [],
            "task_failed": [],
            "task_retrying": []
        }
        
        # 锁
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """初始化调度器"""
        if self._running:
            return
        
        # 创建工作器
        for i in range(self.max_workers):
            worker_id = f"worker_{i}"
            worker = TaskWorker(worker_id)
            self.workers[worker_id] = worker
        
        # 启动调度器
        self._running = True
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        self._dependency_resolver_task = asyncio.create_task(self._dependency_resolver_loop())
        
        logger.info(f"AsyncTaskScheduler initialized with {self.max_workers} workers")
    
    async def shutdown(self):
        """关闭调度器"""
        if not self._running:
            return
        
        self._running = False
        
        # 停止调度器循环
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        if self._dependency_resolver_task:
            self._dependency_resolver_task.cancel()
            try:
                await self._dependency_resolver_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有工作器
        for worker in self.workers.values():
            await worker.shutdown()
        
        logger.info("AsyncTaskScheduler shutdown completed")
    
    async def schedule_task(self, name: str, func: Callable, 
                          args: List[Any] = None,
                          kwargs: Dict[str, Any] = None,
                          priority: TaskPriority = TaskPriority.NORMAL,
                          scheduled_time: Optional[datetime] = None,
                          timeout: Optional[float] = None,
                          retry_config: Optional[RetryConfig] = None,
                          dependencies: List[str] = None,
                          metadata: Dict[str, Any] = None,
                          tags: List[str] = None) -> str:
        """调度任务"""
        if len(self.task_queue) >= self.max_queue_size:
            raise TaskSchedulerError("Task queue is full")
        
        # 创建任务
        task = Task(
            name=name,
            func=func,
            args=args or [],
            kwargs=kwargs or {},
            priority=priority,
            scheduled_time=scheduled_time,
            timeout=timeout,
            retry_config=retry_config or RetryConfig(),
            dependencies=dependencies or [],
            metadata=metadata or {},
            tags=tags or []
        )
        
        async with self._lock:
            # 存储任务
            self.tasks[task.task_id] = task
            
            # 处理依赖关系
            if task.dependencies:
                await self._add_dependencies(task)
                task.status = TaskStatus.WAITING_DEPENDENCY
            else:
                # 添加到队列
                task.status = TaskStatus.QUEUED
                heapq.heappush(self.task_queue, task)
            
            self.total_tasks_scheduled += 1
        
        logger.debug(f"Task scheduled: {task.task_id} ({name})")
        return task.task_id
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        async with self._lock:
            if task_id not in self.tasks:
                return False
            
            task = self.tasks[task_id]
            
            if task.status == TaskStatus.RUNNING:
                # 取消正在运行的任务
                worker_id = self.worker_assignment.get(task_id)
                if worker_id and worker_id in self.workers:
                    await self.workers[worker_id].cancel_task(task_id)
            
            elif task.status in [TaskStatus.QUEUED, TaskStatus.WAITING_DEPENDENCY]:
                # 从队列中移除
                if task in self.task_queue:
                    self.task_queue.remove(task)
                    heapq.heapify(self.task_queue)
            
            task.status = TaskStatus.CANCELLED
            
            # 处理依赖任务
            await self._handle_cancelled_dependencies(task_id)
            
            logger.debug(f"Task cancelled: {task_id}")
            return True
    
    async def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        if task_id in self.tasks:
            return self.tasks[task_id].status
        return None
    
    async def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取任务结果"""
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        
        if task.status == TaskStatus.COMPLETED:
            return TaskResult(
                task_id=task_id,
                status=task.status,
                start_time=task.started_at,
                end_time=task.completed_at,
                retry_count=task.retry_count
            )
        elif task.status == TaskStatus.FAILED:
            return TaskResult(
                task_id=task_id,
                status=task.status,
                error=Exception(task.last_error) if task.last_error else None,
                start_time=task.started_at,
                retry_count=task.retry_count
            )
        
        return TaskResult(task_id=task_id, status=task.status)
    
    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> TaskResult:
        """等待任务完成"""
        start_time = time.time()
        
        while True:
            if task_id not in self.tasks:
                raise TaskNotFoundError(f"Task not found: {task_id}")
            
            task = self.tasks[task_id]
            
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                return await self.get_task_result(task_id)
            
            if timeout and (time.time() - start_time) > timeout:
                raise asyncio.TimeoutError(f"Task {task_id} did not complete within {timeout}s")
            
            await asyncio.sleep(0.1)
    
    def register_callback(self, event_type: str, callback: Callable):
        """注册事件回调"""
        if event_type in self.task_callbacks:
            self.task_callbacks[event_type].append(callback)
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        while self._running:
            try:
                await asyncio.sleep(0.1)  # 100ms调度间隔
                
                if not self.task_queue:
                    continue
                
                # 查找可用工作器
                available_workers = [w for w in self.workers.values() if w.is_available]
                if not available_workers:
                    continue
                
                # 获取准备执行的任务
                ready_tasks = []
                temp_queue = []
                
                while self.task_queue:
                    task = heapq.heappop(self.task_queue)
                    if task.is_ready:
                        ready_tasks.append(task)
                        if len(ready_tasks) >= len(available_workers):
                            break
                    else:
                        temp_queue.append(task)
                
                # 将未准备好的任务放回队列
                for task in temp_queue:
                    heapq.heappush(self.task_queue, task)
                
                # 分配任务给工作器
                for task, worker in zip(ready_tasks, available_workers):
                    asyncio.create_task(self._execute_task(task, worker))
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Scheduler loop error: {e}")
    
    async def _execute_task(self, task: Task, worker: TaskWorker):
        """执行任务"""
        try:
            # 记录工作器分配
            self.worker_assignment[task.task_id] = worker.worker_id
            
            # 触发开始回调
            await self._trigger_callbacks("task_started", task)
            
            # 执行任务
            result = await worker.execute_task(task)
            
            if result.status == TaskStatus.COMPLETED:
                # 任务成功完成
                self.total_tasks_completed += 1
                await self._trigger_callbacks("task_completed", task, result)
                
                # 处理依赖任务
                await self._resolve_dependencies(task.task_id)
                
            elif result.status == TaskStatus.FAILED:
                # 任务失败，检查是否需要重试
                if task.can_retry():
                    await self._schedule_retry(task)
                else:
                    self.total_tasks_failed += 1
                    await self._trigger_callbacks("task_failed", task, result)
                    
                    # 处理依赖任务失败
                    await self._handle_failed_dependencies(task.task_id)
            
        except Exception as e:
            logger.error(f"Task execution error: {e}")
            task.status = TaskStatus.FAILED
            task.last_error = str(e)
            self.total_tasks_failed += 1
        
        finally:
            # 清理工作器分配
            if task.task_id in self.worker_assignment:
                del self.worker_assignment[task.task_id]
    
    async def _schedule_retry(self, task: Task):
        """调度重试"""
        task.retry_count += 1
        task.status = TaskStatus.RETRYING
        
        # 计算重试延迟
        delay = task.calculate_retry_delay()
        task.scheduled_time = datetime.now() + timedelta(seconds=delay)
        
        # 重新加入队列
        async with self._lock:
            task.status = TaskStatus.QUEUED
            heapq.heappush(self.task_queue, task)
        
        await self._trigger_callbacks("task_retrying", task)
        logger.debug(f"Task scheduled for retry: {task.task_id} (attempt {task.retry_count})")
    
    async def _add_dependencies(self, task: Task):
        """添加依赖关系"""
        task_id = task.task_id
        
        for dep_id in task.dependencies:
            if dep_id not in self.tasks:
                raise TaskDependencyError(f"Dependency task not found: {dep_id}")
            
            # 添加到依赖图
            if dep_id not in self.dependency_graph:
                self.dependency_graph[dep_id] = set()
            self.dependency_graph[dep_id].add(task_id)
            
            # 更新依赖任务的dependents列表
            dep_task = self.tasks[dep_id]
            if task_id not in dep_task.dependents:
                dep_task.dependents.append(task_id)
    
    async def _resolve_dependencies(self, completed_task_id: str):
        """解析依赖关系"""
        if completed_task_id not in self.dependency_graph:
            return
        
        dependent_task_ids = self.dependency_graph[completed_task_id]
        
        for task_id in dependent_task_ids:
            if task_id not in self.tasks:
                continue
            
            task = self.tasks[task_id]
            
            # 检查所有依赖是否都已完成
            all_deps_completed = True
            for dep_id in task.dependencies:
                if dep_id in self.tasks:
                    dep_task = self.tasks[dep_id]
                    if dep_task.status != TaskStatus.COMPLETED:
                        all_deps_completed = False
                        break
            
            # 如果所有依赖都完成，将任务加入队列
            if all_deps_completed and task.status == TaskStatus.WAITING_DEPENDENCY:
                async with self._lock:
                    task.status = TaskStatus.QUEUED
                    heapq.heappush(self.task_queue, task)
        
        # 清理依赖图
        del self.dependency_graph[completed_task_id]
    
    async def _handle_failed_dependencies(self, failed_task_id: str):
        """处理失败的依赖"""
        if failed_task_id not in self.dependency_graph:
            return
        
        dependent_task_ids = self.dependency_graph[failed_task_id]
        
        for task_id in dependent_task_ids:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                task.status = TaskStatus.FAILED
                task.last_error = f"Dependency task failed: {failed_task_id}"
                
                # 递归处理依赖的依赖
                await self._handle_failed_dependencies(task_id)
        
        # 清理依赖图
        del self.dependency_graph[failed_task_id]
    
    async def _handle_cancelled_dependencies(self, cancelled_task_id: str):
        """处理取消的依赖"""
        if cancelled_task_id not in self.dependency_graph:
            return
        
        dependent_task_ids = self.dependency_graph[cancelled_task_id]
        
        for task_id in dependent_task_ids:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                task.status = TaskStatus.CANCELLED
                
                # 递归处理依赖的依赖
                await self._handle_cancelled_dependencies(task_id)
        
        # 清理依赖图
        del self.dependency_graph[cancelled_task_id]
    
    async def _dependency_resolver_loop(self):
        """依赖解析循环"""
        while self._running:
            try:
                await asyncio.sleep(1)  # 1秒检查间隔
                
                # 检查等待依赖的任务
                waiting_tasks = [
                    task for task in self.tasks.values()
                    if task.status == TaskStatus.WAITING_DEPENDENCY
                ]
                
                for task in waiting_tasks:
                    # 检查依赖是否满足
                    all_deps_completed = True
                    for dep_id in task.dependencies:
                        if dep_id in self.tasks:
                            dep_task = self.tasks[dep_id]
                            if dep_task.status != TaskStatus.COMPLETED:
                                all_deps_completed = False
                                break
                        else:
                            # 依赖任务不存在
                            all_deps_completed = False
                            break
                    
                    if all_deps_completed:
                        async with self._lock:
                            task.status = TaskStatus.QUEUED
                            heapq.heappush(self.task_queue, task)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Dependency resolver error: {e}")
    
    async def _trigger_callbacks(self, event_type: str, task: Task, result: TaskResult = None):
        """触发事件回调"""
        callbacks = self.task_callbacks.get(event_type, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    if result:
                        await callback(task, result)
                    else:
                        await callback(task)
                else:
                    if result:
                        callback(task, result)
                    else:
                        callback(task)
            except Exception as e:
                logger.error(f"Callback error for {event_type}: {e}")
    
    def get_scheduler_stats(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        queue_size = len(self.task_queue)
        running_tasks = sum(1 for task in self.tasks.values() if task.status == TaskStatus.RUNNING)
        waiting_tasks = sum(1 for task in self.tasks.values() if task.status == TaskStatus.WAITING_DEPENDENCY)
        
        worker_stats = [worker.get_stats() for worker in self.workers.values()]
        
        return {
            "total_tasks_scheduled": self.total_tasks_scheduled,
            "total_tasks_completed": self.total_tasks_completed,
            "total_tasks_failed": self.total_tasks_failed,
            "queue_size": queue_size,
            "running_tasks": running_tasks,
            "waiting_tasks": waiting_tasks,
            "total_workers": len(self.workers),
            "available_workers": sum(1 for w in self.workers.values() if w.is_available),
            "worker_stats": worker_stats,
            "is_running": self._running
        }


# 全局任务调度器实例
task_scheduler = AsyncTaskScheduler()


async def get_task_scheduler() -> AsyncTaskScheduler:
    """获取任务调度器实例"""
    if not task_scheduler._running:
        await task_scheduler.initialize()
    return task_scheduler