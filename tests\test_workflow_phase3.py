#!/usr/bin/env python3
"""
第三阶段高级工作流引擎测试
测试工作流引擎、DSL、监控和增强版Supervisor的功能
"""

import asyncio
import unittest
import json
import yaml
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

# 导入工作流组件
from src.langgraph_system.workflow import (
    AdvancedWorkflowEngine, WorkflowDefinition, WorkflowNode, 
    NodeType, WorkflowStatus, ParallelExecutionEngine,
    ConditionalBranchEngine, WorkflowDSLParser, WorkflowTemplateLibrary,
    WorkflowMonitor, MonitorEventType
)
from src.langgraph_system.agents.enhanced_supervisor_agent import EnhancedSupervisorAgent


class TestAdvancedWorkflowEngine(unittest.TestCase):
    """测试高级工作流引擎"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.engine = AdvancedWorkflowEngine()
    
    def test_workflow_registration(self):
        """测试工作流注册"""
        workflow_def = WorkflowDefinition(
            id="test_workflow",
            name="测试工作流",
            description="用于测试的简单工作流"
        )
        
        # 添加节点
        node = WorkflowNode(
            id="test_node",
            name="测试节点",
            node_type=NodeType.TASK,
            agent_id="test_agent"
        )
        workflow_def.nodes["test_node"] = node
        workflow_def.start_nodes = ["test_node"]
        workflow_def.end_nodes = ["test_node"]
        
        # 注册工作流
        workflow_id = self.engine.register_workflow(workflow_def)
        
        self.assertEqual(workflow_id, "test_workflow")
        self.assertIn("test_workflow", self.engine.workflow_definitions)
    
    def test_simple_workflow_execution(self):
        """测试简单工作流执行"""
        async def run_test():
            # 创建简单工作流
            workflow_def = WorkflowDefinition(
                id="simple_workflow",
                name="简单工作流",
                description="单节点工作流"
            )
            
            node = WorkflowNode(
                id="single_task",
                name="单一任务",
                node_type=NodeType.TASK,
                agent_id="test_agent",
                task_config={"type": "test", "description": "测试任务"}
            )
            
            workflow_def.nodes["single_task"] = node
            workflow_def.start_nodes = ["single_task"]
            workflow_def.end_nodes = ["single_task"]
            
            # 注册并执行
            workflow_id = self.engine.register_workflow(workflow_def)
            execution = await self.engine.execute_workflow(workflow_id, {"test": "data"})
            
            self.assertEqual(execution.workflow_id, workflow_id)
            self.assertIn(execution.status, [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED])
        
        asyncio.run(run_test())
    
    def test_parallel_workflow_execution(self):
        """测试并行工作流执行"""
        async def run_test():
            # 创建并行工作流
            workflow_def = WorkflowDefinition(
                id="parallel_workflow",
                name="并行工作流",
                description="多节点并行执行"
            )
            
            # 创建三个并行任务
            for i in range(3):
                node = WorkflowNode(
                    id=f"parallel_task_{i}",
                    name=f"并行任务{i}",
                    node_type=NodeType.TASK,
                    agent_id="test_agent",
                    task_config={"type": "parallel_test", "task_id": i}
                )
                workflow_def.nodes[f"parallel_task_{i}"] = node
            
            workflow_def.start_nodes = ["parallel_task_0", "parallel_task_1", "parallel_task_2"]
            workflow_def.end_nodes = ["parallel_task_0", "parallel_task_1", "parallel_task_2"]
            
            # 注册并执行
            workflow_id = self.engine.register_workflow(workflow_def)
            execution = await self.engine.execute_workflow(workflow_id)
            
            self.assertEqual(len(execution.completed_nodes), 3)
        
        asyncio.run(run_test())


class TestParallelExecutionEngine(unittest.TestCase):
    """测试并行执行引擎"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.parallel_engine = ParallelExecutionEngine(max_workers=5)
    
    def test_parallel_node_execution(self):
        """测试并行节点执行"""
        async def run_test():
            # 创建测试节点
            nodes = []
            for i in range(3):
                node = WorkflowNode(
                    id=f"node_{i}",
                    name=f"节点{i}",
                    node_type=NodeType.TASK,
                    agent_id="test_agent"
                )
                nodes.append(node)
            
            # 模拟智能体执行器
            async def mock_agent_executor(agent_id, task_config, context):
                await asyncio.sleep(0.1)  # 模拟执行时间
                return {
                    "status": "completed",
                    "result": f"执行结果 - {agent_id}",
                    "agent_id": agent_id
                }
            
            # 执行并行任务
            results = await self.parallel_engine.execute_parallel_nodes(
                nodes, {"test": "context"}, mock_agent_executor
            )
            
            self.assertEqual(len(results), 3)
            for node_id, result in results.items():
                self.assertEqual(result["status"], "completed")
        
        asyncio.run(run_test())
    
    def test_parallel_execution_with_failure(self):
        """测试并行执行中的失败处理"""
        async def run_test():
            nodes = []
            for i in range(3):
                node = WorkflowNode(
                    id=f"node_{i}",
                    name=f"节点{i}",
                    node_type=NodeType.TASK,
                    agent_id="test_agent"
                )
                nodes.append(node)
            
            # 模拟部分失败的执行器
            async def mock_failing_executor(agent_id, task_config, context):
                if "node_1" in task_config.get("node_id", ""):
                    raise Exception("模拟执行失败")
                return {"status": "completed", "result": "成功"}
            
            # 为节点添加配置
            for i, node in enumerate(nodes):
                node.task_config["node_id"] = f"node_{i}"
            
            results = await self.parallel_engine.execute_parallel_nodes(
                nodes, {}, mock_failing_executor
            )
            
            # 检查结果
            success_count = sum(1 for r in results.values() if r.get("status") == "completed")
            failure_count = sum(1 for r in results.values() if r.get("status") == "failed")
            
            self.assertEqual(success_count, 2)
            self.assertEqual(failure_count, 1)
        
        asyncio.run(run_test())


class TestConditionalBranchEngine(unittest.TestCase):
    """测试条件分支引擎"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.condition_engine = ConditionalBranchEngine()
    
    def test_simple_condition_evaluation(self):
        """测试简单条件评估"""
        # 测试相等条件
        condition = {
            "type": "equals",
            "field": "status",
            "value": "success"
        }
        context = {"status": "success"}
        
        result = self.condition_engine.evaluate_condition(condition, context)
        self.assertTrue(result)
        
        # 测试不相等情况
        context = {"status": "failed"}
        result = self.condition_engine.evaluate_condition(condition, context)
        self.assertFalse(result)
    
    def test_numeric_conditions(self):
        """测试数值条件"""
        # 大于条件
        condition = {
            "type": "greater_than",
            "field": "score",
            "value": 80
        }
        context = {"score": 85}
        
        result = self.condition_engine.evaluate_condition(condition, context)
        self.assertTrue(result)
        
        # 小于条件
        condition = {
            "type": "less_than",
            "field": "score",
            "value": 80
        }
        context = {"score": 75}
        
        result = self.condition_engine.evaluate_condition(condition, context)
        self.assertTrue(result)
    
    def test_nested_field_access(self):
        """测试嵌套字段访问"""
        condition = {
            "type": "equals",
            "field": "result.status",
            "value": "completed"
        }
        context = {
            "result": {
                "status": "completed",
                "data": "test"
            }
        }
        
        result = self.condition_engine.evaluate_condition(condition, context)
        self.assertTrue(result)
    
    def test_next_nodes_determination(self):
        """测试下一节点确定"""
        # 创建条件节点
        node = WorkflowNode(
            id="condition_node",
            name="条件节点",
            node_type=NodeType.CONDITION,
            conditions={
                "success_path": {
                    "condition": {
                        "type": "equals",
                        "field": "status",
                        "value": "success"
                    },
                    "next_nodes": ["success_node"]
                },
                "default": {
                    "next_nodes": ["failure_node"]
                }
            }
        )
        
        # 创建工作流定义
        workflow_def = WorkflowDefinition(
            id="test_workflow",
            name="测试工作流",
            description="条件分支测试"
        )
        
        # 测试成功路径
        context = {"status": "success"}
        next_nodes = self.condition_engine.get_next_nodes(node, context, workflow_def)
        self.assertEqual(next_nodes, ["success_node"])
        
        # 测试默认路径
        context = {"status": "failed"}
        next_nodes = self.condition_engine.get_next_nodes(node, context, workflow_def)
        self.assertEqual(next_nodes, ["failure_node"])


class TestWorkflowDSL(unittest.TestCase):
    """测试工作流DSL"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.parser = WorkflowDSLParser()
    
    def test_yaml_parsing(self):
        """测试YAML解析"""
        yaml_content = """
id: test_workflow
name: 测试工作流
description: YAML格式的工作流定义
version: "1.0.0"
nodes:
  start_task:
    name: 开始任务
    type: task
    agent_id: test_agent
    task_config:
      type: initialize
      description: 初始化任务
  end_task:
    name: 结束任务
    type: task
    agent_id: test_agent
    task_config:
      type: finalize
      description: 结束任务
    dependencies:
      - start_task
edges:
  - from: start_task
    to: end_task
start_nodes:
  - start_task
end_nodes:
  - end_task
"""
        
        workflow_def = self.parser.parse_yaml(yaml_content)
        
        self.assertEqual(workflow_def.id, "test_workflow")
        self.assertEqual(workflow_def.name, "测试工作流")
        self.assertEqual(len(workflow_def.nodes), 2)
        self.assertEqual(len(workflow_def.edges), 1)
        self.assertEqual(workflow_def.start_nodes, ["start_task"])
        self.assertEqual(workflow_def.end_nodes, ["end_task"])
    
    def test_json_parsing(self):
        """测试JSON解析"""
        json_content = """
{
  "id": "json_workflow",
  "name": "JSON工作流",
  "description": "JSON格式的工作流定义",
  "nodes": {
    "task1": {
      "name": "任务1",
      "type": "task",
      "agent_id": "agent1",
      "task_config": {
        "type": "process",
        "description": "处理数据"
      }
    }
  },
  "edges": [],
  "start_nodes": ["task1"],
  "end_nodes": ["task1"]
}
"""
        
        workflow_def = self.parser.parse_json(json_content)
        
        self.assertEqual(workflow_def.id, "json_workflow")
        self.assertEqual(workflow_def.name, "JSON工作流")
        self.assertEqual(len(workflow_def.nodes), 1)
    
    def test_workflow_validation(self):
        """测试工作流验证"""
        # 测试缺少必需字段
        invalid_yaml = """
name: 无效工作流
nodes:
  task1:
    type: task
"""
        
        with self.assertRaises(ValueError):
            self.parser.parse_yaml(invalid_yaml)
        
        # 测试无效节点类型
        invalid_yaml = """
id: invalid_workflow
name: 无效工作流
nodes:
  task1:
    type: invalid_type
    agent_id: agent1
"""
        
        with self.assertRaises(ValueError):
            self.parser.parse_yaml(invalid_yaml)


class TestWorkflowTemplateLibrary(unittest.TestCase):
    """测试工作流模板库"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.template_library = WorkflowTemplateLibrary()
    
    def test_template_listing(self):
        """测试模板列表"""
        templates = self.template_library.list_templates()
        
        self.assertIsInstance(templates, list)
        self.assertGreater(len(templates), 0)
        
        # 检查模板结构
        for template in templates:
            self.assertIn("name", template)
            self.assertIn("title", template)
            self.assertIn("description", template)
    
    def test_template_retrieval(self):
        """测试模板获取"""
        template = self.template_library.get_template("software_development")
        
        self.assertIsNotNone(template)
        self.assertEqual(template["id"], "software_development_template")
        self.assertIn("nodes", template)
        self.assertIn("edges", template)
    
    def test_workflow_creation_from_template(self):
        """测试从模板创建工作流"""
        workflow_def = self.template_library.create_workflow_from_template(
            "simple_task",
            "my_simple_workflow",
            {
                "name": "我的简单工作流",
                "description": "自定义描述"
            }
        )
        
        self.assertEqual(workflow_def.id, "my_simple_workflow")
        self.assertEqual(workflow_def.name, "我的简单工作流")
        self.assertEqual(workflow_def.description, "自定义描述")


class TestWorkflowMonitor(unittest.TestCase):
    """测试工作流监控器"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.monitor = WorkflowMonitor()
    
    def test_event_emission(self):
        """测试事件发送"""
        events_received = []
        
        def event_handler(event):
            events_received.append(event)
        
        # 注册事件处理器
        self.monitor.add_event_handler(MonitorEventType.WORKFLOW_STARTED, event_handler)
        
        # 发送事件
        self.monitor._emit_event(
            MonitorEventType.WORKFLOW_STARTED,
            "test_workflow",
            "test_execution",
            message="测试事件"
        )
        
        self.assertEqual(len(events_received), 1)
        self.assertEqual(events_received[0].event_type, MonitorEventType.WORKFLOW_STARTED)
        self.assertEqual(events_received[0].workflow_id, "test_workflow")
    
    def test_performance_metrics_tracking(self):
        """测试性能指标跟踪"""
        from src.langgraph_system.workflow.workflow_engine import WorkflowExecution
        
        # 创建模拟执行
        execution = WorkflowExecution(
            id="test_execution",
            workflow_id="test_workflow",
            start_time=datetime.now()
        )
        
        # 注册执行
        self.monitor.register_execution(execution)
        
        # 检查性能指标
        metrics = self.monitor.get_performance_metrics("test_execution")
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics.execution_id, "test_execution")
        self.assertEqual(metrics.workflow_id, "test_workflow")
    
    def test_debugger_functionality(self):
        """测试调试器功能"""
        debugger = self.monitor.debugger
        
        # 设置断点
        debugger.set_breakpoint("test_execution", "test_node")
        
        # 检查断点
        should_pause = debugger.should_pause_at_node("test_execution", "test_node")
        self.assertTrue(should_pause)
        
        # 移除断点
        debugger.remove_breakpoint("test_execution", "test_node")
        should_pause = debugger.should_pause_at_node("test_execution", "test_node")
        self.assertFalse(should_pause)
        
        # 测试单步模式
        debugger.enable_step_mode("test_execution")
        should_pause = debugger.should_pause_at_node("test_execution", "any_node")
        self.assertTrue(should_pause)


class TestEnhancedSupervisorAgent(unittest.TestCase):
    """测试增强版Supervisor智能体"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 使用模拟配置避免实际LLM调用
        with patch('src.langgraph_system.agents.enhanced_supervisor_agent.SupervisorAgent.__init__'):
            self.supervisor = EnhancedSupervisorAgent()
            # 手动设置必要的属性
            self.supervisor.agents = {}
            self.supervisor.model = Mock()
    
    def test_workflow_creation_from_yaml(self):
        """测试从YAML创建工作流"""
        async def run_test():
            yaml_content = """
id: supervisor_test_workflow
name: Supervisor测试工作流
description: 测试增强版Supervisor的工作流创建
nodes:
  test_task:
    name: 测试任务
    type: task
    agent_id: test_agent
    task_config:
      type: test
      description: 测试任务
edges: []
start_nodes:
  - test_task
end_nodes:
  - test_task
"""
            
            workflow_id = await self.supervisor.create_workflow_from_yaml(yaml_content)
            
            self.assertEqual(workflow_id, "supervisor_test_workflow")
            self.assertIn(workflow_id, self.supervisor.workflow_cache)
        
        asyncio.run(run_test())
    
    def test_template_based_workflow_creation(self):
        """测试基于模板的工作流创建"""
        async def run_test():
            workflow_id = await self.supervisor.create_workflow_from_template(
                "simple_task",
                "my_template_workflow",
                {
                    "name": "我的模板工作流",
                    "nodes": {
                        "main_task": {
                            "agent_id": "custom_agent"
                        }
                    }
                }
            )
            
            self.assertEqual(workflow_id, "my_template_workflow")
            self.assertIn(workflow_id, self.supervisor.workflow_cache)
            
            # 检查自定义配置是否应用
            workflow_def = self.supervisor.workflow_cache[workflow_id]
            self.assertEqual(workflow_def.name, "我的模板工作流")
        
        asyncio.run(run_test())
    
    def test_workflow_optimization(self):
        """测试工作流优化"""
        # 创建一个简单的工作流用于优化测试
        from src.langgraph_system.workflow import WorkflowDefinition, WorkflowNode, NodeType
        
        workflow_def = WorkflowDefinition(
            id="optimization_test",
            name="优化测试工作流",
            description="用于测试优化功能的工作流"
        )
        
        # 添加一些节点
        for i in range(3):
            node = WorkflowNode(
                id=f"task_{i}",
                name=f"任务{i}",
                node_type=NodeType.TASK,
                agent_id="test_agent"
            )
            workflow_def.nodes[f"task_{i}"] = node
        
        self.supervisor.workflow_cache["optimization_test"] = workflow_def
        
        # 执行优化分析
        optimization_result = self.supervisor.optimize_workflow("optimization_test")
        
        self.assertIn("workflow_id", optimization_result)
        self.assertIn("optimization_suggestions", optimization_result)
        self.assertIn("estimated_improvement", optimization_result)
    
    def test_system_status(self):
        """测试系统状态获取"""
        status = self.supervisor.get_system_status()
        
        # 检查基础状态
        self.assertIn("supervisor_name", status)
        self.assertIn("available_agents", status)
        
        # 检查工作流引擎状态
        self.assertIn("workflow_engine", status)
        self.assertIn("monitoring", status)
        self.assertIn("templates", status)


class TestWorkflowIntegration(unittest.TestCase):
    """工作流系统集成测试"""
    
    def test_end_to_end_workflow_execution(self):
        """端到端工作流执行测试"""
        async def run_test():
            # 创建增强版Supervisor
            with patch('src.langgraph_system.agents.enhanced_supervisor_agent.SupervisorAgent.__init__'):
                supervisor = EnhancedSupervisorAgent()
                supervisor.agents = {"test_agent": Mock()}
                supervisor.model = Mock()
            
            # 从模板创建工作流
            workflow_id = await supervisor.create_workflow_from_template(
                "simple_task",
                "integration_test_workflow"
            )
            
            # 模拟工作流执行
            with patch.object(supervisor.workflow_engine, 'execute_workflow') as mock_execute:
                from src.langgraph_system.workflow import WorkflowExecution, WorkflowStatus
                
                mock_execution = WorkflowExecution(
                    id="test_execution",
                    workflow_id=workflow_id,
                    status=WorkflowStatus.COMPLETED
                )
                mock_execute.return_value = mock_execution
                
                # 执行工作流
                execution = await supervisor.execute_advanced_workflow(workflow_id)
                
                self.assertEqual(execution.status, WorkflowStatus.COMPLETED)
                self.assertEqual(execution.workflow_id, workflow_id)
        
        asyncio.run(run_test())
    
    def test_monitoring_integration(self):
        """监控集成测试"""
        with patch('src.langgraph_system.agents.enhanced_supervisor_agent.SupervisorAgent.__init__'):
            supervisor = EnhancedSupervisorAgent()
            supervisor.agents = {}
            supervisor.model = Mock()
        
        # 获取监控仪表板数据
        dashboard_data = supervisor.get_monitoring_dashboard()
        
        self.assertIn("statistics", dashboard_data)
        self.assertIn("active_executions", dashboard_data)
        self.assertIn("recent_events", dashboard_data)
        self.assertIn("performance_summary", dashboard_data)


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)