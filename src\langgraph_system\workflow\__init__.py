#!/usr/bin/env python3
"""
工作流模块
提供高级工作流引擎、DSL解析、监控和调试功能
"""

from .workflow_engine import (
    AdvancedWorkflowEngine,
    ParallelExecutionEngine,
    ConditionalBranchEngine,
    WorkflowDefinition,
    WorkflowNode,
    WorkflowExecution,
    NodeType,
    WorkflowStatus,
    ExecutionMode
)

from .workflow_dsl import (
    WorkflowDSLParser,
    WorkflowDSLGenerator,
    WorkflowTemplateLibrary
)

from .workflow_monitor import (
    WorkflowMonitor,
    WorkflowDebugger,
    MonitorEvent,
    MonitorEventType,
    PerformanceMetrics,
    AlertRule
)

__all__ = [
    # 工作流引擎
    "AdvancedWorkflowEngine",
    "ParallelExecutionEngine", 
    "ConditionalBranchEngine",
    
    # 工作流定义
    "WorkflowDefinition",
    "WorkflowNode",
    "WorkflowExecution",
    
    # 枚举类型
    "NodeType",
    "WorkflowStatus",
    "ExecutionMode",
    
    # DSL相关
    "WorkflowDSLParser",
    "WorkflowDSLGenerator",
    "WorkflowTemplateLibrary",
    
    # 监控相关
    "WorkflowMonitor",
    "WorkflowDebugger",
    "MonitorEvent",
    "MonitorEventType",
    "PerformanceMetrics",
    "AlertRule"
]

# 版本信息
__version__ = "0.3.0"
__author__ = "LangGraph Team"
__description__ = "Advanced Workflow Engine for LangGraph Multi-Agent System"