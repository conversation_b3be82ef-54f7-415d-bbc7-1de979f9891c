"""
项目生成工具 - 实际创建项目文件和目录结构
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class ProjectGenerator:
    """项目生成器 - 负责实际创建项目文件"""
    
    def __init__(self, workspace_dir: str = None):
        self.workspace_dir = Path(workspace_dir) if workspace_dir else Path("workspace")
        self.workspace_dir.mkdir(exist_ok=True)
    
    def create_project_structure(self, project_name: str, project_type: str = "web_app") -> Dict[str, Any]:
        """创建项目目录结构"""
        project_dir = self.workspace_dir / project_name
        project_dir.mkdir(exist_ok=True)
        
        # 根据项目类型创建不同的目录结构
        if project_type == "web_app":
            directories = [
                "src", "tests", "docs", "config", "scripts",
                "frontend", "backend", "static", "templates"
            ]
        elif project_type == "api":
            directories = [
                "src", "tests", "docs", "config", "scripts",
                "api", "models", "services", "utils"
            ]
        elif project_type == "data_science":
            directories = [
                "data", "notebooks", "src", "tests", "docs",
                "models", "reports", "scripts"
            ]
        else:
            directories = [
                "src", "tests", "docs", "config", "scripts"
            ]
        
        created_dirs = []
        for dir_name in directories:
            dir_path = project_dir / dir_name
            dir_path.mkdir(exist_ok=True)
            created_dirs.append(str(dir_path.relative_to(self.workspace_dir)))
        
        return {
            "project_dir": str(project_dir),
            "created_directories": created_dirs,
            "project_type": project_type
        }
    
    def create_file(self, project_name: str, file_path: str, content: str, 
                   encoding: str = "utf-8") -> Dict[str, Any]:
        """创建单个文件"""
        project_dir = self.workspace_dir / project_name
        full_path = project_dir / file_path
        
        # 确保父目录存在
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            full_path.write_text(content, encoding=encoding)
            file_size = full_path.stat().st_size
            
            logger.info(f"创建文件: {full_path} ({file_size} bytes)")
            
            return {
                "status": "success",
                "file_path": str(full_path.relative_to(self.workspace_dir)),
                "absolute_path": str(full_path),
                "size": file_size,
                "encoding": encoding
            }
        except Exception as e:
            logger.error(f"创建文件失败 {full_path}: {e}")
            return {
                "status": "error",
                "file_path": str(full_path.relative_to(self.workspace_dir)),
                "error": str(e)
            }
    
    def create_multiple_files(self, project_name: str, 
                            files: Dict[str, str]) -> Dict[str, Any]:
        """批量创建文件"""
        results = []
        success_count = 0
        
        for file_path, content in files.items():
            result = self.create_file(project_name, file_path, content)
            results.append(result)
            if result["status"] == "success":
                success_count += 1
        
        return {
            "total_files": len(files),
            "success_count": success_count,
            "failed_count": len(files) - success_count,
            "results": results
        }
    
    def generate_web_app_project(self, project_name: str, description: str,
                                tech_stack: Dict[str, str] = None) -> Dict[str, Any]:
        """生成完整的Web应用项目"""
        
        # 默认技术栈
        if not tech_stack:
            tech_stack = {
                "backend": "FastAPI",
                "frontend": "HTML/CSS/JavaScript",
                "database": "SQLite",
                "auth": "JWT"
            }
        
        # 创建目录结构
        structure_result = self.create_project_structure(project_name, "web_app")
        
        # 生成项目文件
        files = self._generate_web_app_files(project_name, description, tech_stack)
        
        # 创建文件
        files_result = self.create_multiple_files(project_name, files)
        
        return {
            "project_name": project_name,
            "description": description,
            "tech_stack": tech_stack,
            "structure": structure_result,
            "files": files_result,
            "created_at": datetime.now().isoformat()
        }
    
    def _generate_web_app_files(self, project_name: str, description: str,
                               tech_stack: Dict[str, str]) -> Dict[str, str]:
        """生成Web应用的文件内容"""
        
        files = {}
        
        # README.md
        files["README.md"] = f"""# {project_name}

{description}

## 技术栈

- **后端**: {tech_stack.get('backend', 'FastAPI')}
- **前端**: {tech_stack.get('frontend', 'HTML/CSS/JavaScript')}
- **数据库**: {tech_stack.get('database', 'SQLite')}
- **认证**: {tech_stack.get('auth', 'JWT')}

## 快速开始

1. 安装依赖:
   ```bash
   pip install -r requirements.txt
   ```

2. 启动后端:
   ```bash
   python backend/main.py
   ```

3. 访问应用:
   ```
   http://localhost:8000
   ```

## 项目结构

```
{project_name}/
├── backend/          # 后端代码
├── frontend/         # 前端代码
├── tests/           # 测试代码
├── docs/            # 文档
├── config/          # 配置文件
└── scripts/         # 脚本文件
```

## 开发指南

请查看 [docs/DEVELOPMENT.md](docs/DEVELOPMENT.md) 获取详细的开发指南。

---

*项目创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # requirements.txt
        if tech_stack.get('backend') == 'FastAPI':
            files["requirements.txt"] = """fastapi>=0.104.0
uvicorn>=0.24.0
sqlalchemy>=2.0.0
pydantic>=2.0.0
python-jose>=3.3.0
passlib>=1.7.4
python-multipart>=0.0.6
pytest>=7.0.0
requests>=2.31.0
"""
        
        # .gitignore
        files[".gitignore"] = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Database
*.db
*.sqlite3

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Testing
.coverage
.pytest_cache/
htmlcov/

# Node.js (if using)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
"""
        
        # Docker配置
        files["docker-compose.yml"] = f"""version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./app.db
    volumes:
      - .:/app
    working_dir: /app
    command: python backend/main.py

  # 可选：添加数据库服务
  # db:
  #   image: postgres:15
  #   environment:
  #     POSTGRES_DB: {project_name.lower()}
  #     POSTGRES_USER: user
  #     POSTGRES_PASSWORD: password
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data

# volumes:
#   postgres_data:
"""
        
        # Dockerfile
        files["Dockerfile"] = """FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "backend/main.py"]
"""
        
        # 后端主文件
        if tech_stack.get('backend') == 'FastAPI':
            files["backend/main.py"] = self._generate_fastapi_main(project_name)
        
        # 前端文件
        files["frontend/index.html"] = self._generate_frontend_html(project_name, description)
        files["frontend/style.css"] = self._generate_frontend_css()
        files["frontend/script.js"] = self._generate_frontend_js()
        
        # 测试文件
        files["tests/test_main.py"] = self._generate_test_file(project_name)
        
        # 文档
        files["docs/API.md"] = self._generate_api_docs(project_name)
        files["docs/DEVELOPMENT.md"] = self._generate_dev_docs(project_name)
        
        # 配置文件
        files["config/settings.py"] = self._generate_settings_file()
        
        return files
    
    def _generate_fastapi_main(self, project_name: str) -> str:
        """生成FastAPI主文件"""
        return f'''"""
{project_name} - FastAPI后端应用
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from pydantic import BaseModel
from datetime import datetime
from typing import List, Optional
import uvicorn

# 数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./app.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={{"check_same_thread": False}})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# FastAPI应用
app = FastAPI(
    title="{project_name} API",
    description="API服务",
    version="1.0.0"
)

# 静态文件
app.mount("/static", StaticFiles(directory="frontend"), name="static")

# 数据模型
class Item(Base):
    __tablename__ = "items"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True)
    description = Column(String)
    completed = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)

# Pydantic模型
class ItemCreate(BaseModel):
    title: str
    description: Optional[str] = None

class ItemResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    completed: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

# 依赖注入
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# API路由
@app.get("/", response_class=HTMLResponse)
async def read_root():
    """主页"""
    with open("frontend/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.get("/api/items", response_model=List[ItemResponse])
async def get_items(db: Session = Depends(get_db)):
    """获取所有项目"""
    return db.query(Item).all()

@app.post("/api/items", response_model=ItemResponse)
async def create_item(item: ItemCreate, db: Session = Depends(get_db)):
    """创建新项目"""
    db_item = Item(**item.dict())
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item

@app.put("/api/items/{{item_id}}")
async def update_item(item_id: int, completed: bool, db: Session = Depends(get_db)):
    """更新项目状态"""
    db_item = db.query(Item).filter(Item.id == item_id).first()
    if not db_item:
        raise HTTPException(status_code=404, detail="Item not found")
    
    db_item.completed = completed
    db.commit()
    return {{"message": "Item updated successfully"}}

@app.delete("/api/items/{{item_id}}")
async def delete_item(item_id: int, db: Session = Depends(get_db)):
    """删除项目"""
    db_item = db.query(Item).filter(Item.id == item_id).first()
    if not db_item:
        raise HTTPException(status_code=404, detail="Item not found")
    
    db.delete(db_item)
    db.commit()
    return {{"message": "Item deleted successfully"}}

# 健康检查
@app.get("/health")
async def health_check():
    """健康检查"""
    return {{"status": "healthy", "timestamp": datetime.utcnow()}}

# 创建数据库表
Base.metadata.create_all(bind=engine)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
'''
    
    def _generate_frontend_html(self, project_name: str, description: str) -> str:
        """生成前端HTML"""
        return f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{project_name}</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>{project_name}</h1>
            <p class="description">{description}</p>
        </header>
        
        <main>
            <section class="add-item">
                <h2>添加新项目</h2>
                <form id="itemForm">
                    <input type="text" id="title" placeholder="项目标题" required>
                    <textarea id="description" placeholder="项目描述"></textarea>
                    <button type="submit">添加项目</button>
                </form>
            </section>
            
            <section class="items-list">
                <h2>项目列表</h2>
                <div id="itemsList" class="items-container">
                    <!-- 项目列表将在这里显示 -->
                </div>
            </section>
        </main>
    </div>
    
    <script src="/static/script.js"></script>
</body>
</html>'''
    
    def _generate_frontend_css(self) -> str:
        """生成前端CSS"""
        return '''/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.description {
    color: #7f8c8d;
    font-size: 1.1em;
}

/* 表单样式 */
.add-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.add-item h2 {
    margin-bottom: 15px;
    color: #2c3e50;
}

#itemForm {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

input, textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

input:focus, textarea:focus {
    outline: none;
    border-color: #3498db;
}

button {
    padding: 12px 24px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

button:hover {
    background: #2980b9;
}

/* 项目列表样式 */
.items-list {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.items-list h2 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.item {
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 10px;
    background: #fafafa;
    transition: all 0.3s;
}

.item:hover {
    background: #f0f0f0;
}

.item.completed {
    opacity: 0.6;
    text-decoration: line-through;
}

.item-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.item-description {
    color: #666;
    margin-bottom: 10px;
}

.item-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    padding: 5px 10px;
    font-size: 14px;
}

.btn-success {
    background: #27ae60;
}

.btn-success:hover {
    background: #229954;
}

.btn-danger {
    background: #e74c3c;
}

.btn-danger:hover {
    background: #c0392b;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .container {
        padding: 10px;
    }
    
    .item-actions {
        flex-direction: column;
    }
}'''
    
    def _generate_frontend_js(self) -> str:
        """生成前端JavaScript"""
        return '''// 应用状态
let items = [];

// DOM元素
const itemForm = document.getElementById('itemForm');
const titleInput = document.getElementById('title');
const descriptionInput = document.getElementById('description');
const itemsList = document.getElementById('itemsList');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    loadItems();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    itemForm.addEventListener('submit', handleAddItem);
}

// 加载项目列表
async function loadItems() {
    try {
        const response = await fetch('/api/items');
        if (response.ok) {
            items = await response.json();
            renderItems();
        } else {
            console.error('Failed to load items');
        }
    } catch (error) {
        console.error('Error loading items:', error);
    }
}

// 渲染项目列表
function renderItems() {
    if (items.length === 0) {
        itemsList.innerHTML = '<p>暂无项目</p>';
        return;
    }
    
    const itemsHTML = items.map(item => `
        <div class="item ${item.completed ? 'completed' : ''}">
            <div class="item-title">${escapeHtml(item.title)}</div>
            <div class="item-description">${escapeHtml(item.description || '')}</div>
            <div class="item-actions">
                <button class="btn-small btn-success" onclick="toggleItem(${item.id}, ${!item.completed})">
                    ${item.completed ? '取消完成' : '标记完成'}
                </button>
                <button class="btn-small btn-danger" onclick="deleteItem(${item.id})">
                    删除
                </button>
            </div>
        </div>
    `).join('');
    
    itemsList.innerHTML = itemsHTML;
}

// 添加新项目
async function handleAddItem(event) {
    event.preventDefault();
    
    const title = titleInput.value.trim();
    const description = descriptionInput.value.trim();
    
    if (!title) {
        alert('请输入项目标题');
        return;
    }
    
    try {
        const response = await fetch('/api/items', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                title: title,
                description: description
            })
        });
        
        if (response.ok) {
            titleInput.value = '';
            descriptionInput.value = '';
            loadItems(); // 重新加载列表
        } else {
            alert('添加项目失败');
        }
    } catch (error) {
        console.error('Error adding item:', error);
        alert('添加项目时发生错误');
    }
}

// 切换项目状态
async function toggleItem(itemId, completed) {
    try {
        const response = await fetch(`/api/items/${itemId}?completed=${completed}`, {
            method: 'PUT'
        });
        
        if (response.ok) {
            loadItems(); // 重新加载列表
        } else {
            alert('更新项目状态失败');
        }
    } catch (error) {
        console.error('Error updating item:', error);
        alert('更新项目时发生错误');
    }
}

// 删除项目
async function deleteItem(itemId) {
    if (!confirm('确定要删除这个项目吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/items/${itemId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            loadItems(); // 重新加载列表
        } else {
            alert('删除项目失败');
        }
    } catch (error) {
        console.error('Error deleting item:', error);
        alert('删除项目时发生错误');
    }
}

// HTML转义函数
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}'''
    
    def _generate_test_file(self, project_name: str) -> str:
        """生成测试文件"""
        return f'''"""
{project_name} 测试用例
"""

import pytest
from fastapi.testclient import TestClient
from backend.main import app

client = TestClient(app)

def test_read_root():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200

def test_health_check():
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_get_items():
    """测试获取项目列表"""
    response = client.get("/api/items")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_create_item():
    """测试创建项目"""
    item_data = {{
        "title": "测试项目",
        "description": "这是一个测试项目"
    }}
    response = client.post("/api/items", json=item_data)
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "测试项目"
    assert data["completed"] == False

def test_create_item_without_description():
    """测试创建没有描述的项目"""
    item_data = {{
        "title": "简单项目"
    }}
    response = client.post("/api/items", json=item_data)
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "简单项目"
    assert data["description"] is None

if __name__ == "__main__":
    pytest.main([__file__])
'''
    
    def _generate_api_docs(self, project_name: str) -> str:
        """生成API文档"""
        return f'''# {project_name} API 文档

## 概述

{project_name} 提供了一套完整的 RESTful API，用于管理项目数据。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: v1
- **数据格式**: JSON

## 认证

目前API不需要认证，但建议在生产环境中添加适当的认证机制。

## API端点

### 1. 健康检查

**GET** `/health`

检查API服务状态。

**响应示例**:
```json
{{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00"
}}
```

### 2. 获取项目列表

**GET** `/api/items`

获取所有项目的列表。

**响应示例**:
```json
[
  {{
    "id": 1,
    "title": "项目标题",
    "description": "项目描述",
    "completed": false,
    "created_at": "2024-01-01T12:00:00"
  }}
]
```

### 3. 创建新项目

**POST** `/api/items`

创建一个新的项目。

**请求体**:
```json
{{
  "title": "项目标题",
  "description": "项目描述（可选）"
}}
```

**响应示例**:
```json
{{
  "id": 1,
  "title": "项目标题",
  "description": "项目描述",
  "completed": false,
  "created_at": "2024-01-01T12:00:00"
}}
```

### 4. 更新项目状态

**PUT** `/api/items/{{item_id}}`

更新指定项目的完成状态。

**查询参数**:
- `completed` (boolean): 项目是否完成

**响应示例**:
```json
{{
  "message": "Item updated successfully"
}}
```

### 5. 删除项目

**DELETE** `/api/items/{{item_id}}`

删除指定的项目。

**响应示例**:
```json
{{
  "message": "Item deleted successfully"
}}
```

## 错误处理

API使用标准的HTTP状态码：

- `200` - 成功
- `404` - 资源未找到
- `422` - 请求数据验证失败
- `500` - 服务器内部错误

错误响应格式：
```json
{{
  "detail": "错误描述"
}}
```

## 开发工具

### 交互式API文档

启动服务后，可以访问以下地址查看交互式API文档：

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### 测试

运行测试：
```bash
pytest tests/
```
'''
    
    def _generate_dev_docs(self, project_name: str) -> str:
        """生成开发文档"""
        return f'''# {project_name} 开发指南

## 开发环境设置

### 1. 环境要求

- Python 3.8+
- pip 或 poetry

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动开发服务器

```bash
python backend/main.py
```

服务器将在 `http://localhost:8000` 启动。

## 项目结构

```
{project_name}/
├── backend/              # 后端代码
│   └── main.py          # FastAPI应用主文件
├── frontend/            # 前端代码
│   ├── index.html       # 主页面
│   ├── style.css        # 样式文件
│   └── script.js        # JavaScript代码
├── tests/               # 测试代码
│   └── test_main.py     # 主要测试用例
├── docs/                # 文档
│   ├── API.md           # API文档
│   └── DEVELOPMENT.md   # 开发指南
├── config/              # 配置文件
│   └── settings.py      # 应用设置
├── scripts/             # 脚本文件
├── requirements.txt     # Python依赖
├── docker-compose.yml   # Docker配置
├── Dockerfile          # Docker镜像配置
└── README.md           # 项目说明
```

## 开发工作流

### 1. 功能开发

1. 创建新分支
2. 实现功能
3. 编写测试
4. 运行测试确保通过
5. 提交代码

### 2. 测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_main.py

# 运行测试并生成覆盖率报告
pytest --cov=backend tests/
```

### 3. 代码质量

建议使用以下工具保证代码质量：

```bash
# 代码格式化
black backend/

# 代码检查
flake8 backend/

# 类型检查
mypy backend/
```

## 数据库

### 1. 数据库迁移

项目使用SQLAlchemy ORM，数据库表会在应用启动时自动创建。

### 2. 数据库操作

```python
# 添加新的数据模型
class NewModel(Base):
    __tablename__ = "new_table"
    
    id = Column(Integer, primary_key=True)
    name = Column(String, index=True)
```

## 部署

### 1. Docker部署

```bash
# 构建镜像
docker build -t {project_name.lower()} .

# 运行容器
docker run -p 8000:8000 {project_name.lower()}
```

### 2. Docker Compose

```bash
docker-compose up -d
```

## 常见问题

### 1. 端口被占用

如果8000端口被占用，可以修改`backend/main.py`中的端口号：

```python
uvicorn.run(app, host="0.0.0.0", port=8001, reload=True)
```

### 2. 数据库连接问题

检查数据库文件权限和路径是否正确。

### 3. 依赖安装失败

尝试升级pip：
```bash
pip install --upgrade pip
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。
'''
    
    def _generate_settings_file(self) -> str:
        """生成设置文件"""
        return '''"""
应用配置设置
"""

import os
from typing import Optional

class Settings:
    """应用设置"""
    
    # 数据库配置
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./app.db")
    
    # API配置
    API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
    API_PORT: int = int(os.getenv("API_PORT", "8000"))
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    
    # 安全配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # CORS配置
    ALLOWED_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000"
    ]
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    class Config:
        case_sensitive = True

# 全局设置实例
settings = Settings()
'''

# 全局实例
project_generator = ProjectGenerator()
