# 🤖 Kimi K2默认配置指南

> **LangGraph多智能体协作平台默认模型配置为kimi-k2-0711-preview**

## 🎯 配置概述

系统已成功配置为默认使用Moonshot的Kimi K2模型（kimi-k2-0711-preview），无需每次手动指定LLM提供商。

## ✅ 已修改的文件

### 1. **LLM配置类** (`src/langgraph_system/llm/config.py`)
```python
# 修改前
provider: str = Field(default_factory=lambda: os.getenv("LLM_PROVIDER"))
model: str = Field(default="gpt-4")

# 修改后
provider: str = Field(default_factory=lambda: os.getenv("LLM_PROVIDER", "moonshot"))
model: str = Field(default="kimi-k2-0711-preview")
```

### 2. **系统设置** (`src/langgraph_system/config/settings.py`)
```python
# 修改前
default_model: str = Field(default="gpt-4")
default_provider: str = Field(default="openai")

# 修改后
default_model: str = Field(default="kimi-k2-0711-preview")
default_provider: str = Field(default="moonshot")
```

### 3. **CLI默认参数** (`src/langgraph_system/cli/cli_main.py`)
```python
# 修改前
@click.option('--llm-provider', default='openai', help='LLM提供商')

# 修改后
@click.option('--llm-provider', default='moonshot', help='LLM提供商')

# 添加了模型自动设置逻辑
if llm_config.provider == "moonshot":
    llm_config.model = "kimi-k2-0711-preview"
```

### 4. **环境配置** (`.env`)
```bash
# 新增配置
LLM_PROVIDER="moonshot"
DEFAULT_MODEL="kimi-k2-0711-preview"
TEMPERATURE=0.7
```

### 5. **示例配置** (`.env.example`)
```bash
# 修改前
DEFAULT_MODEL=gpt-4o-mini

# 修改后
DEFAULT_MODEL=kimi-k2-0711-preview
```

## 🚀 使用方法

### 简化命令（推荐）
现在可以直接使用命令，无需指定提供商：

```bash
# 系统状态
python scripts/run_cli.py status

# 智能体列表
python scripts/run_cli.py agents list

# 项目创建
python scripts/run_cli.py project create --name "MyProject" --description "项目描述"
```

### 完整命令（可选）
仍然可以显式指定提供商：

```bash
# 明确指定moonshot
python scripts/run_cli.py --llm-provider moonshot status

# 使用其他提供商
python scripts/run_cli.py --llm-provider openai status
python scripts/run_cli.py --llm-provider anthropic status
```

## 📊 配置验证

运行验证脚本确认配置正确：

```bash
python scripts/verify_kimi_config.py
```

**预期输出**:
```
🎯 验证结果: 5/5 通过 (100.0%)
🎉 配置验证成功！系统已正确配置为使用Kimi K2

📋 配置摘要:
   • 默认提供商: moonshot
   • 默认模型: kimi-k2-0711-preview
   • 温度参数: 0.7
   • API基础URL: https://api.moonshot.cn/v1
```

## 🔧 技术细节

### 模型规格
- **模型名称**: kimi-k2-0711-preview
- **提供商**: Moonshot AI
- **API基础URL**: https://api.moonshot.cn/v1
- **默认温度**: 0.7
- **超时时间**: 120秒

### 兼容性
- ✅ **向后兼容**: 仍支持OpenAI和Anthropic
- ✅ **环境变量**: 支持通过环境变量覆盖
- ✅ **命令行参数**: 支持运行时指定不同提供商
- ✅ **配置文件**: 支持通过配置文件自定义

### 自动降级
如果Moonshot API不可用，系统会：
1. 显示错误信息
2. 提示检查API密钥配置
3. 建议使用其他提供商

## 🛠️ 故障排除

### 常见问题

#### 1. API密钥未配置
**错误**: `MOONSHOT_API_KEY is not set`
**解决**: 在`.env`文件中设置正确的API密钥

#### 2. 网络连接问题
**错误**: `Connection timeout`
**解决**: 检查网络连接或使用代理

#### 3. 模型不可用
**错误**: `Model not found`
**解决**: 确认使用的是正确的模型名称

### 切换到其他提供商

如需临时使用其他提供商：

```bash
# 使用OpenAI
python scripts/run_cli.py --llm-provider openai --model gpt-4 status

# 使用Anthropic
python scripts/run_cli.py --llm-provider anthropic --model claude-3-sonnet status
```

## 📈 性能优势

### Kimi K2的优势
- 🚀 **响应速度**: 优化的推理速度
- 🧠 **中文理解**: 优秀的中文语言能力
- 💰 **成本效益**: 具有竞争力的定价
- 🔄 **稳定性**: 高可用性和稳定性

### 系统优化
- ⚡ **默认配置**: 减少命令行参数
- 🔧 **自动设置**: 智能模型选择
- 📊 **统一体验**: 一致的用户体验

## 🔗 相关文档

- [LLM配置指南](LLM_CONFIGURATION.md)
- [CLI使用手册](CLI_USAGE.md)
- [故障排除指南](TROUBLESHOOTING.md)
- [API参考文档](API_REFERENCE.md)

## 📝 更新日志

### v0.3.1 - 2025-07-26
- ✅ 设置kimi-k2-0711-preview为默认模型
- ✅ 更新所有配置文件和类
- ✅ 添加配置验证脚本
- ✅ 简化CLI命令使用
- ✅ 完善文档和示例

---

*配置完成时间: 2025-07-26*  
*默认模型: kimi-k2-0711-preview*  
*系统版本: LangGraph v0.3.0*
