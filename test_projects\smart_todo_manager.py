#!/usr/bin/env python3
"""
智能待办事项管理器 - LangGraph多智能体系统测试项目

这个项目将测试系统的以下功能：
1. 项目创建和管理
2. 多智能体协作
3. 代码生成和审查
4. 文档生成
5. 测试和部署

项目需求：
- 创建一个Web应用，用户可以管理待办事项
- 支持智能分类和优先级排序
- 包含用户认证和数据持久化
- 提供REST API和Web界面
- 包含完整的测试和部署配置
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from main import create_system_manager
from langgraph_system.states.project_state import TaskType
from langgraph_system.llm.config import LLMConfig


class SmartTodoProjectTest:
    """智能待办事项管理器项目测试"""
    
    def __init__(self):
        self.project_name = "SmartTodoManager"
        self.test_results = []
        self.start_time = datetime.now()
        
    def log_result(self, test_name: str, success: bool, message: str = "", details: Any = None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        
        if details and isinstance(details, dict):
            for key, value in details.items():
                print(f"   {key}: {value}")
    
    async def test_system_initialization(self):
        """测试系统初始化"""
        print("🔧 测试系统初始化...")
        
        try:
            # 创建系统管理器
            manager = create_system_manager()
            
            # 初始化系统
            llm_config = LLMConfig(provider="moonshot")
            success = await manager.initialize(llm_config)
            
            if success:
                # 健康检查
                health = await manager.health_check()
                
                self.log_result(
                    "系统初始化",
                    True,
                    "系统初始化成功",
                    {
                        "状态": health.get("status"),
                        "模式": health.get("mode"),
                        "版本": health.get("version")
                    }
                )
                return manager
            else:
                self.log_result("系统初始化", False, "系统初始化失败")
                return None
                
        except Exception as e:
            self.log_result("系统初始化", False, f"初始化异常: {e}")
            return None
    
    async def test_project_creation(self, manager):
        """测试项目创建"""
        print("\n🚀 测试项目创建...")
        
        try:
            # 定义项目需求
            project_requirements = {
                "name": self.project_name,
                "description": "智能待办事项管理器 - 支持智能分类、优先级排序和用户管理",
                "features": [
                    "用户注册和登录",
                    "待办事项CRUD操作",
                    "智能分类和标签",
                    "优先级自动排序",
                    "截止日期提醒",
                    "数据统计和报告",
                    "REST API接口",
                    "响应式Web界面"
                ],
                "tech_stack": {
                    "backend": "FastAPI + SQLAlchemy",
                    "frontend": "HTML + CSS + JavaScript",
                    "database": "SQLite",
                    "auth": "JWT Token",
                    "deployment": "Docker"
                },
                "requirements": {
                    "performance": "支持1000+并发用户",
                    "security": "安全的用户认证和数据保护",
                    "usability": "直观易用的界面设计",
                    "scalability": "可扩展的架构设计"
                }
            }
            
            # 创建项目
            result = await manager.core_system.create_project(
                project_name=self.project_name,
                task_type=TaskType.DEVELOPMENT,
                description=project_requirements["description"],
                requirements=project_requirements
            )
            
            self.log_result(
                "项目创建",
                result.get("status") == "created",
                result.get("message", ""),
                {
                    "项目ID": result.get("project_id"),
                    "任务类型": result.get("task_type"),
                    "状态": result.get("status")
                }
            )
            
            return result
            
        except Exception as e:
            self.log_result("项目创建", False, f"项目创建异常: {e}")
            return None
    
    async def test_cli_integration(self):
        """测试CLI集成"""
        print("\n⌨️  测试CLI集成...")
        
        import subprocess
        import os
        
        try:
            # 测试CLI状态命令
            env = os.environ.copy()
            env['PYTHONPATH'] = str(Path(__file__).parent.parent / "src")
            
            result = subprocess.run([
                sys.executable, "src/main.py", 
                "--llm-provider", "moonshot", 
                "status"
            ], 
            capture_output=True, 
            text=True, 
            timeout=30,
            cwd=str(Path(__file__).parent.parent),
            env=env
            )
            
            if result.returncode == 0:
                self.log_result(
                    "CLI状态命令",
                    True,
                    "CLI状态命令执行成功",
                    {"输出长度": len(result.stdout)}
                )
            else:
                self.log_result(
                    "CLI状态命令",
                    False,
                    f"CLI命令失败: {result.stderr}"
                )
            
            # 测试智能体列表命令
            result = subprocess.run([
                sys.executable, "src/main.py",
                "--llm-provider", "moonshot",
                "agents", "list"
            ],
            capture_output=True,
            text=True,
            timeout=30,
            cwd=str(Path(__file__).parent.parent),
            env=env
            )
            
            if result.returncode == 0:
                # 检查是否包含预期的智能体
                expected_agents = ["architect", "product_manager", "coder", "qa_engineer", "devops"]
                found_agents = []
                
                for agent in expected_agents:
                    if agent in result.stdout:
                        found_agents.append(agent)
                
                self.log_result(
                    "CLI智能体列表",
                    len(found_agents) >= 3,
                    f"找到 {len(found_agents)}/{len(expected_agents)} 个智能体",
                    {"找到的智能体": found_agents}
                )
            else:
                self.log_result(
                    "CLI智能体列表",
                    False,
                    f"智能体列表命令失败: {result.stderr}"
                )
                
        except subprocess.TimeoutExpired:
            self.log_result("CLI集成", False, "CLI命令超时")
        except Exception as e:
            self.log_result("CLI集成", False, f"CLI测试异常: {e}")
    
    async def test_workspace_creation(self):
        """测试工作空间创建"""
        print("\n📁 测试工作空间创建...")
        
        try:
            # 创建项目工作空间
            workspace_dir = Path(__file__).parent.parent / "workspace" / self.project_name
            workspace_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建基本目录结构
            directories = [
                "src",
                "tests", 
                "docs",
                "config",
                "scripts",
                "frontend",
                "backend"
            ]
            
            created_dirs = []
            for dir_name in directories:
                dir_path = workspace_dir / dir_name
                dir_path.mkdir(exist_ok=True)
                if dir_path.exists():
                    created_dirs.append(dir_name)
            
            # 创建基本文件
            files = {
                "README.md": f"# {self.project_name}\n\n智能待办事项管理器",
                "requirements.txt": "fastapi>=0.104.0\nsqlalchemy>=2.0.0\npydantic>=2.0.0",
                ".gitignore": "*.pyc\n__pycache__/\n.env\n*.db",
                "docker-compose.yml": "version: '3.8'\nservices:\n  app:\n    build: .\n    ports:\n      - '8000:8000'"
            }
            
            created_files = []
            for filename, content in files.items():
                file_path = workspace_dir / filename
                file_path.write_text(content, encoding='utf-8')
                if file_path.exists():
                    created_files.append(filename)
            
            self.log_result(
                "工作空间创建",
                len(created_dirs) == len(directories) and len(created_files) == len(files),
                f"工作空间创建成功",
                {
                    "目录": f"{len(created_dirs)}/{len(directories)}",
                    "文件": f"{len(created_files)}/{len(files)}",
                    "路径": str(workspace_dir)
                }
            )
            
            return workspace_dir
            
        except Exception as e:
            self.log_result("工作空间创建", False, f"工作空间创建异常: {e}")
            return None
    
    async def test_mock_code_generation(self, workspace_dir):
        """测试模拟代码生成"""
        print("\n💻 测试模拟代码生成...")
        
        if not workspace_dir:
            self.log_result("代码生成", False, "工作空间不存在")
            return
        
        try:
            # 模拟生成后端代码
            backend_code = '''"""
智能待办事项管理器 - 后端API
"""
from fastapi import FastAPI, HTTPException, Depends
from sqlalchemy import create_engine, Column, Integer, String, Boolean, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from pydantic import BaseModel
from datetime import datetime
from typing import List, Optional

app = FastAPI(title="Smart Todo Manager API")

# 数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./todos.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 数据模型
class TodoItem(Base):
    __tablename__ = "todos"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True)
    description = Column(String)
    completed = Column(Boolean, default=False)
    priority = Column(Integer, default=1)
    created_at = Column(DateTime, default=datetime.utcnow)
    due_date = Column(DateTime, nullable=True)

# Pydantic模型
class TodoCreate(BaseModel):
    title: str
    description: Optional[str] = None
    priority: int = 1
    due_date: Optional[datetime] = None

class TodoResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    completed: bool
    priority: int
    created_at: datetime
    due_date: Optional[datetime]
    
    class Config:
        from_attributes = True

# 依赖注入
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# API端点
@app.get("/")
async def root():
    return {"message": "Smart Todo Manager API"}

@app.get("/todos", response_model=List[TodoResponse])
async def get_todos(db: Session = Depends(get_db)):
    return db.query(TodoItem).all()

@app.post("/todos", response_model=TodoResponse)
async def create_todo(todo: TodoCreate, db: Session = Depends(get_db)):
    db_todo = TodoItem(**todo.dict())
    db.add(db_todo)
    db.commit()
    db.refresh(db_todo)
    return db_todo

if __name__ == "__main__":
    import uvicorn
    Base.metadata.create_all(bind=engine)
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
            
            # 模拟生成前端代码
            frontend_code = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能待办事项管理器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .todo-form { margin-bottom: 30px; padding: 20px; background: #f9f9f9; border-radius: 5px; }
        .todo-item { padding: 15px; margin: 10px 0; background: white; border-left: 4px solid #007bff; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .todo-item.completed { opacity: 0.6; border-left-color: #28a745; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        input, textarea, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">🎯 智能待办事项管理器</h1>
        
        <div class="todo-form">
            <h3>添加新任务</h3>
            <input type="text" id="todoTitle" placeholder="任务标题" required>
            <textarea id="todoDescription" placeholder="任务描述" rows="3"></textarea>
            <select id="todoPriority">
                <option value="1">低优先级</option>
                <option value="2" selected>中优先级</option>
                <option value="3">高优先级</option>
            </select>
            <input type="datetime-local" id="todoDueDate">
            <button class="btn btn-primary" onclick="addTodo()">添加任务</button>
        </div>
        
        <div id="todoList">
            <!-- 待办事项列表将在这里显示 -->
        </div>
    </div>

    <script>
        let todos = [];
        
        async function loadTodos() {
            try {
                const response = await fetch('/todos');
                todos = await response.json();
                renderTodos();
            } catch (error) {
                console.error('加载待办事项失败:', error);
            }
        }
        
        async function addTodo() {
            const title = document.getElementById('todoTitle').value;
            const description = document.getElementById('todoDescription').value;
            const priority = parseInt(document.getElementById('todoPriority').value);
            const dueDate = document.getElementById('todoDueDate').value;
            
            if (!title.trim()) {
                alert('请输入任务标题');
                return;
            }
            
            const todoData = {
                title: title,
                description: description,
                priority: priority,
                due_date: dueDate || null
            };
            
            try {
                const response = await fetch('/todos', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(todoData)
                });
                
                if (response.ok) {
                    document.getElementById('todoTitle').value = '';
                    document.getElementById('todoDescription').value = '';
                    document.getElementById('todoDueDate').value = '';
                    loadTodos();
                } else {
                    alert('添加任务失败');
                }
            } catch (error) {
                console.error('添加任务失败:', error);
            }
        }
        
        function renderTodos() {
            const todoList = document.getElementById('todoList');
            todoList.innerHTML = '';
            
            todos.sort((a, b) => b.priority - a.priority).forEach(todo => {
                const todoElement = document.createElement('div');
                todoElement.className = `todo-item ${todo.completed ? 'completed' : ''}`;
                todoElement.innerHTML = `
                    <h4>${todo.title}</h4>
                    <p>${todo.description || '无描述'}</p>
                    <small>优先级: ${todo.priority} | 创建时间: ${new Date(todo.created_at).toLocaleString()}</small>
                    ${todo.due_date ? `<br><small>截止时间: ${new Date(todo.due_date).toLocaleString()}</small>` : ''}
                    <div style="margin-top: 10px;">
                        <button class="btn btn-success" onclick="toggleTodo(${todo.id})">
                            ${todo.completed ? '取消完成' : '标记完成'}
                        </button>
                        <button class="btn btn-danger" onclick="deleteTodo(${todo.id})">删除</button>
                    </div>
                `;
                todoList.appendChild(todoElement);
            });
        }
        
        // 页面加载时获取待办事项
        window.onload = loadTodos;
    </script>
</body>
</html>'''
            
            # 保存生成的代码
            backend_file = workspace_dir / "backend" / "main.py"
            frontend_file = workspace_dir / "frontend" / "index.html"
            
            backend_file.write_text(backend_code, encoding='utf-8')
            frontend_file.write_text(frontend_code, encoding='utf-8')
            
            # 生成测试文件
            test_code = '''"""
智能待办事项管理器 - 测试用例
"""
import pytest
from fastapi.testclient import TestClient
from backend.main import app

client = TestClient(app)

def test_root():
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Smart Todo Manager API"}

def test_create_todo():
    todo_data = {
        "title": "测试任务",
        "description": "这是一个测试任务",
        "priority": 2
    }
    response = client.post("/todos", json=todo_data)
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "测试任务"
    assert data["completed"] == False

def test_get_todos():
    response = client.get("/todos")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
'''
            
            test_file = workspace_dir / "tests" / "test_main.py"
            test_file.write_text(test_code, encoding='utf-8')
            
            # 生成文档
            doc_content = f'''# {self.project_name} 文档

## 项目概述
智能待办事项管理器是一个现代化的任务管理应用，支持智能分类、优先级排序和用户管理。

## 功能特性
- ✅ 用户注册和登录
- ✅ 待办事项CRUD操作
- ✅ 智能分类和标签
- ✅ 优先级自动排序
- ✅ 截止日期提醒
- ✅ 数据统计和报告
- ✅ REST API接口
- ✅ 响应式Web界面

## 技术栈
- **后端**: FastAPI + SQLAlchemy
- **前端**: HTML + CSS + JavaScript
- **数据库**: SQLite
- **认证**: JWT Token
- **部署**: Docker

## 快速开始
1. 安装依赖: `pip install -r requirements.txt`
2. 启动后端: `python backend/main.py`
3. 访问前端: `http://localhost:8000/frontend/index.html`

## API文档
- GET /todos - 获取所有待办事项
- POST /todos - 创建新的待办事项
- PUT /todos/{{id}} - 更新待办事项
- DELETE /todos/{{id}} - 删除待办事项

## 测试
运行测试: `pytest tests/`

## 部署
使用Docker: `docker-compose up`
'''
            
            doc_file = workspace_dir / "docs" / "README.md"
            doc_file.write_text(doc_content, encoding='utf-8')
            
            # 统计生成的文件
            generated_files = [
                backend_file,
                frontend_file, 
                test_file,
                doc_file
            ]
            
            existing_files = [f for f in generated_files if f.exists()]
            
            self.log_result(
                "代码生成",
                len(existing_files) == len(generated_files),
                f"代码生成完成",
                {
                    "后端文件": backend_file.name if backend_file.exists() else "未生成",
                    "前端文件": frontend_file.name if frontend_file.exists() else "未生成", 
                    "测试文件": test_file.name if test_file.exists() else "未生成",
                    "文档文件": doc_file.name if doc_file.exists() else "未生成",
                    "总文件数": f"{len(existing_files)}/{len(generated_files)}"
                }
            )
            
        except Exception as e:
            self.log_result("代码生成", False, f"代码生成异常: {e}")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 生成测试报告...")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        duration = datetime.now() - self.start_time
        
        report = {
            "project_name": self.project_name,
            "test_summary": {
                "total": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
            },
            "duration": str(duration),
            "timestamp": datetime.now().isoformat(),
            "test_results": self.test_results
        }
        
        # 保存报告
        report_file = Path(__file__).parent / f"{self.project_name}_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        print(f"\n🎯 测试摘要:")
        print(f"   项目名称: {self.project_name}")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {failed_tests}")
        print(f"   成功率: {report['test_summary']['success_rate']}")
        print(f"   执行时间: {duration}")
        print(f"   报告文件: {report_file}")
        
        return report
    
    async def run_all_tests(self):
        """运行所有测试"""
        print(f"🚀 开始测试项目: {self.project_name}")
        print("=" * 60)
        
        # 1. 系统初始化测试
        manager = await self.test_system_initialization()
        
        # 2. 项目创建测试
        if manager:
            project_result = await self.test_project_creation(manager)
            
            # 关闭系统管理器
            await manager.shutdown()
        
        # 3. CLI集成测试
        await self.test_cli_integration()
        
        # 4. 工作空间创建测试
        workspace_dir = await self.test_workspace_creation()
        
        # 5. 代码生成测试
        await self.test_mock_code_generation(workspace_dir)
        
        # 6. 生成测试报告
        report = self.generate_test_report()
        
        print("\n" + "=" * 60)
        print("🎉 测试完成!")
        
        return report


async def main():
    """主函数"""
    test = SmartTodoProjectTest()
    report = await test.run_all_tests()
    
    # 根据测试结果返回适当的退出码
    if report["test_summary"]["failed"] == 0:
        print("✅ 所有测试通过!")
        return 0
    else:
        print(f"❌ {report['test_summary']['failed']} 个测试失败")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
