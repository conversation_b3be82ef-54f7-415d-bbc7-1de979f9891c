"""
命令行命令模块 v0.3.0
支持专业智能体生态、高级工作流引擎和智能协作机制
"""

import asyncio
import json
import yaml
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime

from ..states.project_state import ProjectState, TaskType, AgentStatus
from ..agents.supervisor_agent import SupervisorAgent, CollaborationMode, AgentCapability


class ProjectCommands:
    """项目相关命令 v0.3.0"""
    
    def __init__(self, supervisor: SupervisorAgent):
        self.supervisor = supervisor
    
    async def create_project(self, project_config: Dict[str, Any]) -> ProjectState:
        """创建新项目"""
        # 安全处理任务类型
        task_type_value = project_config.get("task_type")
        if task_type_value is None:
            task_type_value = "development"  # 默认值

        try:
            current_task = TaskType(task_type_value)
        except (ValueError, TypeError):
            logger.warning(f"无效的任务类型: {task_type_value}，使用默认值")
            current_task = TaskType.DEVELOPMENT

        project_state = ProjectState(
            project_name=project_config["name"],
            current_task=current_task,
            description=project_config["description"],
            context={
                "description": project_config["description"],
                "collaboration_mode": project_config.get("collaboration_mode"),
                "specified_agents": project_config.get("specified_agents")
            },
            execution_status="initialized"
        )
        return project_state
    
    async def create_execution_plan(self, project_state: ProjectState) -> Dict[str, Any]:
        """创建执行计划"""
        task_type = project_state.current_task
        requirements = project_state.context or {}
        
        # 使用supervisor创建计划
        plan = self.supervisor.create_task_plan(task_type, requirements)
        
        return {
            "project_id": project_state.project_id,
            "project_name": project_state.project_name,
            "task_type": task_type.value,
            "execution_plan": plan,
            "estimated_total_duration": sum(step["estimated_duration"] for step in plan),
            "created_at": datetime.now().isoformat()
        }
    
    async def execute_project(self, project_state: ProjectState) -> Dict[str, Any]:
        """执行项目"""
        # 构建任务信息
        task_info = {
            "id": project_state.project_name,  # 使用项目名作为ID
            "type": project_state.current_task.value,
            "description": project_state.description,
            "requirements": project_state.context or {}
        }

        # 使用supervisor处理任务
        result = await self.supervisor.process_task(task_info)

        # 如果任务成功完成，尝试创建实际文件
        if result["status"] == "completed":
            try:
                await self._create_project_files(project_state, result)
                project_state.execution_status = "completed"
                project_state.completed_tasks.append(project_state.current_task)
                project_state.current_task = None
            except Exception as e:
                logger.error(f"创建项目文件失败: {e}")
                result["file_creation_error"] = str(e)
        else:
            project_state.execution_status = "failed"

        return result

    async def _create_project_files(self, project_state: ProjectState, task_result: Dict[str, Any]):
        """创建项目文件"""
        from ..tools.project_generator import project_generator

        project_name = project_state.project_name
        description = project_state.description
        task_type = project_state.current_task.value

        logger.info(f"为项目 {project_name} 创建实际文件")

        # 根据任务类型和描述判断项目类型
        if task_type == "development" and any(keyword in description.lower() for keyword in ["web", "api", "网站", "应用"]):
            # Web应用项目
            result = project_generator.generate_web_app_project(
                project_name=project_name,
                description=description
            )
            logger.info(f"Web应用项目文件创建完成: {result['files']['success_count']} 个文件")

        elif "hello" in description.lower() or "python" in description.lower():
            # Python Hello World项目
            files = {
                "main.py": f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{project_name} - {description}
"""

def main():
    """主函数"""
    print("🐍 Hello, World!")
    print("你好，世界！")
    print(f"欢迎使用 {project_name}!")

    # 简单交互
    name = input("请输入您的姓名: ")
    if name.strip():
        print(f"你好，{{name}}！")

    print("程序运行完成！")

if __name__ == "__main__":
    main()
''',
                "README.md": f'''# {project_name}

{description}

## 运行方法

```bash
python main.py
```

## 项目说明

这是一个简单的Python程序，演示了基本的Python语法。

---

*项目创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
''',
                "requirements.txt": "# 项目依赖\n# 暂无外部依赖\n"
            }

            result = project_generator.create_multiple_files(project_name, files)
            logger.info(f"Python项目文件创建完成: {result['success_count']} 个文件")

        else:
            # 通用项目
            files = {
                "README.md": f'''# {project_name}

{description}

## 项目说明

{description}

## 使用方法

请根据项目需求添加具体的使用说明。

---

*项目创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
''',
                "main.py": f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{project_name} - {description}
"""

def main():
    """主函数"""
    print(f"欢迎使用 {project_name}!")
    print(f"项目描述: {description}")

if __name__ == "__main__":
    main()
'''
            }

            result = project_generator.create_multiple_files(project_name, files)
            logger.info(f"通用项目文件创建完成: {result['success_count']} 个文件")
    
    async def execute_project_advanced(self, project_config: Dict[str, Any], parallel: bool = False) -> Dict[str, Any]:
        """高级项目执行"""
        project_state = await self.create_project(project_config)
        
        # 如果指定了协作模式，设置到任务中
        task_info = {
            "id": project_state.project_id,
            "type": project_state.current_task.value,
            "description": project_state.description,
            "requirements": project_state.context or {},
            "collaboration_mode": project_config.get("collaboration_mode"),
            "specified_agents": project_config.get("specified_agents"),
            "parallel_execution": parallel
        }
        
        return await self.supervisor.process_task(task_info)
    
    async def execute_with_workflow(self, project_config: Dict[str, Any], 
                                  workflow_def: Dict[str, Any], parallel: bool = False) -> Dict[str, Any]:
        """使用自定义工作流执行项目"""
        # 这里应该集成工作流引擎
        # 暂时使用简化实现
        project_state = await self.create_project(project_config)
        
        task_info = {
            "id": project_state.project_id,
            "type": project_state.current_task.value,
            "description": project_state.description,
            "requirements": project_state.context or {},
            "workflow_definition": workflow_def,
            "parallel_execution": parallel
        }
        
        return await self.supervisor.process_task(task_info)
    
    def save_project(self, project_state: ProjectState, filepath: str) -> bool:
        """保存项目状态"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(project_state.to_dict(), f, ensure_ascii=False, indent=2, default=str)
            return True
        except Exception:
            return False
    
    def load_project(self, filepath: str) -> Optional[ProjectState]:
        """加载项目状态"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return ProjectState.from_dict(data)
        except Exception:
            return None


class AgentCommands:
    """智能体相关命令 v0.3.0"""
    
    def __init__(self, supervisor: SupervisorAgent):
        self.supervisor = supervisor
    
    def get_agents_info(self) -> Dict[str, Any]:
        """获取智能体信息"""
        return self.supervisor.get_agent_capabilities()
    
    def get_agent_capabilities(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """获取特定智能体能力"""
        all_capabilities = self.supervisor.get_agent_capabilities()
        return all_capabilities.get(agent_id)
    
    def recommend_agent_for_capability(self, capability: str) -> Optional[Dict[str, Any]]:
        """为特定能力推荐智能体"""
        try:
            capability_enum = AgentCapability(capability)
            best_agent = self.supervisor._find_best_agent_for_capability(capability_enum)
            
            if best_agent:
                score = self.supervisor.capability_matrix[best_agent][capability_enum]
                return {
                    "agent_id": best_agent,
                    "score": score,
                    "reason": f"在{capability}方面具有最高能力评分"
                }
            return None
        except ValueError:
            return None
    
    def get_collaboration_modes(self) -> List[str]:
        """获取可用协作模式"""
        return [mode.value for mode in CollaborationMode]
    
    def get_agent_performance_metrics(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """获取智能体性能指标"""
        if agent_id in self.supervisor.specialist_agents:
            agent = self.supervisor.specialist_agents[agent_id]
            return agent.performance_metrics
        return None


class SystemCommands:
    """系统相关命令 v0.3.0"""
    
    def __init__(self, supervisor: SupervisorAgent):
        self.supervisor = supervisor
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return self.supervisor.get_system_status()
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        status = self.supervisor.get_system_status()
        return {
            "name": "LangGraph Multi-Agent System",
            "version": "v0.3.0",
            "supervisor_version": status["supervisor_version"],
            "specialist_agents": status["specialist_agents"],
            "collaboration_modes": status["collaboration_modes"],
            "capabilities": status["capabilities"],
            "total_agents": status["total_agents"],
            "status": status["status"]
        }
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        try:
            status = self.supervisor.get_system_status()
            return {
                "valid": True,
                "supervisor_version": status["supervisor_version"],
                "agents_count": status["total_agents"],
                "agents": status["specialist_agents"],
                "model": status["model"]
            }
        except Exception as e:
            return {
                "valid": False,
                "error": str(e)
            }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        status = self.supervisor.get_system_status()
        perf_metrics = status["performance_metrics"]
        
        return {
            "total_tasks": perf_metrics["total_tasks"],
            "success_rate": perf_metrics["success_rate"],
            "average_execution_time": perf_metrics["average_execution_time"],
            "active_agents": perf_metrics["active_agents"],
            "system_health": "healthy" if perf_metrics["success_rate"] > 0.8 else "degraded"
        }


class WorkflowCommands:
    """工作流相关命令 v0.3.0"""
    
    def __init__(self, supervisor: SupervisorAgent):
        self.supervisor = supervisor
    
    def list_templates(self) -> List[Dict[str, str]]:
        """列出工作流模板"""
        # 这里应该从工作流引擎获取模板
        # 暂时返回预定义模板
        return [
            {
                "name": "software_development",
                "description": "软件开发标准流程：需求分析 -> 架构设计 -> 开发 -> 测试 -> 部署"
            },
            {
                "name": "code_review",
                "description": "代码审查流程：代码分析 -> 安全检查 -> 质量评估 -> 文档更新"
            },
            {
                "name": "research_analysis",
                "description": "研究分析流程：需求调研 -> 技术分析 -> 方案设计 -> 文档输出"
            },
            {
                "name": "deployment_pipeline",
                "description": "部署流水线：构建 -> 测试 -> 安全扫描 -> 部署 -> 监控"
            }
        ]
    
    def create_from_template(self, template_name: str) -> Dict[str, Any]:
        """从模板创建工作流"""
        templates = {
            "software_development": {
                "name": "软件开发工作流",
                "description": "标准软件开发流程",
                "steps": [
                    {
                        "id": "requirement_analysis",
                        "name": "需求分析",
                        "agent": "product_manager",
                        "type": "analysis"
                    },
                    {
                        "id": "architecture_design",
                        "name": "架构设计",
                        "agent": "architect",
                        "type": "design",
                        "depends_on": ["requirement_analysis"]
                    },
                    {
                        "id": "development",
                        "name": "开发实现",
                        "agent": "coder",
                        "type": "implementation",
                        "depends_on": ["architecture_design"]
                    },
                    {
                        "id": "testing",
                        "name": "测试验证",
                        "agent": "qa_engineer",
                        "type": "testing",
                        "depends_on": ["development"]
                    },
                    {
                        "id": "deployment",
                        "name": "部署发布",
                        "agent": "devops",
                        "type": "deployment",
                        "depends_on": ["testing"]
                    }
                ]
            },
            "code_review": {
                "name": "代码审查工作流",
                "description": "代码质量审查流程",
                "steps": [
                    {
                        "id": "code_analysis",
                        "name": "代码分析",
                        "agent": "coder",
                        "type": "analysis"
                    },
                    {
                        "id": "security_check",
                        "name": "安全检查",
                        "agent": "security",
                        "type": "security_audit",
                        "parallel": True
                    },
                    {
                        "id": "quality_assessment",
                        "name": "质量评估",
                        "agent": "qa_engineer",
                        "type": "quality_check",
                        "parallel": True
                    },
                    {
                        "id": "documentation",
                        "name": "文档更新",
                        "agent": "documentation",
                        "type": "documentation",
                        "depends_on": ["code_analysis", "security_check", "quality_assessment"]
                    }
                ]
            }
        }
        
        return templates.get(template_name, {})
    
    def create_interactive(self) -> Dict[str, Any]:
        """交互式创建工作流"""
        # 这里应该实现交互式工作流创建
        # 暂时返回基础模板
        return {
            "name": "自定义工作流",
            "description": "用户自定义工作流",
            "steps": [
                {
                    "id": "step_1",
                    "name": "步骤1",
                    "agent": "coder",
                    "type": "general"
                }
            ]
        }
    
    async def execute_workflow_file(self, workflow_file: str, input_data: Dict[str, Any], 
                                  parallel: bool = False) -> Dict[str, Any]:
        """执行工作流文件"""
        try:
            # 加载工作流定义
            with open(workflow_file, 'r', encoding='utf-8') as f:
                if workflow_file.endswith('.yaml') or workflow_file.endswith('.yml'):
                    workflow_def = yaml.safe_load(f)
                else:
                    workflow_def = json.load(f)
            
            # 构建任务信息
            task_info = {
                "id": f"workflow_{datetime.now().timestamp()}",
                "type": "workflow_execution",
                "description": workflow_def.get("description", "工作流执行"),
                "requirements": input_data,
                "workflow_definition": workflow_def,
                "parallel_execution": parallel
            }
            
            # 执行工作流
            return await self.supervisor.process_task(task_info)
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }


class MonitoringCommands:
    """监控相关命令 v0.3.0"""
    
    def __init__(self, supervisor: SupervisorAgent):
        self.supervisor = supervisor
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        status = self.supervisor.get_system_status()
        perf_metrics = status["performance_metrics"]
        
        return {
            "system_status": status["status"],
            "active_agents": status["total_agents"],
            "running_tasks": 0,  # 这里应该从任务调度器获取
            "performance_metrics": {
                "success_rate": perf_metrics["success_rate"],
                "avg_response_time": perf_metrics["average_execution_time"],
                "throughput": perf_metrics["total_tasks"] / max(1, perf_metrics["average_execution_time"]) * 60
            },
            "alerts": [],  # 这里应该从监控系统获取告警
            "timestamp": datetime.now().isoformat()
        }
    
    def get_agent_performance(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """获取智能体性能数据"""
        if agent_id in self.supervisor.specialist_agents:
            agent = self.supervisor.specialist_agents[agent_id]
            metrics = agent.performance_metrics.copy()
            metrics["agent_id"] = agent_id
            metrics["agent_name"] = agent.name
            return metrics
        return None
    
    def get_all_performance_metrics(self) -> Dict[str, Any]:
        """获取所有性能指标"""
        all_metrics = {}
        for agent_id, agent in self.supervisor.specialist_agents.items():
            all_metrics[agent_id] = agent.performance_metrics.copy()
            all_metrics[agent_id]["agent_name"] = agent.name
        return all_metrics
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        status = self.supervisor.get_system_status()
        perf_metrics = status["performance_metrics"]
        
        # 计算健康评分
        success_rate = perf_metrics["success_rate"]
        avg_time = perf_metrics["average_execution_time"]
        
        health_score = success_rate * 0.7 + (1 - min(avg_time / 60, 1)) * 0.3
        
        if health_score >= 0.8:
            health_status = "excellent"
        elif health_score >= 0.6:
            health_status = "good"
        elif health_score >= 0.4:
            health_status = "fair"
        else:
            health_status = "poor"
        
        return {
            "health_score": health_score,
            "health_status": health_status,
            "success_rate": success_rate,
            "average_response_time": avg_time,
            "active_agents": status["total_agents"],
            "recommendations": self._generate_health_recommendations(health_score, perf_metrics)
        }
    
    def _generate_health_recommendations(self, health_score: float, metrics: Dict[str, Any]) -> List[str]:
        """生成健康建议"""
        recommendations = []
        
        if metrics["success_rate"] < 0.8:
            recommendations.append("建议检查智能体配置和任务分配策略")
        
        if metrics["average_execution_time"] > 30:
            recommendations.append("建议优化任务执行流程或增加并行处理")
        
        if metrics["total_tasks"] == 0:
            recommendations.append("系统尚未处理任务，建议进行功能测试")
        
        if health_score < 0.6:
            recommendations.append("系统健康状况较差，建议进行全面检查")
        
        return recommendations


# 工具函数
class UtilityCommands:
    """工具命令 v0.3.0"""
    
    @staticmethod
    def format_timestamp(timestamp: datetime) -> str:
        """格式化时间戳"""
        return timestamp.strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def format_duration(start_time: datetime, end_time: datetime = None) -> str:
        """格式化持续时间"""
        if end_time is None:
            end_time = datetime.now()
        
        duration = end_time - start_time
        hours, remainder = divmod(duration.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f"{int(hours)}h {int(minutes)}m {int(seconds)}s"
        elif minutes > 0:
            return f"{int(minutes)}m {int(seconds)}s"
        else:
            return f"{int(seconds)}s"
    
    @staticmethod
    def validate_collaboration_mode(mode: str) -> bool:
        """验证协作模式"""
        try:
            CollaborationMode(mode)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_agent_capability(capability: str) -> bool:
        """验证智能体能力"""
        try:
            AgentCapability(capability)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def get_available_collaboration_modes() -> List[str]:
        """获取可用协作模式"""
        return [mode.value for mode in CollaborationMode]
    
    @staticmethod
    def get_available_capabilities() -> List[str]:
        """获取可用能力"""
        return [cap.value for cap in AgentCapability]
    
    @staticmethod
    def create_project_summary(project_state: ProjectState, execution_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建项目摘要 v0.3.0"""
        summary = {
            "name": project_state.project_name,
            "id": project_state.project_id,
            "task": project_state.current_task.value if project_state.current_task else None,
            "status": project_state.execution_status,
            "messages_count": len(project_state.messages),
            "agents_count": len(project_state.agent_status),
            "start_time": UtilityCommands.format_timestamp(project_state.start_time),
            "last_update": UtilityCommands.format_timestamp(project_state.last_update),
            "duration": UtilityCommands.format_duration(project_state.start_time)
        }
        
        if execution_result:
            summary.update({
                "execution_status": execution_result.get("status"),
                "collaboration_mode": execution_result.get("execution_summary", {}).get("collaboration_mode"),
                "participating_agents": execution_result.get("execution_summary", {}).get("total_agents"),
                "success_rate": execution_result.get("execution_summary", {}).get("successful_agents", 0) / 
                               max(1, execution_result.get("execution_summary", {}).get("total_agents", 1))
            })
        
        return summary