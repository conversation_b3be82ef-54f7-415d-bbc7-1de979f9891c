#!/usr/bin/env python3
"""
研究智能体 (ResearcherAgent) - v0.3.0 重构
负责对特定主题进行深入研究、分析和总结
"""

import os
import json
from typing import Dict, Any, List

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.tools import tool

from .base_v3 import SpecialistAgent, AgentCapability

# ============================================================================
# 研究智能体实现 (v3)
# ============================================================================

class ResearcherAgent(SpecialistAgent):
    """
    研究智能体 v3
    
    核心职责：
    - 对给定主题进行深入研究、分析并生成报告
    """
    
    def __init__(self, model: BaseLanguageModel, custom_tools: List = None, **kwargs):
        agent_tools = [
            self.conduct_research,
            self.analyze_data,
            self.summarize_findings,
        ]
        if custom_tools:
            agent_tools.extend(custom_tools)

        super().__init__(
            agent_id="researcher_001",
            name="研究分析师",
            capabilities=[
                AgentCapability.REQUIREMENT_ANALYSIS, 
                AgentCapability.ARCHITECTURE_DESIGN # 研究也可能涉及架构
            ],
            model=model,
            tools=agent_tools,
            **kwargs,
        )
        self.prompt_template = self._load_prompt_template()

    def _load_prompt_template(self) -> str:
        """从文件加载或使用默认的Prompt模板"""
        try:
            prompt_path = os.path.join(os.path.dirname(__file__), "..", "prompts", "researcher_agent.md")
            with open(prompt_path, "r", encoding="utf-8") as f:
                return f.read()
        except FileNotFoundError:
            return """As a professional research analyst, your task is to conduct in-depth research on the given topic.
Topic: {topic}
Research Requirements: {requirements}
Context: {context}

Please provide a detailed report that includes an overview, technical analysis, best practices, risk assessment, and recommendations.
Ensure the report is accurate, detailed, and actionable.
"""

    @tool
    async def conduct_research(self, topic: str, requirements: str, sources: List[str] = None) -> str:
        """
        对指定主题进行研究。
        Args:
            topic (str): 研究的主题.
            requirements (str): 具体的研究要求或问题.
            sources (List[str], optional): 建议的研究来源列表 (e.g., URLs, document IDs).
        Returns:
            str: JSON格式的研究报告摘要.
        """
        self.logger.info(f"开始研究主题: {topic}")
        
        # 实际应用中会调用工具（如网络搜索、文件读取）来收集信息
        # 此处简化为直接调用LLM
        
        context = f"Sources to consider: {', '.join(sources) if sources else 'General web search'}"
        prompt = self.prompt_template.format(
            topic=topic,
            requirements=requirements,
            context=context,
        )
        
        report_content = await self.model.ainvoke(prompt)
        
        summary = {
            "topic": topic,
            "key_findings": [finding.strip() for finding in report_content.split('\n') if finding.strip() and len(finding) > 10][:5],
            "full_report_preview": report_content[:500] + "...",
            "report": report_content
        }
        
        return json.dumps(summary, indent=2, ensure_ascii=False)

    @tool
    async def analyze_data(self, data: str, analysis_type: str = "trend_analysis") -> str:
        """
        分析给定的数据集。
        Args:
            data (str): JSON格式的数据集.
            analysis_type (str, optional): 分析类型 (e.g., 'trend_analysis', 'swot_analysis').
        Returns:
            str: JSON格式的分析结果.
        """
        self.logger.info(f"开始数据分析: {analysis_type}")
        
        prompt = f"""As a data analyst, perform a {analysis_type} on the following data.
Data:
{data}
Provide a JSON object with your analysis, including 'insights' and 'recommendations'."""
        
        analysis_result = await self.model.ainvoke(prompt)
        return analysis_result

    @tool
    async def summarize_findings(self, text: str, max_length: int = 200) -> str:
        """
        总结一段长文本的研究发现。
        Args:
            text (str): 需要总结的文本内容.
            max_length (int, optional): 摘要的最大长度（字数）.
        Returns:
            str: JSON格式的摘要.
        """
        self.logger.info("开始总结研究发现")
        
        prompt = f"Summarize the key findings from the following text in under {max_length} words:\n\n{text}"
        
        summary_text = await self.model.ainvoke(prompt)
        
        return json.dumps({"summary": summary_text}, ensure_ascii=False)

__all__ = ['ResearcherAgent']