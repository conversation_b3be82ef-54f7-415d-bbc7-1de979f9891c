#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试项目创建功能 - 验证文件是否正确存储在workspace
"""

import sys
import os
import asyncio
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def safe_print(message):
    """安全打印函数"""
    try:
        print(message)
    except UnicodeEncodeError:
        safe_msg = (message
                   .replace('✅', '[OK]')
                   .replace('❌', '[ERROR]')
                   .replace('🚀', '[START]')
                   .replace('🔧', '[TOOL]')
                   .replace('📁', '[DIR]')
                   .replace('📊', '[STATS]')
                   .replace('🤖', '[AGENT]'))
        print(safe_msg)

async def test_direct_project_generation():
    """直接测试项目生成器"""
    safe_print("🔧 测试1: 直接项目生成器")
    
    try:
        from langgraph_system.tools.project_generator import project_generator
        
        project_name = f"TestProject_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        description = "测试项目 - 验证文件创建功能"
        
        result = project_generator.generate_web_app_project(
            project_name=project_name,
            description=description
        )
        
        safe_print(f"✅ 项目生成成功: {project_name}")
        safe_print(f"   📁 项目目录: {result['structure']['project_dir']}")
        safe_print(f"   📊 文件统计: {result['files']['success_count']}/{result['files']['total_files']}")
        
        # 验证文件是否存在
        project_dir = Path(result['structure']['project_dir'])
        if project_dir.exists():
            safe_print(f"   ✅ 项目目录存在")
            
            # 检查关键文件
            key_files = ["README.md", "backend/main.py", "frontend/index.html", "requirements.txt"]
            existing_files = []
            
            for file_path in key_files:
                full_path = project_dir / file_path
                if full_path.exists():
                    existing_files.append(file_path)
                    size = full_path.stat().st_size
                    safe_print(f"     ✅ {file_path} ({size} bytes)")
                else:
                    safe_print(f"     ❌ {file_path}")
            
            success_rate = len(existing_files) / len(key_files) * 100
            safe_print(f"   📊 关键文件完整性: {success_rate:.1f}%")
            
            return success_rate >= 80
        else:
            safe_print(f"   ❌ 项目目录不存在")
            return False
            
    except Exception as e:
        safe_print(f"❌ 直接项目生成测试失败: {e}")
        return False

async def test_coder_agent_project_creation():
    """测试Coder智能体项目创建"""
    safe_print("\n🤖 测试2: Coder智能体项目创建")
    
    try:
        from langgraph_system.agents.coder_agent import CoderAgent
        from langgraph_system.llm.factory import LLMFactory
        from langgraph_system.llm.config import LLMConfig
        
        # 创建LLM配置和模型
        llm_config = LLMConfig()
        model = LLMFactory.create_client(llm_config)
        
        # 创建Coder智能体
        coder = CoderAgent(model)
        
        # 创建测试任务
        project_name = f"CoderTestProject_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        task = {
            "id": project_name,
            "type": "development",
            "description": "智能Web应用项目 - 支持用户管理和数据分析",
            "requirements": {
                "features": ["用户认证", "数据管理", "API接口", "Web界面"],
                "tech_stack": "FastAPI + HTML/CSS/JavaScript"
            }
        }
        
        # 执行任务
        result = await coder.execute_task(task)
        
        if result["status"] == "completed":
            safe_print(f"✅ Coder智能体任务完成")
            safe_print(f"   🤖 智能体ID: {result['agent_id']}")
            safe_print(f"   ⏱️  执行时间: {result['execution_time']:.2f}s")
            
            # 检查是否创建了项目文件
            if "project_files" in result:
                project_info = result["project_files"]
                project_dir = Path(project_info["structure"]["project_dir"])
                
                if project_dir.exists():
                    safe_print(f"   ✅ 项目目录已创建: {project_dir}")
                    
                    # 列出创建的文件
                    files = list(project_dir.rglob("*"))
                    file_count = len([f for f in files if f.is_file()])
                    safe_print(f"   📊 创建的文件数: {file_count}")
                    
                    return True
                else:
                    safe_print(f"   ❌ 项目目录未创建")
                    return False
            else:
                safe_print(f"   ⚠️  任务完成但未包含项目文件信息")
                return False
        else:
            safe_print(f"❌ Coder智能体任务失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        safe_print(f"❌ Coder智能体测试失败: {e}")
        return False

async def test_cli_project_creation():
    """测试CLI项目创建"""
    safe_print("\n⌨️  测试3: CLI项目创建")
    
    try:
        import subprocess
        
        project_name = f"CLITestProject_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 设置环境
        env = os.environ.copy()
        env.update({
            'PYTHONIOENCODING': 'utf-8',
            'PYTHONLEGACYWINDOWSSTDIO': '0'
        })
        
        # 运行CLI命令
        cmd = [
            sys.executable, "scripts/run_cli.py",
            "project", "create",
            "--name", project_name,
            "--description", "CLI测试项目 - 验证文件创建",
            "--task-type", "development"
        ]
        
        safe_print(f"   🚀 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60,
            env=env,
            encoding='utf-8',
            errors='replace',
            cwd=str(Path(__file__).parent.parent)
        )
        
        if result.returncode == 0:
            safe_print(f"   ✅ CLI命令执行成功")
            
            # 检查workspace中是否有项目文件
            workspace_dir = Path("workspace")
            project_dirs = [d for d in workspace_dir.iterdir() if d.is_dir() and project_name in d.name]
            
            if project_dirs:
                project_dir = project_dirs[0]
                safe_print(f"   ✅ 找到项目目录: {project_dir}")
                
                files = list(project_dir.rglob("*"))
                file_count = len([f for f in files if f.is_file()])
                safe_print(f"   📊 项目文件数: {file_count}")
                
                return file_count > 0
            else:
                safe_print(f"   ❌ 未找到项目目录")
                return False
        else:
            safe_print(f"   ❌ CLI命令执行失败")
            safe_print(f"   📝 错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        safe_print(f"   ❌ CLI命令超时")
        return False
    except Exception as e:
        safe_print(f"❌ CLI测试失败: {e}")
        return False

def check_workspace_structure():
    """检查workspace目录结构"""
    safe_print("\n📁 检查workspace目录结构")
    
    workspace_dir = Path("workspace")
    if not workspace_dir.exists():
        safe_print("   ❌ workspace目录不存在")
        return False
    
    safe_print(f"   ✅ workspace目录存在: {workspace_dir.absolute()}")
    
    # 列出所有项目
    projects = [d for d in workspace_dir.iterdir() if d.is_dir()]
    safe_print(f"   📊 项目数量: {len(projects)}")
    
    for project in projects:
        files = list(project.rglob("*"))
        file_count = len([f for f in files if f.is_file()])
        safe_print(f"     📁 {project.name}: {file_count} 个文件")
    
    return True

async def main():
    """主函数"""
    safe_print("🚀 项目创建功能测试")
    safe_print("=" * 60)
    
    # 切换到项目根目录
    os.chdir(Path(__file__).parent.parent)
    
    # 检查workspace结构
    workspace_ok = check_workspace_structure()
    
    tests = [
        ("直接项目生成器", test_direct_project_generation),
        ("Coder智能体创建", test_coder_agent_project_creation),
        ("CLI项目创建", test_cli_project_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            safe_print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 生成总结
    safe_print("\n" + "=" * 60)
    safe_print("📊 测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        safe_print(f"   {status} {test_name}")
    
    success_rate = passed / total * 100 if total > 0 else 0
    safe_print(f"\n🎯 总体结果: {passed}/{total} 通过 ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        safe_print("🎉 项目创建功能正常！文件已正确存储在workspace")
    elif success_rate >= 60:
        safe_print("👍 项目创建基本正常，部分功能需要优化")
    else:
        safe_print("⚠️ 项目创建存在问题，需要修复")
    
    # 最终检查workspace
    safe_print("\n📁 最终workspace检查:")
    final_check = check_workspace_structure()
    
    return 0 if success_rate >= 80 and final_check else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
