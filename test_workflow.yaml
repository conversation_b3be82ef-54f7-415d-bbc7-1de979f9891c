description: 标准软件开发流程
name: 软件开发工作流
steps:
- agent: product_manager
  id: requirement_analysis
  name: 需求分析
  type: analysis
- agent: architect
  depends_on:
  - requirement_analysis
  id: architecture_design
  name: 架构设计
  type: design
- agent: coder
  depends_on:
  - architecture_design
  id: development
  name: 开发实现
  type: implementation
- agent: qa_engineer
  depends_on:
  - development
  id: testing
  name: 测试验证
  type: testing
- agent: devops
  depends_on:
  - testing
  id: deployment
  name: 部署发布
  type: deployment
