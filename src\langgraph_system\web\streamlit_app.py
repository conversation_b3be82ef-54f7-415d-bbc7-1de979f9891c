"""
LangGraph多智能体系统 Streamlit Web界面
"""

import streamlit as st
import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, List
import traceback

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from langgraph_system.core import LangGraphSystem
from langgraph_system.core.registry import tool_registry, agent_registry
from langgraph_system.core.exceptions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AgentError, ToolError
from langgraph_system.states.project_state import TaskType
from langgraph_system.llm.config import LLMConfig

# 页面配置
st.set_page_config(
    page_title="LangGraph多智能体系统",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 0.75rem;
        border-radius: 0.25rem;
        border: 1px solid #c3e6cb;
    }
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 0.25rem;
        border: 1px solid #f5c6cb;
    }
    .info-message {
        background-color: #d1ecf1;
        color: #0c5460;
        padding: 0.75rem;
        border-radius: 0.25rem;
        border: 1px solid #bee5eb;
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
def init_session_state():
    """初始化会话状态"""
    if 'system' not in st.session_state:
        st.session_state.system = None
    if 'system_initialized' not in st.session_state:
        st.session_state.system_initialized = False
    if 'task_history' not in st.session_state:
        st.session_state.task_history = []
    if 'current_project' not in st.session_state:
        st.session_state.current_project = None

def initialize_system():
    """初始化LangGraph系统"""
    try:
        if not st.session_state.system_initialized:
            with st.spinner("正在初始化系统..."):
                system = LangGraphSystem()
                system.initialize()
                st.session_state.system = system
                st.session_state.system_initialized = True
            st.success("✅ 系统初始化成功！")
        return True
    except Exception as e:
        st.error(f"❌ 系统初始化失败: {str(e)}")
        return False

def display_system_status():
    """显示系统状态"""
    if not st.session_state.system_initialized:
        st.warning("⚠️ 系统未初始化")
        return
    
    system = st.session_state.system
    info = system.get_system_info()
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("### 📊 系统信息")
        st.markdown(f"""
        <div class="status-card">
            <strong>系统名称:</strong> {info['name']}<br>
            <strong>版本:</strong> {info['version']}<br>
            <strong>状态:</strong> {'🟢 运行中' if info['initialized'] else '🔴 未初始化'}
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("### 🤖 智能体")
        agents = info.get('available_agents', [])
        st.markdown(f"""
        <div class="status-card">
            <strong>可用智能体:</strong> {len(agents)}<br>
            {'<br>'.join([f"• {agent}" for agent in agents])}
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("### 🛠️ 工具")
        tools = info.get('available_tools', [])
        st.markdown(f"""
        <div class="status-card">
            <strong>可用工具:</strong> {len(tools)}<br>
            <strong>LLM提供商:</strong> {info.get('llm_provider', 'N/A')}
        </div>
        """, unsafe_allow_html=True)

def create_project_interface():
    """项目创建界面"""
    st.markdown("### 🚀 创建新项目")
    
    with st.form("create_project_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            project_name = st.text_input("项目名称", placeholder="输入项目名称...")
            task_type = st.selectbox(
                "任务类型",
                options=[t.value for t in TaskType],
                format_func=lambda x: {
                    'architecture': '🏗️ 架构设计',
                    'development': '💻 开发编码',
                    'research': '🔍 研究分析',
                    'testing': '🧪 测试验证',
                    'debugging': '🐛 调试修复',
                    'documentation': '📚 文档编写',
                    'deployment': '🚀 部署发布',
                    'coding': '⌨️ 代码实现'
                }.get(x, x)
            )
        
        with col2:
            description = st.text_area("项目描述", placeholder="详细描述项目需求...")
            requirements = st.text_area("具体要求", placeholder="输入具体的技术要求...")
        
        submitted = st.form_submit_button("🚀 创建项目", use_container_width=True)
        
        if submitted:
            if not project_name.strip():
                st.error("❌ 请输入项目名称")
                return
            
            if not description.strip():
                st.error("❌ 请输入项目描述")
                return
            
            create_project(project_name, task_type, description, requirements)

async def create_project_async(project_name: str, task_type: str, description: str, requirements: str):
    """异步创建项目"""
    system = st.session_state.system
    
    # 构建需求字典
    req_dict = {
        "description": description,
        "requirements": requirements,
        "created_at": datetime.now().isoformat()
    }
    
    # 创建项目
    result = await system.create_project(
        project_name=project_name,
        task_type=TaskType(task_type),
        description=description,
        requirements=req_dict
    )
    
    return result

def create_project(project_name: str, task_type: str, description: str, requirements: str):
    """创建项目"""
    try:
        with st.spinner(f"正在创建项目 '{project_name}'..."):
            # 运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(
                create_project_async(project_name, task_type, description, requirements)
            )
            loop.close()
        
        if result.get('status') == 'success':
            st.success(f"✅ 项目 '{project_name}' 创建成功！")
            
            # 保存到历史记录
            task_record = {
                "project_name": project_name,
                "task_type": task_type,
                "description": description,
                "requirements": requirements,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            st.session_state.task_history.append(task_record)
            st.session_state.current_project = task_record
            
            # 显示结果
            with st.expander("📋 查看执行结果", expanded=True):
                st.json(result)
        else:
            st.error(f"❌ 项目创建失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        st.error(f"❌ 创建项目时发生错误: {str(e)}")
        with st.expander("🔍 错误详情"):
            st.code(traceback.format_exc())

def tool_management_interface():
    """工具管理界面"""
    st.markdown("### 🛠️ 工具管理")
    
    tab1, tab2, tab3 = st.tabs(["📋 工具列表", "🧪 工具测试", "📊 工具统计"])
    
    with tab1:
        tools = tool_registry.list_tools()
        st.markdown(f"**可用工具数量:** {len(tools)}")
        
        for tool_name in tools:
            with st.expander(f"🔧 {tool_name}"):
                metadata = tool_registry.get_metadata(tool_name)
                st.markdown(f"**描述:** {metadata.get('description', '无描述')}")
                
                if 'parameters' in metadata:
                    st.markdown("**参数:**")
                    st.json(metadata['parameters'])
    
    with tab2:
        st.markdown("#### 测试工具功能")
        
        tool_name = st.selectbox("选择工具", tool_registry.list_tools())
        
        if tool_name:
            metadata = tool_registry.get_metadata(tool_name)
            st.markdown(f"**工具描述:** {metadata.get('description', '无描述')}")
            
            # 根据工具类型提供不同的测试界面
            if tool_name == "write_file":
                test_write_file_tool()
            elif tool_name == "read_file":
                test_read_file_tool()
            elif tool_name == "list_files":
                test_list_files_tool()
            else:
                st.info(f"工具 '{tool_name}' 的测试界面正在开发中...")
    
    with tab3:
        display_tool_statistics()

def test_write_file_tool():
    """测试文件写入工具"""
    st.markdown("##### 📝 测试文件写入")
    
    with st.form("write_file_test"):
        file_path = st.text_input("文件路径", value="test_output.txt")
        content = st.text_area("文件内容", value="这是一个测试文件\n由Streamlit界面创建")
        
        if st.form_submit_button("💾 写入文件"):
            try:
                result = tool_registry.execute_tool("write_file", file_path=file_path, content=content)
                st.success(f"✅ {result}")
            except Exception as e:
                st.error(f"❌ 写入失败: {str(e)}")

def test_read_file_tool():
    """测试文件读取工具"""
    st.markdown("##### 📖 测试文件读取")
    
    with st.form("read_file_test"):
        file_path = st.text_input("文件路径", value="test_output.txt")
        
        if st.form_submit_button("📂 读取文件"):
            try:
                content = tool_registry.execute_tool("read_file", file_path=file_path)
                st.success("✅ 文件读取成功")
                st.code(content, language="text")
            except Exception as e:
                st.error(f"❌ 读取失败: {str(e)}")

def test_list_files_tool():
    """测试文件列表工具"""
    st.markdown("##### 📁 测试文件列表")
    
    with st.form("list_files_test"):
        directory = st.text_input("目录路径", value=".")
        
        if st.form_submit_button("📋 列出文件"):
            try:
                files = tool_registry.execute_tool("list_files", directory_path=directory)
                st.success(f"✅ 找到 {len(files)} 个文件/目录")
                
                for file in files:
                    st.write(f"📄 {file}")
            except Exception as e:
                st.error(f"❌ 列出文件失败: {str(e)}")

def display_tool_statistics():
    """显示工具统计信息"""
    tools = tool_registry.list_tools()
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric("总工具数", len(tools))
        
        # 按类型分类
        tool_types = {}
        for tool_name in tools:
            metadata = tool_registry.get_metadata(tool_name)
            tool_type = "文件操作" if "file" in tool_name else "其他"
            tool_types[tool_type] = tool_types.get(tool_type, 0) + 1
        
        st.markdown("**工具分类:**")
        for tool_type, count in tool_types.items():
            st.write(f"• {tool_type}: {count}")
    
    with col2:
        st.markdown("**工具列表:**")
        for tool_name in tools:
            metadata = tool_registry.get_metadata(tool_name)
            desc = metadata.get('description', '无描述')[:50]
            st.write(f"🔧 **{tool_name}**: {desc}...")

def agent_management_interface():
    """智能体管理界面"""
    st.markdown("### 🤖 智能体管理")
    
    tab1, tab2 = st.tabs(["📋 智能体列表", "🎯 智能体测试"])
    
    with tab1:
        agents = agent_registry.list_agents()
        st.markdown(f"**可用智能体数量:** {len(agents)}")
        
        for agent_name in agents:
            with st.expander(f"🤖 {agent_name}"):
                metadata = agent_registry.get_metadata(agent_name)
                st.markdown(f"**描述:** {metadata.get('description', '无描述')}")
                
                if 'capabilities' in metadata:
                    st.markdown("**能力:**")
                    for cap in metadata['capabilities']:
                        st.write(f"• {cap}")
                
                if 'supported_tasks' in metadata:
                    st.markdown("**支持的任务类型:**")
                    for task in metadata['supported_tasks']:
                        st.write(f"• {task}")
    
    with tab2:
        st.markdown("#### 测试智能体功能")
        st.info("智能体测试功能需要LLM API配置，请确保.env文件中的API密钥正确配置。")

def task_history_interface():
    """任务历史界面"""
    st.markdown("### 📚 任务历史")
    
    if not st.session_state.task_history:
        st.info("暂无任务历史记录")
        return
    
    for i, task in enumerate(reversed(st.session_state.task_history)):
        with st.expander(f"📋 {task['project_name']} - {task['task_type']} ({task['timestamp'][:19]})"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**项目信息:**")
                st.write(f"**名称:** {task['project_name']}")
                st.write(f"**类型:** {task['task_type']}")
                st.write(f"**描述:** {task['description']}")
            
            with col2:
                st.markdown("**执行结果:**")
                result = task.get('result', {})
                status = result.get('status', 'unknown')
                
                if status == 'success':
                    st.success("✅ 执行成功")
                else:
                    st.error("❌ 执行失败")
                
                with st.expander("查看详细结果"):
                    st.json(result)

def main():
    """主函数"""
    # 初始化会话状态
    init_session_state()
    
    # 页面标题
    st.markdown('<h1 class="main-header">🤖 LangGraph多智能体系统</h1>', unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.markdown("## 🎛️ 控制面板")
        
        # 系统初始化
        if st.button("🔄 初始化系统", use_container_width=True):
            initialize_system()
        
        # 导航菜单
        page = st.selectbox(
            "选择功能页面",
            ["📊 系统状态", "🚀 创建项目", "🛠️ 工具管理", "🤖 智能体管理", "📚 任务历史"]
        )
        
        # 系统信息
        if st.session_state.system_initialized:
            st.success("✅ 系统已初始化")
        else:
            st.warning("⚠️ 系统未初始化")
    
    # 主内容区域
    if page == "📊 系统状态":
        display_system_status()
    elif page == "🚀 创建项目":
        if st.session_state.system_initialized:
            create_project_interface()
        else:
            st.warning("⚠️ 请先初始化系统")
    elif page == "🛠️ 工具管理":
        if st.session_state.system_initialized:
            tool_management_interface()
        else:
            st.warning("⚠️ 请先初始化系统")
    elif page == "🤖 智能体管理":
        if st.session_state.system_initialized:
            agent_management_interface()
        else:
            st.warning("⚠️ 请先初始化系统")
    elif page == "📚 任务历史":
        task_history_interface()

if __name__ == "__main__":
    main()