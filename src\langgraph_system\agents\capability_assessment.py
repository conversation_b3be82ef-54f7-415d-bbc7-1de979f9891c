#!/usr/bin/env python3
"""
智能体能力评估系统 (Agent Capability Assessment)
负责评估智能体的能力、性能和适配性
"""

import asyncio
import json
import uuid
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging

# from .base_agent import (
#     IAgent, BaseAgent, AgentMessage, MessageType, Priority,
#     TaskRequest, TaskResult, AgentCapability
# )

# Placeholder classes to avoid import errors
class IAgent: pass
class BaseAgent: pass
class AgentMessage: pass
class MessageType: pass
class Priority: pass
class TaskRequest: pass
class TaskResult: pass
class AgentCapability: pass

logger = logging.getLogger(__name__)

# ============================================================================
# 能力评估数据模型
# ============================================================================

class AssessmentType(Enum):
    """评估类型枚举"""
    CAPABILITY = "capability"  # 能力评估
    PERFORMANCE = "performance"  # 性能评估
    RELIABILITY = "reliability"  # 可靠性评估
    COMPATIBILITY = "compatibility"  # 兼容性评估
    SCALABILITY = "scalability"  # 可扩展性评估

class AssessmentStatus(Enum):
    """评估状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class CapabilityLevel(Enum):
    """能力等级枚举"""
    NOVICE = "novice"      # 新手
    INTERMEDIATE = "intermediate"  # 中级
    ADVANCED = "advanced"  # 高级
    EXPERT = "expert"      # 专家
    MASTER = "master"      # 大师

class AssessmentMetric(Enum):
    """评估指标枚举"""
    ACCURACY = "accuracy"          # 准确性
    SPEED = "speed"               # 速度
    THROUGHPUT = "throughput"     # 吞吐量
    LATENCY = "latency"           # 延迟
    ERROR_RATE = "error_rate"     # 错误率
    RESOURCE_USAGE = "resource_usage"  # 资源使用率
    AVAILABILITY = "availability"  # 可用性
    CONSISTENCY = "consistency"    # 一致性

@dataclass
class AssessmentCriteria:
    """评估标准"""
    metric: AssessmentMetric
    weight: float  # 权重 (0-1)
    threshold: float  # 阈值
    target_value: Optional[float] = None  # 目标值
    measurement_unit: str = ""  # 测量单位
    description: str = ""  # 描述

@dataclass
class AssessmentResult:
    """评估结果"""
    metric: AssessmentMetric
    value: float
    score: float  # 评分 (0-100)
    passed: bool  # 是否通过
    measurement_unit: str = ""
    details: Dict[str, Any] = None

@dataclass
class CapabilityAssessment:
    """能力评估"""
    id: str
    agent_id: str
    capability_name: str
    assessment_type: AssessmentType
    criteria: List[AssessmentCriteria]
    results: List[AssessmentResult]
    overall_score: float
    capability_level: CapabilityLevel
    status: AssessmentStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration: Optional[float] = None  # 评估持续时间（秒）
    metadata: Dict[str, Any] = None

@dataclass
class BenchmarkTask:
    """基准测试任务"""
    id: str
    name: str
    description: str
    task_type: str
    parameters: Dict[str, Any]
    expected_output: Any
    timeout: int = 30  # 超时时间（秒）
    difficulty_level: int = 1  # 难度等级 (1-5)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    execution_time: float
    memory_usage: float
    cpu_usage: float
    success_rate: float
    error_count: int
    throughput: float
    latency: float

# ============================================================================
# 能力评估系统
# ============================================================================

class AgentCapabilityAssessment:
    """
    智能体能力评估系统
    负责评估智能体的各项能力和性能指标
    """
    
    def __init__(self):
        self.assessments: Dict[str, CapabilityAssessment] = {}
        self.benchmark_tasks: Dict[str, List[BenchmarkTask]] = {}
        self.assessment_history: Dict[str, List[CapabilityAssessment]] = {}
        self.capability_standards: Dict[str, Dict[str, Any]] = {}
        
        # 初始化基准测试任务
        self._initialize_benchmark_tasks()
        
        # 初始化能力标准
        self._initialize_capability_standards()
    
    def _initialize_benchmark_tasks(self):
        """初始化基准测试任务"""
        # 架构师智能体基准任务
        architect_tasks = [
            BenchmarkTask(
                id="arch_design_simple",
                name="简单系统设计",
                description="设计一个简单的Web应用架构",
                task_type="design_system",
                parameters={
                    "requirements": {
                        "name": "简单博客系统",
                        "type": "web_application",
                        "expected_users": 1000
                    }
                },
                expected_output={"components_count": 3, "patterns_used": ["mvc"]},
                difficulty_level=1
            ),
            BenchmarkTask(
                id="arch_design_complex",
                name="复杂系统设计",
                description="设计一个高并发电商平台架构",
                task_type="design_system",
                parameters={
                    "requirements": {
                        "name": "电商平台",
                        "type": "web_application",
                        "expected_users": 100000,
                        "performance_requirements": "高并发、低延迟"
                    }
                },
                expected_output={"components_count": 8, "patterns_used": ["microservices", "cqrs"]},
                difficulty_level=4
            )
        ]
        self.benchmark_tasks["ArchitectAgent"] = architect_tasks
        
        # 产品经理智能体基准任务
        pm_tasks = [
            BenchmarkTask(
                id="pm_requirements_basic",
                name="基础需求分析",
                description="分析简单产品需求",
                task_type="analyze_requirements",
                parameters={
                    "requirements": [
                        {
                            "id": "req_001",
                            "title": "用户登录",
                            "description": "用户可以登录系统",
                            "type": "functional"
                        }
                    ]
                },
                expected_output={"total_requirements": 1, "requirements_by_priority": {"high": 1}},
                difficulty_level=1
            ),
            BenchmarkTask(
                id="pm_user_stories_advanced",
                name="高级用户故事创建",
                description="为复杂功能创建详细用户故事",
                task_type="create_user_stories",
                parameters={
                    "requirements": [
                        {
                            "id": "req_001",
                            "title": "支付系统",
                            "description": "支持多种支付方式的支付系统",
                            "priority": "critical"
                        }
                    ]
                },
                expected_output={"total_stories": 5, "total_story_points": 20},
                difficulty_level=3
            )
        ]
        self.benchmark_tasks["ProductManagerAgent"] = pm_tasks
        
        # DevOps智能体基准任务
        devops_tasks = [
            BenchmarkTask(
                id="devops_cicd_basic",
                name="基础CI/CD设置",
                description="为简单项目设置CI/CD管道",
                task_type="setup_cicd",
                parameters={
                    "project": {
                        "name": "simple-app",
                        "type": "web_application",
                        "language": "python"
                    },
                    "platform": "jenkins"
                },
                expected_output={"stages_count": 4, "estimated_build_time": 10},
                difficulty_level=2
            )
        ]
        self.benchmark_tasks["DevOpsAgent"] = devops_tasks
        
        # QA智能体基准任务
        qa_tasks = [
            BenchmarkTask(
                id="qa_test_strategy_basic",
                name="基础测试策略设计",
                description="为简单项目设计测试策略",
                task_type="design_test_strategy",
                parameters={
                    "project_name": "测试项目",
                    "requirements": {"type": "web_application", "complexity": "low"}
                },
                expected_output={"test_types_covered": 3, "estimated_duration": 20},
                difficulty_level=1
            )
        ]
        self.benchmark_tasks["QAAgent"] = qa_tasks
        
        # 文档智能体基准任务
        doc_tasks = [
            BenchmarkTask(
                id="doc_api_basic",
                name="基础API文档生成",
                description="生成简单API的文档",
                task_type="generate_api_docs",
                parameters={
                    "api_spec": {
                        "name": "用户API",
                        "version": "1.0.0",
                        "endpoints": [
                            {
                                "path": "/users",
                                "method": "GET",
                                "description": "获取用户列表"
                            }
                        ]
                    }
                },
                expected_output={"endpoints_count": 1, "format": "markdown"},
                difficulty_level=1
            )
        ]
        self.benchmark_tasks["DocumentationAgent"] = doc_tasks
    
    def _initialize_capability_standards(self):
        """初始化能力标准"""
        # 通用能力标准
        self.capability_standards = {
            "accuracy": {
                "novice": {"min_score": 0, "max_score": 40},
                "intermediate": {"min_score": 40, "max_score": 60},
                "advanced": {"min_score": 60, "max_score": 80},
                "expert": {"min_score": 80, "max_score": 95},
                "master": {"min_score": 95, "max_score": 100}
            },
            "speed": {
                "novice": {"max_time": 60},      # 60秒以内
                "intermediate": {"max_time": 30}, # 30秒以内
                "advanced": {"max_time": 15},     # 15秒以内
                "expert": {"max_time": 10},       # 10秒以内
                "master": {"max_time": 5}         # 5秒以内
            },
            "reliability": {
                "novice": {"min_success_rate": 0.5},
                "intermediate": {"min_success_rate": 0.7},
                "advanced": {"min_success_rate": 0.85},
                "expert": {"min_success_rate": 0.95},
                "master": {"min_success_rate": 0.99}
            }
        }
    
    async def assess_agent_capability(self, agent: IAgent, capability_name: str,
                                    assessment_type: AssessmentType = AssessmentType.CAPABILITY) -> str:
        """评估智能体能力"""
        try:
            assessment_id = f"assessment_{uuid.uuid4().hex[:8]}"
            
            # 获取智能体类型对应的基准任务
            agent_type = agent.__class__.__name__
            if agent_type not in self.benchmark_tasks:
                raise ValueError(f"没有为智能体类型 {agent_type} 定义基准任务")
            
            # 创建评估标准
            criteria = self._create_assessment_criteria(assessment_type, capability_name)
            
            # 创建评估对象
            assessment = CapabilityAssessment(
                id=assessment_id,
                agent_id=agent.agent_id,
                capability_name=capability_name,
                assessment_type=assessment_type,
                criteria=criteria,
                results=[],
                overall_score=0.0,
                capability_level=CapabilityLevel.NOVICE,
                status=AssessmentStatus.PENDING,
                created_at=datetime.now(),
                metadata={"agent_type": agent_type}
            )
            
            self.assessments[assessment_id] = assessment
            
            # 开始评估
            await self._execute_assessment(agent, assessment)
            
            logger.info(f"智能体 {agent.agent_id} 的能力评估 {assessment_id} 完成")
            return assessment_id
            
        except Exception as e:
            logger.error(f"评估智能体能力失败: {e}")
            raise
    
    def _create_assessment_criteria(self, assessment_type: AssessmentType, 
                                  capability_name: str) -> List[AssessmentCriteria]:
        """创建评估标准"""
        criteria = []
        
        if assessment_type == AssessmentType.CAPABILITY:
            # 能力评估标准
            criteria.extend([
                AssessmentCriteria(
                    metric=AssessmentMetric.ACCURACY,
                    weight=0.4,
                    threshold=60.0,
                    target_value=90.0,
                    measurement_unit="%",
                    description="任务完成的准确性"
                ),
                AssessmentCriteria(
                    metric=AssessmentMetric.SPEED,
                    weight=0.3,
                    threshold=30.0,
                    target_value=10.0,
                    measurement_unit="秒",
                    description="任务完成速度"
                ),
                AssessmentCriteria(
                    metric=AssessmentMetric.CONSISTENCY,
                    weight=0.3,
                    threshold=70.0,
                    target_value=95.0,
                    measurement_unit="%",
                    description="多次执行结果的一致性"
                )
            ])
        
        elif assessment_type == AssessmentType.PERFORMANCE:
            # 性能评估标准
            criteria.extend([
                AssessmentCriteria(
                    metric=AssessmentMetric.THROUGHPUT,
                    weight=0.4,
                    threshold=10.0,
                    target_value=50.0,
                    measurement_unit="任务/分钟",
                    description="单位时间处理任务数量"
                ),
                AssessmentCriteria(
                    metric=AssessmentMetric.LATENCY,
                    weight=0.3,
                    threshold=5.0,
                    target_value=1.0,
                    measurement_unit="秒",
                    description="任务响应延迟"
                ),
                AssessmentCriteria(
                    metric=AssessmentMetric.RESOURCE_USAGE,
                    weight=0.3,
                    threshold=80.0,
                    target_value=50.0,
                    measurement_unit="%",
                    description="系统资源使用率"
                )
            ])
        
        elif assessment_type == AssessmentType.RELIABILITY:
            # 可靠性评估标准
            criteria.extend([
                AssessmentCriteria(
                    metric=AssessmentMetric.AVAILABILITY,
                    weight=0.4,
                    threshold=95.0,
                    target_value=99.9,
                    measurement_unit="%",
                    description="服务可用性"
                ),
                AssessmentCriteria(
                    metric=AssessmentMetric.ERROR_RATE,
                    weight=0.6,
                    threshold=5.0,
                    target_value=0.1,
                    measurement_unit="%",
                    description="错误发生率"
                )
            ])
        
        return criteria
    
    async def _execute_assessment(self, agent: IAgent, assessment: CapabilityAssessment):
        """执行评估"""
        try:
            assessment.status = AssessmentStatus.RUNNING
            assessment.started_at = datetime.now()
            
            agent_type = agent.__class__.__name__
            benchmark_tasks = self.benchmark_tasks.get(agent_type, [])
            
            if not benchmark_tasks:
                raise ValueError(f"没有为智能体类型 {agent_type} 定义基准任务")
            
            # 执行基准测试
            test_results = []
            for task in benchmark_tasks:
                result = await self._execute_benchmark_task(agent, task)
                test_results.append(result)
            
            # 计算评估结果
            assessment.results = await self._calculate_assessment_results(
                assessment.criteria, test_results
            )
            
            # 计算总体评分
            assessment.overall_score = self._calculate_overall_score(
                assessment.criteria, assessment.results
            )
            
            # 确定能力等级
            assessment.capability_level = self._determine_capability_level(
                assessment.overall_score, test_results
            )
            
            assessment.status = AssessmentStatus.COMPLETED
            assessment.completed_at = datetime.now()
            assessment.duration = (assessment.completed_at - assessment.started_at).total_seconds()
            
            # 保存到历史记录
            if agent.agent_id not in self.assessment_history:
                self.assessment_history[agent.agent_id] = []
            self.assessment_history[agent.agent_id].append(assessment)
            
        except Exception as e:
            assessment.status = AssessmentStatus.FAILED
            assessment.completed_at = datetime.now()
            logger.error(f"执行评估失败: {e}")
            raise
    
    async def _execute_benchmark_task(self, agent: IAgent, task: BenchmarkTask) -> Dict[str, Any]:
        """执行基准测试任务"""
        try:
            start_time = datetime.now()
            
            # 创建任务请求
            task_request = TaskRequest(
                type=task.task_type,
                description=task.description,
                parameters=task.parameters,
                priority=Priority.HIGH
            )
            
            # 执行任务
            task_result = await asyncio.wait_for(
                agent.process_task(task_request),
                timeout=task.timeout
            )
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # 评估结果质量
            quality_score = self._evaluate_task_quality(
                task_result.result, task.expected_output, task.difficulty_level
            )
            
            return {
                "task_id": task.id,
                "task_name": task.name,
                "difficulty_level": task.difficulty_level,
                "execution_time": execution_time,
                "success": task_result.status == "completed",
                "quality_score": quality_score,
                "result": task_result.result,
                "expected": task.expected_output
            }
            
        except asyncio.TimeoutError:
            logger.warning(f"基准任务 {task.id} 执行超时")
            return {
                "task_id": task.id,
                "task_name": task.name,
                "difficulty_level": task.difficulty_level,
                "execution_time": task.timeout,
                "success": False,
                "quality_score": 0.0,
                "error": "执行超时"
            }
        except Exception as e:
            logger.error(f"执行基准任务 {task.id} 失败: {e}")
            return {
                "task_id": task.id,
                "task_name": task.name,
                "difficulty_level": task.difficulty_level,
                "execution_time": 0.0,
                "success": False,
                "quality_score": 0.0,
                "error": str(e)
            }
    
    def _evaluate_task_quality(self, actual_result: Any, expected_result: Any, 
                              difficulty_level: int) -> float:
        """评估任务结果质量"""
        try:
            if not isinstance(actual_result, dict) or not isinstance(expected_result, dict):
                return 50.0  # 默认分数
            
            score = 0.0
            total_weight = 0.0
            
            for key, expected_value in expected_result.items():
                weight = 1.0
                if key in actual_result:
                    actual_value = actual_result[key]
                    
                    if isinstance(expected_value, (int, float)):
                        # 数值比较
                        if actual_value >= expected_value:
                            score += weight * 100
                        else:
                            ratio = actual_value / expected_value if expected_value > 0 else 0
                            score += weight * ratio * 100
                    elif isinstance(expected_value, list):
                        # 列表比较
                        if isinstance(actual_value, list):
                            intersection = set(actual_value) & set(expected_value)
                            union = set(actual_value) | set(expected_value)
                            if union:
                                jaccard_similarity = len(intersection) / len(union)
                                score += weight * jaccard_similarity * 100
                            else:
                                score += weight * 100
                        else:
                            score += weight * 50  # 部分分数
                    else:
                        # 其他类型的精确匹配
                        if actual_value == expected_value:
                            score += weight * 100
                        else:
                            score += weight * 30  # 部分分数
                
                total_weight += weight
            
            # 根据难度等级调整分数
            if total_weight > 0:
                base_score = score / total_weight
                # 难度越高，要求越严格
                difficulty_factor = 1.0 - (difficulty_level - 1) * 0.1
                return max(0.0, min(100.0, base_score * difficulty_factor))
            
            return 50.0
            
        except Exception as e:
            logger.error(f"评估任务质量失败: {e}")
            return 0.0
    
    async def _calculate_assessment_results(self, criteria: List[AssessmentCriteria],
                                          test_results: List[Dict[str, Any]]) -> List[AssessmentResult]:
        """计算评估结果"""
        results = []
        
        for criterion in criteria:
            if criterion.metric == AssessmentMetric.ACCURACY:
                # 计算准确性
                quality_scores = [r.get("quality_score", 0) for r in test_results if r.get("success", False)]
                if quality_scores:
                    value = statistics.mean(quality_scores)
                else:
                    value = 0.0
                
                score = min(100.0, (value / criterion.target_value) * 100) if criterion.target_value else value
                passed = value >= criterion.threshold
                
            elif criterion.metric == AssessmentMetric.SPEED:
                # 计算速度
                execution_times = [r.get("execution_time", float('inf')) for r in test_results if r.get("success", False)]
                if execution_times:
                    value = statistics.mean(execution_times)
                else:
                    value = float('inf')
                
                # 速度越快分数越高
                if criterion.target_value and value <= criterion.target_value:
                    score = 100.0
                elif value <= criterion.threshold:
                    score = 80.0
                else:
                    score = max(0.0, 100.0 - (value - criterion.threshold) * 2)
                
                passed = value <= criterion.threshold
                
            elif criterion.metric == AssessmentMetric.CONSISTENCY:
                # 计算一致性
                quality_scores = [r.get("quality_score", 0) for r in test_results if r.get("success", False)]
                if len(quality_scores) > 1:
                    std_dev = statistics.stdev(quality_scores)
                    mean_score = statistics.mean(quality_scores)
                    # 一致性 = 100 - (标准差/均值) * 100
                    value = max(0.0, 100.0 - (std_dev / mean_score * 100)) if mean_score > 0 else 0.0
                else:
                    value = 100.0 if quality_scores else 0.0
                
                score = value
                passed = value >= criterion.threshold
                
            else:
                # 其他指标的默认处理
                value = 75.0
                score = 75.0
                passed = True
            
            result = AssessmentResult(
                metric=criterion.metric,
                value=value,
                score=score,
                passed=passed,
                measurement_unit=criterion.measurement_unit,
                details={"test_results": test_results}
            )
            
            results.append(result)
        
        return results
    
    def _calculate_overall_score(self, criteria: List[AssessmentCriteria],
                               results: List[AssessmentResult]) -> float:
        """计算总体评分"""
        if not criteria or not results:
            return 0.0
        
        weighted_score = 0.0
        total_weight = 0.0
        
        for criterion, result in zip(criteria, results):
            weighted_score += criterion.weight * result.score
            total_weight += criterion.weight
        
        return weighted_score / total_weight if total_weight > 0 else 0.0
    
    def _determine_capability_level(self, overall_score: float, 
                                  test_results: List[Dict[str, Any]]) -> CapabilityLevel:
        """确定能力等级"""
        # 基于总体评分确定等级
        if overall_score >= 95:
            return CapabilityLevel.MASTER
        elif overall_score >= 80:
            return CapabilityLevel.EXPERT
        elif overall_score >= 60:
            return CapabilityLevel.ADVANCED
        elif overall_score >= 40:
            return CapabilityLevel.INTERMEDIATE
        else:
            return CapabilityLevel.NOVICE
    
    async def batch_assess_agents(self, agents: List[IAgent], 
                                 capability_name: str = "general") -> Dict[str, str]:
        """批量评估智能体"""
        assessment_ids = {}
        
        for agent in agents:
            try:
                assessment_id = await self.assess_agent_capability(agent, capability_name)
                assessment_ids[agent.agent_id] = assessment_id
            except Exception as e:
                logger.error(f"评估智能体 {agent.agent_id} 失败: {e}")
                assessment_ids[agent.agent_id] = None
        
        return assessment_ids
    
    def get_assessment_result(self, assessment_id: str) -> Optional[CapabilityAssessment]:
        """获取评估结果"""
        return self.assessments.get(assessment_id)
    
    def get_agent_assessment_history(self, agent_id: str) -> List[CapabilityAssessment]:
        """获取智能体评估历史"""
        return self.assessment_history.get(agent_id, [])
    
    def compare_agents(self, agent_ids: List[str], 
                      capability_name: str = None) -> Dict[str, Any]:
        """比较智能体能力"""
        comparison_data = {
            "agents": [],
            "metrics": [],
            "summary": {}
        }
        
        for agent_id in agent_ids:
            agent_assessments = self.get_agent_assessment_history(agent_id)
            
            if capability_name:
                # 筛选特定能力的评估
                relevant_assessments = [
                    a for a in agent_assessments 
                    if a.capability_name == capability_name
                ]
            else:
                relevant_assessments = agent_assessments
            
            if relevant_assessments:
                # 获取最新评估
                latest_assessment = max(relevant_assessments, key=lambda x: x.created_at)
                
                agent_data = {
                    "agent_id": agent_id,
                    "overall_score": latest_assessment.overall_score,
                    "capability_level": latest_assessment.capability_level.value,
                    "assessment_date": latest_assessment.created_at.isoformat(),
                    "results": {r.metric.value: r.score for r in latest_assessment.results}
                }
                
                comparison_data["agents"].append(agent_data)
        
        # 生成比较指标
        if comparison_data["agents"]:
            scores = [agent["overall_score"] for agent in comparison_data["agents"]]
            comparison_data["summary"] = {
                "best_agent": max(comparison_data["agents"], key=lambda x: x["overall_score"])["agent_id"],
                "average_score": statistics.mean(scores),
                "score_range": {"min": min(scores), "max": max(scores)},
                "agents_count": len(comparison_data["agents"])
            }
            
            # 收集所有指标
            all_metrics = set()
            for agent in comparison_data["agents"]:
                all_metrics.update(agent["results"].keys())
            comparison_data["metrics"] = list(all_metrics)
        
        return comparison_data
    
    def generate_assessment_report(self, assessment_id: str) -> Dict[str, Any]:
        """生成评估报告"""
        assessment = self.get_assessment_result(assessment_id)
        if not assessment:
            raise ValueError(f"评估 {assessment_id} 不存在")
        
        report = {
            "assessment_id": assessment_id,
            "agent_id": assessment.agent_id,
            "capability_name": assessment.capability_name,
            "assessment_type": assessment.assessment_type.value,
            "overall_score": assessment.overall_score,
            "capability_level": assessment.capability_level.value,
            "status": assessment.status.value,
            "duration": assessment.duration,
            "created_at": assessment.created_at.isoformat(),
            "completed_at": assessment.completed_at.isoformat() if assessment.completed_at else None,
            "criteria": [
                {
                    "metric": c.metric.value,
                    "weight": c.weight,
                    "threshold": c.threshold,
                    "target_value": c.target_value,
                    "description": c.description
                }
                for c in assessment.criteria
            ],
            "results": [
                {
                    "metric": r.metric.value,
                    "value": r.value,
                    "score": r.score,
                    "passed": r.passed,
                    "measurement_unit": r.measurement_unit
                }
                for r in assessment.results
            ],
            "recommendations": self._generate_recommendations(assessment),
            "metadata": assessment.metadata or {}
        }
        
        return report
    
    def _generate_recommendations(self, assessment: CapabilityAssessment) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        for result in assessment.results:
            if not result.passed:
                if result.metric == AssessmentMetric.ACCURACY:
                    recommendations.append("建议加强任务理解和执行准确性训练")
                elif result.metric == AssessmentMetric.SPEED:
                    recommendations.append("建议优化算法和处理流程以提高执行速度")
                elif result.metric == AssessmentMetric.CONSISTENCY:
                    recommendations.append("建议改进决策逻辑以提高结果一致性")
                elif result.metric == AssessmentMetric.RELIABILITY:
                    recommendations.append("建议增强错误处理和恢复机制")
        
        # 基于能力等级的建议
        if assessment.capability_level == CapabilityLevel.NOVICE:
            recommendations.append("建议进行基础能力训练和知识补充")
        elif assessment.capability_level == CapabilityLevel.INTERMEDIATE:
            recommendations.append("建议进行专项技能提升训练")
        elif assessment.capability_level == CapabilityLevel.ADVANCED:
            recommendations.append("建议进行复杂场景应用训练")
        
        return recommendations
    
    def get_capability_trends(self, agent_id: str,
                            capability_name: str = None) -> Dict[str, Any]:
        """获取能力发展趋势"""
        assessments = self.get_agent_assessment_history(agent_id)
        
        if capability_name:
            assessments = [a for a in assessments if a.capability_name == capability_name]
        
        if not assessments:
            return {"error": "没有找到相关评估记录"}
        
        # 按时间排序
        assessments.sort(key=lambda x: x.created_at)
        
        trends = {
            "agent_id": agent_id,
            "capability_name": capability_name,
            "assessment_count": len(assessments),
            "time_range": {
                "start": assessments[0].created_at.isoformat(),
                "end": assessments[-1].created_at.isoformat()
            },
            "score_trend": [
                {
                    "date": a.created_at.isoformat(),
                    "score": a.overall_score,
                    "level": a.capability_level.value
                }
                for a in assessments
            ],
            "improvement": {
                "score_change": assessments[-1].overall_score - assessments[0].overall_score,
                "level_progression": self._calculate_level_progression(assessments)
            }
        }
        
        return trends
    
    def _calculate_level_progression(self, assessments: List[CapabilityAssessment]) -> Dict[str, Any]:
        """计算能力等级进展"""
        levels = [a.capability_level for a in assessments]
        level_values = {
            CapabilityLevel.NOVICE: 1,
            CapabilityLevel.INTERMEDIATE: 2,
            CapabilityLevel.ADVANCED: 3,
            CapabilityLevel.EXPERT: 4,
            CapabilityLevel.MASTER: 5
        }
        
        start_level = levels[0]
        end_level = levels[-1]
        
        progression = level_values[end_level] - level_values[start_level]
        
        return {
            "start_level": start_level.value,
            "end_level": end_level.value,
            "progression": progression,
            "improved": progression > 0,
            "stable": progression == 0,
            "declined": progression < 0
        }
    
    def export_assessment_data(self, agent_id: str = None,
                             format: str = "json") -> str:
        """导出评估数据"""
        if agent_id:
            assessments = self.get_agent_assessment_history(agent_id)
        else:
            assessments = list(self.assessments.values())
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "agent_id": agent_id,
            "assessment_count": len(assessments),
            "assessments": [asdict(assessment) for assessment in assessments]
        }
        
        if format.lower() == "json":
            return json.dumps(export_data, default=str, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
            