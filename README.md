# 🤖 LangGraph 多智能体协作平台 v0.3

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![LangGraph](https://img.shields.io/badge/LangGraph-0.2+-green.svg)](https://github.com/langchain-ai/langgraph)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-0.3.0-red.svg)](CHANGELOG.md)

> **新一代智能软件开发平台** - 基于LangGraph框架构建的企业级多智能体协作系统，具备7种专业智能体、高级工作流引擎、智能协作机制和企业级管理功能。

## 🎯 项目概述

LangGraph多智能体协作平台是一个**生产级智能软件开发平台**，通过模拟真实软件开发团队的协作模式，实现了从需求分析到部署交付的全流程自动化。系统采用先进的图工作流技术，支持复杂的并行协作、条件分支和智能决策，能够与人类开发团队深度协作，显著提升软件开发效率和质量。

## ✨ 核心特性

### 🤖 专业智能体生态系统
- **7种专业智能体**: 架构师、产品经理、开发工程师、QA测试、DevOps、文档工程师、安全专家
- **智能协作机制**: 支持顺序、并行、审查、咨询、结对编程、头脑风暴、指导等7种协作模式
- **动态能力评估**: 基于历史表现的智能体能力评分和任务分配优化
- **可插拔架构**: 支持自定义智能体扩展和第三方智能体集成

### ⚡ 高级工作流引擎
- **并行执行引擎**: 支持100+并发任务，任务启动延迟<10ms
- **条件分支引擎**: 复杂业务逻辑的智能判断和路径选择
- **工作流DSL**: 支持YAML/JSON声明式工作流定义
- **实时监控调试**: 完整的执行状态跟踪、性能分析和错误诊断
- **智能优化建议**: 基于执行数据的工作流性能优化建议

### 🔧 企业级功能
- **多LLM支持**: OpenAI GPT、Anthropic Claude、Moonshot Kimi等主流模型
- **人机协同**: Human-in-the-Loop审核机制，确保关键决策的人工把控
- **安全沙箱**: 所有文件操作限制在安全的workspace环境中
- **性能监控**: 实时系统指标、智能体性能和资源使用监控
- **模板库**: 开箱即用的工作流模板，支持快速项目启动

## � 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
├─────────────────────┬───────────────────┬───────────────────┤
│   Web UI (Streamlit) │   CLI Interface   │   REST API        │
└─────────────────────┴───────────────────┴───────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   应用服务层 (Service Layer)                  │
├─────────────────────┬───────────────────┬───────────────────┤
│  Project Manager    │  Workflow Engine  │  Agent Manager    │
│  Task Scheduler     │  Collaboration    │  Tool Executor    │
└─────────────────────┴───────────────────┴───────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   智能体层 (Agent Layer)                     │
├─────────────────────┬───────────────────┬───────────────────┤
│   Supervisor Agent  │  Specialist Agents│  Custom Agents    │
│   ├─ 任务协调        │  ├─ 架构师         │  ├─ 插件智能体     │
│   ├─ 智能分配        │  ├─ 产品经理       │  ├─ 外部API       │
│   ├─ 协作优化        │  ├─ 开发工程师     │  └─ 自定义扩展     │
│   └─ 质量控制        │  ├─ QA测试        │                   │
│                     │  ├─ DevOps        │                   │
│                     │  ├─ 文档工程师     │                   │
│                     │  └─ 安全专家       │                   │
└─────────────────────┴───────────────────┴───────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   核心引擎层 (Core Layer)                     │
├─────────────────────┬───────────────────┬───────────────────┤
│  LangGraph Engine   │  State Manager    │  Memory System    │
│  Workflow Executor  │  Event System     │  Knowledge Base   │
│  Load Balancer      │  Cache Manager    │  Learning Engine  │
└─────────────────────┴───────────────────┴───────────────────┘
```

## 📂 项目结构

```
langgraph-multi-agent/
├── 🌐 用户界面
│   ├── app.py                          # Streamlit Web UI 主入口
│   ├── run_web_ui.py                   # Web UI 启动脚本
│   └── src/langgraph_system/
│       ├── web/streamlit_app.py        # Web应用核心逻辑
│       └── cli/cli_main.py             # CLI命令行界面
│
├── 🤖 智能体系统
│   └── src/langgraph_system/agents/
│       ├── supervisor_agent.py         # 协调者智能体
│       ├── architect_agent.py          # 系统架构师
│       ├── product_manager_agent.py    # 产品经理
│       ├── coder_agent.py              # 开发工程师
│       ├── qa_agent.py                 # QA测试工程师
│       ├── devops_agent.py             # DevOps工程师
│       ├── documentation_agent.py      # 文档工程师
│       └── base_v3.py                  # 智能体基类
│
├── ⚡ 工作流引擎
│   └── src/langgraph_system/
│       ├── workflow/                   # 高级工作流引擎
│       │   ├── workflow_engine.py      # 核心工作流引擎
│       │   ├── workflow_dsl.py         # DSL解析器
│       │   └── workflow_monitor.py     # 监控和调试
│       └── graphs/                     # LangGraph工作流定义
│           └── project_workflow.py     # 项目工作流图
│
├── 🔧 核心系统
│   └── src/langgraph_system/
│       ├── core/                       # 核心组件
│       │   ├── system.py               # 系统主类
│       │   ├── agent_pool.py           # 智能体池管理
│       │   └── performance_monitor.py  # 性能监控
│       ├── states/                     # 状态管理
│       ├── tools/                      # 工具集合
│       └── llm/                        # LLM配置和客户端
│
├── 📁 工作空间
│   ├── workspace/                      # 安全的文件操作区域
│   ├── logs/                          # 系统日志
│   └── checkpoints/                   # 工作流检查点
│
└── 📚 文档和配置
    ├── docs/                          # 完整技术文档
    ├── examples/                      # 使用示例
    ├── requirements.txt               # Python依赖
    └── .env                          # 环境配置
```

## 🚀 快速开始

### 📋 系统要求

- **Python**: 3.11+ (推荐 3.12+)
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 2GB 可用空间
- **网络**: 稳定的互联网连接（用于LLM API调用）

### ⚡ 一键安装

```bash
# 1. 克隆项目
git clone https://github.com/your-org/langgraph-multi-agent.git
cd langgraph-multi-agent

# 2. 创建虚拟环境
python -m venv venv

# 3. 激活虚拟环境
# Windows
.\venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt

# 5. 配置Redis（推荐使用内存模式）
python scripts/setup_redis.py memory

# 6. 配置API密钥
cp .env.example .env  # 然后编辑.env文件
```

### 🔑 环境配置

创建 `.env` 文件并配置您的API密钥：

```bash
# LLM提供商选择 (openai/anthropic/moonshot)
LLM_PROVIDER="moonshot"

# API密钥配置
OPENAI_API_KEY="sk-your-openai-key"
MOONSHOT_API_KEY="sk-your-moonshot-key"
ANTHROPIC_API_KEY="sk-your-anthropic-key"

# Redis配置（推荐使用内存模式）
REDIS_MODE="memory"

# 可选配置
LANGGRAPH_LOG_LEVEL="INFO"
LANGGRAPH_WORKSPACE_DIR="./workspace"
```

### 🔧 Redis配置选项

系统支持多种Redis模式，根据需要选择：

```bash
# 内存模拟（推荐用于开发测试）
python scripts/setup_redis.py memory

# Docker容器（推荐用于本地开发）
python scripts/setup_redis.py docker

# 本地安装（适合生产环境）
python scripts/setup_redis.py local

# 交互式配置
python scripts/setup_redis.py
```

### 🎯 验证安装

```bash
# 检查系统状态
python src/main.py status

# 查看可用智能体
python src/main.py agents list

# 运行示例项目
python examples/basic_usage.py
```

## � 使用方式

### 🌐 Web界面 (推荐新手)

**启动Web界面**:
```bash
# 方式1: 使用启动脚本
python run_web_ui.py

# 方式2: 直接启动Streamlit
streamlit run src/langgraph_system/web/streamlit_app.py
```

**功能特性**:
- 📊 **实时监控面板**: 系统状态、智能体性能、任务进度
- 🚀 **项目创建向导**: 可视化项目配置和需求定义
- 🤖 **智能体管理**: 查看智能体能力、历史表现和协作关系
- 📈 **工作流可视化**: 实时工作流执行状态和调试信息
- 🔧 **工具管理**: 文件操作、代码生成、测试执行等工具

### ⌨️ 命令行界面 (推荐专业用户)

**基础命令**:
```bash
# 查看系统状态
python src/main.py status

# 查看智能体列表
python src/main.py agents list

# 创建并执行项目
python src/main.py project create \
  --name "WebApp" \
  --task-type "development" \
  --description "创建一个Flask Web应用"
```

**高级工作流管理**:
```bash
# 查看工作流模板
python src/main.py workflow templates

# 创建自定义工作流
python src/main.py workflow create \
  --template software_development \
  --output my_workflow.yaml

# 执行工作流
python src/main.py workflow execute \
  --workflow my_workflow.yaml \
  --project "MyProject"
```

**智能体协作**:
```bash
# 智能体能力推荐
python src/main.py agents recommend \
  --capability code_development

# 查看协作模式
python src/main.py agents collaboration \
  --mode pair_programming

# 性能监控
python src/main.py monitor dashboard
```

### 🔄 交互式模式

```bash
# 启动交互式CLI
python src/main.py interactive

# 在交互模式中使用命令
langgraph> create MyProject development
langgraph> run
langgraph> monitor
langgraph> help
```

## � 性能指标

### 🚀 系统性能
- **并发处理**: 支持100+并发任务
- **响应时间**: 任务启动延迟 < 10ms
- **内存效率**: 优化的内存使用，支持长时间运行
- **可扩展性**: 支持水平扩展和负载均衡

### 🤖 智能体能力
- **专业智能体**: 7种专业角色，覆盖软件开发全流程
- **协作模式**: 7种智能协作模式，适应不同场景
- **任务成功率**: 基于历史数据的智能体能力评估
- **学习能力**: 从历史经验中持续学习和优化

### 📊 工作流效率
- **模板库**: 4+种开箱即用的工作流模板
- **自动化程度**: 90%+的开发流程自动化
- **质量保证**: 多层次的代码审查和质量检查
- **人机协同**: 关键节点的人工审核和决策

## 🛠️ 技术栈

### 核心框架
- **Python 3.11+**: 现代Python特性支持
- **LangGraph 0.2+**: 图工作流框架
- **LangChain 0.1+**: LLM应用开发框架
- **FastAPI 0.104+**: 高性能Web框架
- **Streamlit 1.28+**: 快速Web UI开发

### 数据存储
- **PostgreSQL 15+**: 关系型数据存储
- **Redis 7.0+**: 缓存和会话管理
- **ChromaDB 0.4+**: 向量数据库
- **Neo4j 5.0+**: 知识图谱存储

### 监控运维
- **Prometheus**: 指标收集
- **Grafana**: 可视化监控
- **Docker**: 容器化部署
- **Kubernetes**: 容器编排

## 🔮 发展路线图

### 🎯 v0.4 计划 (2024 Q2)
- **🧠 智能记忆系统**: 集成向量数据库和知识图谱
- **🔌 插件生态**: 支持第三方智能体和工具插件
- **🌐 分布式架构**: 支持多节点部署和负载均衡
- **📱 移动端支持**: 响应式Web界面和移动应用

### 🚀 v0.5 计划 (2024 Q3)
- **🤖 AI代码审查**: 基于大模型的智能代码审查
- **📊 高级分析**: 项目效率分析和优化建议
- **🔐 企业安全**: 完整的权限管理和安全审计
- **🌍 多语言支持**: 国际化和本地化支持

### 🌟 长期愿景
- **🧪 自主学习**: 智能体自主学习和能力进化
- **🤝 生态集成**: 与主流开发工具和平台深度集成
- **🎨 可视化编程**: 拖拽式工作流设计器
- **🌐 云原生**: 完整的云原生解决方案

## 📚 文档和资源

### 📖 核心文档
- [🚀 快速开始指南](docs/CLI_QUICK_START.md) - 5分钟快速上手
- [⌨️ CLI使用手册](docs/CLI_USAGE.md) - 完整的命令行使用指南
- [🌐 Web界面指南](docs/WEB_UI_GUIDE.md) - Web界面功能详解
- [🏗️ 架构设计文档](docs/V0.3_ARCHITECTURE_DESIGN.md) - 系统架构深度解析
- [📋 Redis配置指南](docs/REDIS_CONFIGURATION_GUIDE.md) - Redis配置详细说明
- [⚡ Redis快速参考](docs/REDIS_QUICK_REFERENCE.md) - Redis配置快速解决方案

### 🔧 技术文档
- [📋 技术规范](docs/V0.3_TECHNICAL_SPECIFICATION.md) - 完整技术规范
- [🤖 智能体规范](docs/V0.3_AGENT_SPECIFICATIONS.md) - 智能体设计规范
- [🔧 LLM配置指南](docs/LLM_CONFIG.md) - 大语言模型配置
- [🛠️ 开发指南](docs/SUPERVISOR_GUIDE.md) - 开发者指南

### 📊 测试报告
- [✅ 系统测试报告](docs/SYSTEM_TEST_REPORT.md) - 完整系统测试
- [⌨️ CLI测试报告](docs/CLI_TEST_REPORT.md) - CLI功能测试
- [🌐 Web界面测试](docs/WEB_UI_TEST_REPORT.md) - Web界面测试

### 💡 示例代码
```bash
# 查看所有示例
ls examples/

# 基础使用示例
python examples/basic_usage.py

# CLI使用示例
python examples/cli_examples.py

# 工作流演示
python examples/phase3_workflow_demo.py
```

## 🤝 贡献指南

我们欢迎社区贡献！请查看以下指南：

### 🔧 开发环境设置
```bash
# 1. Fork项目并克隆
git clone https://github.com/your-username/langgraph-multi-agent.git
cd langgraph-multi-agent

# 2. 创建开发分支
git checkout -b feature/your-feature-name

# 3. 安装开发依赖
pip install -r requirements.txt
pip install -e .

# 4. 运行测试
python -m pytest tests/

# 5. 代码格式化
black src/ tests/
isort src/ tests/
```

### 📝 贡献类型
- **🐛 Bug修复**: 发现并修复系统问题
- **✨ 新功能**: 添加新的智能体或工具
- **📚 文档改进**: 完善文档和示例
- **🧪 测试增强**: 增加测试覆盖率
- **🎨 UI/UX改进**: 改善用户界面和体验

### 🔍 代码审查流程
1. 提交Pull Request
2. 自动化测试检查
3. 代码审查和反馈
4. 合并到主分支

## 📄 许可证

本项目采用 [MIT许可证](LICENSE)。

## 🙏 致谢

感谢以下开源项目和社区：
- [LangGraph](https://github.com/langchain-ai/langgraph) - 图工作流框架
- [LangChain](https://github.com/langchain-ai/langchain) - LLM应用开发框架
- [Streamlit](https://streamlit.io/) - 快速Web应用开发
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Web框架

## 📞 联系我们

- **项目主页**: [GitHub Repository](https://github.com/your-org/langgraph-multi-agent)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/langgraph-multi-agent/issues)
- **功能建议**: [GitHub Discussions](https://github.com/your-org/langgraph-multi-agent/discussions)
- **技术支持**: [技术文档](docs/)

---

<div align="center">

**🌟 如果这个项目对您有帮助，请给我们一个Star！ 🌟**

[![Star History Chart](https://api.star-history.com/svg?repos=your-org/langgraph-multi-agent&type=Date)](https://star-history.com/#your-org/langgraph-multi-agent&Date)

</div>
