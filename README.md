# LangGraph 多智能体协作平台

这是一个基于 Google 的 LangGraph 框架构建的、高度可扩展的多智能体（Multi-Agent）协作系统。该平台旨在模拟一个软件开发团队，其中包含不同角色的智能体（如研究员、程序员、审查员等），它们在中央协调器（Supervisor）的指导下协同工作，以完成复杂的软件开发任务。

## ✨ 核心特性

- **动态 Agent 加载**: 系统能够自动发现并加载 `src/langgraph_system/agents` 目录下的所有 Agent，实现了真正的可插拔架构。
- **基于角色的工作流**: `Supervisor` Agent 根据任务类型（如 `DEVELOPMENT`, `RESEARCH`）动态地规划和分配任务给最合适的 Agent。
- **可更换的 LLM 内核**: 支持多种大语言模型（如 OpenAI GPT 系列, Moonshot Kimi, Anthropic Claude），并可通过简单的配置进行切换。
- **文件系统工具**: Agent 具备读写本地文件的能力，所有操作均被安全地限制在根目录的 `workspace` 文件夹内。
- **人机协同 (Human-in-the-Loop)**: 在关键节点（如代码生成后），工作流会自动暂停，等待用户进行审核和批准，确保最终产出的质量和可控性。
- **Streamlit Web UI**: 提供了一个直观的 Web 界面，用于配置项目、启动工作流、实时查看状态和进行人机交互。
- **外部化 Prompt 管理**: 所有 Agent 的核心提示词（Prompts）都存储在 `src/langgraph_system/prompts` 目录下的 `.md` 文件中，实现了 Prompt 与业务逻辑的分离，便于迭代和优化。

## 📂 项目结构

```
.
├── app.py                  # Streamlit Web UI 入口
├── workspace/              # Agent 运行时的工作目录（生成的文件等）
├── .env                    # 环境变量配置文件 (API 密钥等)
├── requirements.txt        # 项目依赖
└── src/
    └── langgraph_system/
        ├── agents/         # 存放所有角色的 Agent 实现
        ├── graphs/         # 定义 LangGraph 工作流
        ├── llm/            # LLM 客户端和配置
        ├── prompts/        # 存放所有 Agent 的 Prompt 模板
        ├── states/         # 定义工作流的状态对象
        └── tools/          # 存放 Agent 可使用的工具（如文件操作）
```

## 🚀 安装与设置

1.  **克隆仓库**:
    ```bash
    git clone <your-repo-url>
    cd <repo-name>
    ```

2.  **创建并激活虚拟环境** (推荐):
    ```bash
    python -m venv venv
    # Windows
    .\venv\Scripts\activate
    # macOS / Linux
    source venv/bin/activate
    ```

3.  **安装依赖**:
    ```bash
    pip install -r requirements.txt
    ```

4.  **配置环境变量**:
    - 将根目录下的 `.env.example` (如果存在) 复制为 `.env`，或者直接创建 `.env` 文件。
    - 打开 `.env` 文件并填入您的 API 密钥。

    ```dotenv
    # .env

    # 1. 设置您想使用的 LLM 服务商
    # 可选项: "openai", "moonshot", "anthropic"
    LLM_PROVIDER="moonshot"

    # 2. 填入对应服务商的 API 密钥
    OPENAI_API_KEY="sk-..."
    MOONSHOT_API_KEY="sk-..."
    ANTHROPIC_API_KEY="sk-..."
    ```

## 🏃 如何运行

系统提供两种运行方式：

### 1. 通过 Streamlit Web UI (推荐)

这是最直观、功能最丰富的运行方式。

```bash
streamlit run app.py
```

在浏览器中打开提供的 URL (通常是 `http://localhost:8501`)，您可以在界面上：
- 配置项目名称和需求。
- 启动和监控工作流。
- 在需要时进行人工审核。

### 2. 通过命令行工具

对于自动化和脚本化任务，可以使用命令行界面。

```bash
python -m src.langgraph_system.cli run --project-name "MyCLIApp" --task "DEVELOPMENT" --description "Create a simple calculator function."
```

## 🔮 未来展望

- **增强 Agent 记忆**: 集成向量数据库，为 Agent 提供长期记忆能力。
- **动态工具注册**: 允许 Agent 在运行时动态地发现和注册新工具。
- **更复杂的工作流**: 设计支持并行执行、条件分支更复杂的图结构。
- **完善的 Web UI**: 在 UI 中增加文件浏览器、在线代码编辑器和更详细的状态可视化。
