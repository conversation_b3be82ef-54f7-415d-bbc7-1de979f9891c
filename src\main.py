#!/usr/bin/env python3
"""
LangGraph多智能体系统主入口
"""

import asyncio
import logging
from typing import Dict, Any, Optional
import sys
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 使用新的核心系统
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from langgraph_system.core import LangGraphSystem
from langgraph_system.states.project_state import TaskType
from langgraph_system.llm.config import LLMConfig

class LangGraphMultiAgentSystem:
    """LangGraph多智能体系统主类 - 向后兼容接口"""
    
    def __init__(self, llm_config: Optional[LLMConfig] = None):
        """初始化系统"""
        logger.warning("LangGraphMultiAgentSystem 已弃用，请使用 LangGraphSystem")
        self.core_system = LangGraphSystem(llm_config)
        
    async def create_project(self, project_name: str, task_type: TaskType,
                           description: str = "", requirements: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        创建新项目
        
        Args:
            project_name: 项目名称
            task_type: 任务类型
            description: 项目描述
            requirements: 需求信息
            
        Returns:
            项目创建结果
        """
        return await self.core_system.create_project(project_name, task_type, description, requirements)
        
    async def generate_code(self, project_name: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成代码
        
        Args:
            project_name: 项目名称
            requirements: 需求信息
            
        Returns:
            代码生成结果
        """
        return await self.core_system.create_project(
            project_name, TaskType.DEVELOPMENT,
            requirements.get("description", ""), requirements
        )
        
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        info = self.core_system.get_system_info()
        # 保持向后兼容的格式
        return {
            "name": info["name"],
            "version": info["version"],
            "workflows": ["ProjectWorkflow", "CodeGenerationWorkflow"],  # 兼容格式
            "agents": info["available_agents"],
            "capabilities": [task.value for task in TaskType]
        }

# 导出新的核心系统作为主要接口
__all__ = ["LangGraphSystem", "LangGraphMultiAgentSystem", "TaskType", "LLMConfig"]

from langgraph_system.cli import cli

if __name__ == "__main__":
    cli()
