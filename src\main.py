#!/usr/bin/env python3
"""
🤖 LangGraph多智能体协作平台 v0.3.0 - 主入口
新一代智能软件开发平台，支持7种专业智能体、高级工作流引擎和智能协作机制

主要功能:
- 企业级多智能体协作系统
- 高性能并行工作流引擎
- 智能协作模式和能力评估
- 实时监控和性能优化
- 人机协同和安全审计

使用方式:
  python src/main.py --help              # 查看帮助
  python src/main.py status              # 系统状态
  python src/main.py interactive         # 交互模式
  python src/main.py project create      # 创建项目
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import warnings

# 添加项目路径到Python路径
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))
sys.path.insert(0, str(Path(__file__).parent))

# 配置日志系统
def setup_logging(level: str = "INFO", format_type: str = "standard") -> None:
    """配置日志系统"""
    log_formats = {
        "standard": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "simple": "%(levelname)s: %(message)s",
        "detailed": "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
    }

    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=log_formats.get(format_type, log_formats["standard"]),
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(PROJECT_ROOT / "logs" / "main.log", encoding='utf-8')
        ]
    )

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)

# 导入核心系统组件
try:
    from langgraph_system.core import LangGraphSystem
    from langgraph_system.states.project_state import TaskType
    from langgraph_system.llm.config import LLMConfig
except ImportError as e:
    logger.error(f"核心模块导入失败: {e}")
    sys.exit(1)

class LangGraphMultiAgentSystem:
    """
    LangGraph多智能体系统主类 - 向后兼容接口

    ⚠️  已弃用: 请使用 LangGraphSystem 或直接使用 CLI 接口
    """

    def __init__(self, llm_config: Optional[LLMConfig] = None):
        """初始化系统"""
        warnings.warn(
            "LangGraphMultiAgentSystem 已弃用，请使用 LangGraphSystem 或 CLI 接口",
            DeprecationWarning,
            stacklevel=2
        )
        self.core_system = LangGraphSystem(llm_config)

    async def create_project(self, project_name: str, task_type: TaskType,
                           description: str = "", requirements: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        创建新项目

        Args:
            project_name: 项目名称
            task_type: 任务类型
            description: 项目描述
            requirements: 需求信息

        Returns:
            项目创建结果
        """
        return await self.core_system.create_project(project_name, task_type, description, requirements)

    async def generate_code(self, project_name: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成代码

        Args:
            project_name: 项目名称
            requirements: 需求信息

        Returns:
            代码生成结果
        """
        return await self.core_system.create_project(
            project_name, TaskType.DEVELOPMENT,
            requirements.get("description", ""), requirements
        )

    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        info = self.core_system.get_system_info()
        # 保持向后兼容的格式
        return {
            "name": info["name"],
            "version": info["version"],
            "workflows": ["ProjectWorkflow", "CodeGenerationWorkflow"],  # 兼容格式
            "agents": info["available_agents"],
            "capabilities": [task.value for task in TaskType]
        }


class SystemManager:
    """
    系统管理器 - 提供高级系统管理功能
    """

    def __init__(self):
        """初始化系统管理器"""
        self.core_system = None
        self._initialized = False

    async def initialize(self, llm_config: Optional[LLMConfig] = None) -> bool:
        """
        初始化系统

        Args:
            llm_config: LLM配置

        Returns:
            初始化是否成功
        """
        try:
            self.core_system = LangGraphSystem(llm_config)
            self._initialized = True
            logger.info("✅ LangGraph系统初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            return False

    def is_initialized(self) -> bool:
        """检查系统是否已初始化"""
        return self._initialized and self.core_system is not None

    async def health_check(self) -> Dict[str, Any]:
        """
        系统健康检查

        Returns:
            健康检查结果
        """
        if not self.is_initialized():
            return {"status": "error", "message": "系统未初始化"}

        try:
            info = self.core_system.get_system_info()
            return {
                "status": "healthy",
                "version": info["version"],
                "agents": len(info["available_agents"]),
                "capabilities": len(info["capabilities"]),
                "timestamp": asyncio.get_event_loop().time()
            }
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def shutdown(self) -> None:
        """优雅关闭系统"""
        if self.core_system:
            # 这里可以添加清理逻辑
            logger.info("🔄 系统正在关闭...")
            self._initialized = False
            self.core_system = None
            logger.info("✅ 系统已关闭")

def create_system_manager() -> SystemManager:
    """创建系统管理器实例"""
    return SystemManager()


async def main_async():
    """异步主函数 - 用于程序化调用"""
    manager = create_system_manager()

    # 初始化系统
    if await manager.initialize():
        # 执行健康检查
        health = await manager.health_check()
        logger.info(f"系统状态: {health}")

        # 这里可以添加其他初始化逻辑
        return manager
    else:
        logger.error("系统初始化失败")
        return None


def main():
    """
    主入口函数

    根据调用方式决定行为:
    - 命令行调用: 启动CLI界面
    - 程序化调用: 返回系统管理器
    """
    # 检查是否为命令行调用
    if len(sys.argv) > 1:
        # 有命令行参数，启动CLI
        from langgraph_system.cli import cli
        try:
            cli()
        except KeyboardInterrupt:
            logger.info("👋 用户中断，程序退出")
        except Exception as e:
            logger.error(f"❌ CLI执行错误: {e}")
            sys.exit(1)
    else:
        # 无参数，显示帮助信息
        print("🤖 LangGraph多智能体协作平台 v0.3.0")
        print("=" * 50)
        print()
        print("📖 使用方式:")
        print("  python src/main.py --help              # 查看完整帮助")
        print("  python src/main.py status              # 查看系统状态")
        print("  python src/main.py interactive         # 启动交互模式")
        print("  python src/main.py project create      # 创建新项目")
        print("  python src/main.py agents list         # 查看智能体")
        print("  python src/main.py workflow templates  # 查看工作流模板")
        print()
        print("🌐 Web界面:")
        print("  python run_web_ui.py                   # 启动Web界面")
        print()
        print("📚 更多信息:")
        print("  docs/CLI_QUICK_START.md                # 快速开始指南")
        print("  docs/CLI_USAGE.md                      # 详细使用手册")
        print("  examples/                              # 使用示例")


# 导出接口
__all__ = [
    # 核心系统
    "LangGraphSystem",
    "SystemManager",
    "create_system_manager",

    # 向后兼容
    "LangGraphMultiAgentSystem",

    # 类型和配置
    "TaskType",
    "LLMConfig",

    # 主函数
    "main",
    "main_async"
]

# 程序入口
if __name__ == "__main__":
    main()
