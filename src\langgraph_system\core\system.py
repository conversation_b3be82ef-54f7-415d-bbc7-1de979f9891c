"""
核心系统类 - 统一的系统入口和管理
"""

from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

from ..states.project_state import ProjectState, TaskType
from ..graphs.project_workflow import ProjectWorkflow
from ..agents.supervisor_agent import SupervisorAgent
from ..llm.config import LLMConfig
from .registry import agent_registry, tool_registry
from .exceptions import LangGraphError, ConfigurationError

logger = logging.getLogger(__name__)


class LangGraphSystem:
    """LangGraph多智能体系统核心类"""
    
    def __init__(self, llm_config: Optional[LLMConfig] = None):
        """初始化系统"""
        self.llm_config = llm_config or LLMConfig()
        self.supervisor = None
        self.workflow = None
        self._initialized = False
        
        # 系统信息
        self.version = "0.2.0"
        self.name = "LangGraph Multi-Agent System"
        
        logger.info(f"初始化 {self.name} v{self.version}")
    
    def initialize(self):
        """初始化系统组件"""
        if self._initialized:
            logger.warning("系统已经初始化")
            return
        
        try:
            # 初始化核心组件
            self.supervisor = SupervisorAgent(self.llm_config)
            self.workflow = ProjectWorkflow()
            
            # 注册默认工具
            self._register_default_tools()
            
            self._initialized = True
            logger.info("系统初始化完成")
            
        except Exception as e:
            raise ConfigurationError(f"系统初始化失败: {str(e)}", "SYSTEM_INIT_FAILED")
    
    def _register_default_tools(self):
        """注册默认工具"""
        from ..tools import file_system_tools
        
        # 注册文件系统工具
        tool_registry.register("read_file", file_system_tools.read_file, {
            "description": "读取文件内容",
            "parameters": {"file_path": "str"}
        })
        
        tool_registry.register("write_file", file_system_tools.write_file, {
            "description": "写入文件内容", 
            "parameters": {"file_path": "str", "content": "str"}
        })
        
        tool_registry.register("list_files", file_system_tools.list_files, {
            "description": "列出目录文件",
            "parameters": {"directory_path": "str"}
        })
    
    async def create_project(self, project_name: str, task_type: TaskType, 
                           description: str = "", requirements: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建新项目"""
        if not self._initialized:
            self.initialize()
        
        logger.info(f"创建项目: {project_name}")
        
        # 创建项目状态
        project_state = ProjectState(
            project_name=project_name,
            current_task=task_type,
            description=description,
            requirements=requirements or {},
            context={"description": description},
            execution_status="running"
        )
        
        # 执行工作流
        result = await self.workflow.execute(project_state)
        
        return result
    
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个任务"""
        if not self._initialized:
            self.initialize()
        
        return await self.supervisor.process_task(task)
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "name": self.name,
            "version": self.version,
            "initialized": self._initialized,
            "available_agents": agent_registry.list_agents(),
            "available_tools": tool_registry.list_tools(),
            "supervisor_status": self.supervisor.get_system_status() if self.supervisor else None,
            "llm_provider": self.llm_config.provider if self.llm_config else None
        }
    
    def get_agent_capabilities(self) -> Dict[str, Any]:
        """获取智能体能力信息"""
        if not self.supervisor:
            return {}
        return self.supervisor.get_agent_capabilities()
    
    def create_task_plan(self, task_type: TaskType, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建任务执行计划"""
        if not self.supervisor:
            raise LangGraphError("系统未初始化", "SYSTEM_NOT_INITIALIZED")
        return self.supervisor.create_task_plan(task_type, requirements)
    
    def shutdown(self):
        """关闭系统"""
        logger.info("关闭系统...")
        
        # 清理资源
        if self.supervisor:
            self.supervisor = None
        if self.workflow:
            self.workflow = None
        
        self._initialized = False
        logger.info("系统已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.initialize()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()