# SmartTodoManager 文档

## 项目概述
智能待办事项管理器是一个现代化的任务管理应用，支持智能分类、优先级排序和用户管理。

## 功能特性
- ✅ 用户注册和登录
- ✅ 待办事项CRUD操作
- ✅ 智能分类和标签
- ✅ 优先级自动排序
- ✅ 截止日期提醒
- ✅ 数据统计和报告
- ✅ REST API接口
- ✅ 响应式Web界面

## 技术栈
- **后端**: FastAPI + SQLAlchemy
- **前端**: HTML + CSS + JavaScript
- **数据库**: SQLite
- **认证**: JWT Token
- **部署**: Docker

## 快速开始
1. 安装依赖: `pip install -r requirements.txt`
2. 启动后端: `python backend/main.py`
3. 访问前端: `http://localhost:8000/frontend/index.html`

## API文档
- GET /todos - 获取所有待办事项
- POST /todos - 创建新的待办事项
- PUT /todos/{id} - 更新待办事项
- DELETE /todos/{id} - 删除待办事项

## 测试
运行测试: `pytest tests/`

## 部署
使用Docker: `docker-compose up`
