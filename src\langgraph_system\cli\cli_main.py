#!/usr/bin/env python3
"""
LangGraph多智能体系统命令行主入口 v0.3.0
支持专业智能体生态、高级工作流引擎和智能协作机制
"""

import click
import asyncio
import logging
import sys
import json
import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from ..states.project_state import ProjectState, TaskType
from ..agents.supervisor_agent import SupervisorAgent, CollaborationMode, AgentCapability
from ..llm.config import LLMConfig
from .interactive import InteractiveCLI
from .commands import (
    ProjectCommands, AgentCommands, SystemCommands, 
    WorkflowCommands, MonitoringCommands
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='启用详细输出')
@click.option('--config', '-c', type=click.Path(exists=True), help='配置文件路径')
@click.option('--llm-provider', type=click.Choice(['openai', 'anthropic', 'moonshot']), 
              default='openai', help='LLM提供商')
@click.option('--model', type=str, help='指定模型名称')
@click.pass_context
def cli(ctx, verbose, config, llm_provider, model):
    """
    🤖 LangGraph多智能体协作系统 v0.3.0
    
    新一代智能体协作平台，支持：
    - 7种专业智能体生态系统
    - 高级工作流引擎和并行执行
    - 智能协作模式和能力评估
    - 实时监控和性能优化
    - 企业级管理和安全审计
    """
    # 确保上下文对象存在
    ctx.ensure_object(dict)
    
    # 设置日志级别
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        click.echo("🔍 启用详细输出模式")
    
    # 创建LLM配置
    llm_config = LLMConfig()
    if llm_provider:
        llm_config.provider = llm_provider
    if model:
        llm_config.model = model
    
    # 加载配置文件
    if config:
        ctx.obj['config_file'] = config
        click.echo(f"📁 使用配置文件: {config}")
        # TODO: 实际加载配置文件内容
    
    # 初始化系统组件（轻量级模式）
    try:
        # 初始化Supervisor v0.3.0（轻量级模式，避免阻塞）
        supervisor = SupervisorAgent(llm_config, lightweight=True)

        ctx.obj['supervisor'] = supervisor
        ctx.obj['llm_config'] = llm_config

        # 初始化命令处理器
        ctx.obj['project_commands'] = ProjectCommands(supervisor)
        ctx.obj['agent_commands'] = AgentCommands(supervisor)
        ctx.obj['system_commands'] = SystemCommands(supervisor)
        ctx.obj['workflow_commands'] = WorkflowCommands(supervisor)
        ctx.obj['monitoring_commands'] = MonitoringCommands(supervisor)

        click.echo("✅ LangGraph多智能体系统 v0.3.0 初始化完成（轻量级模式）")

        # 显示系统状态
        status = supervisor.get_system_status()
        click.echo(f"🤖 可用智能体: {len(status['specialist_agents'])} 个")
        click.echo(f"⚡ 协作模式: {len(status['collaboration_modes'])} 种")
        
    except Exception as e:
        click.echo(f"❌ 系统初始化失败: {e}", err=True)
        if verbose:
            import traceback
            click.echo(traceback.format_exc(), err=True)
        sys.exit(1)


@cli.group()
@click.pass_context
def project(ctx):
    """项目管理命令"""
    pass


@project.command()
@click.option('--name', '-n', prompt='项目名称', help='项目名称')
@click.option('--task-type', '-t', 
              type=click.Choice([t.value for t in TaskType]), 
              default='development', 
              help='任务类型')
@click.option('--description', '-d', prompt='项目描述', help='项目描述')
@click.option('--collaboration-mode', '-m',
              type=click.Choice([m.value for m in CollaborationMode]),
              help='协作模式')
@click.option('--agents', '-a', multiple=True, help='指定智能体')
@click.option('--interactive', '-i', is_flag=True, help='启动交互模式')
@click.option('--save-plan', '-s', type=click.Path(), help='保存执行计划到文件')
@click.pass_context
def create(ctx, name, task_type, description, collaboration_mode, agents, interactive, save_plan):
    """创建新项目"""
    click.echo(f"🚀 创建项目: {name}")
    
    project_commands = ctx.obj['project_commands']
    
    # 创建项目配置
    project_config = {
        "name": name,
        "task_type": task_type,
        "description": description,
        "collaboration_mode": collaboration_mode,
        "specified_agents": list(agents) if agents else None
    }
    
    if interactive:
        # 启动交互模式
        interactive_cli = InteractiveCLI(ctx.obj['supervisor'])
        asyncio.run(interactive_cli.start_project_session(project_config))
    else:
        # 直接执行
        asyncio.run(execute_project(project_commands, project_config, save_plan))


@project.command()
@click.option('--project-file', '-f', type=click.Path(exists=True), required=True, help='项目配置文件')
@click.option('--workflow-file', '-w', type=click.Path(exists=True), help='工作流定义文件')
@click.option('--parallel', '-p', is_flag=True, help='启用并行执行')
@click.pass_context
def execute(ctx, project_file, workflow_file, parallel):
    """执行项目"""
    project_commands = ctx.obj['project_commands']
    
    try:
        # 加载项目配置
        with open(project_file, 'r', encoding='utf-8') as f:
            if project_file.endswith('.yaml') or project_file.endswith('.yml'):
                project_config = yaml.safe_load(f)
            else:
                project_config = json.load(f)
        
        # 加载工作流定义（如果提供）
        workflow_def = None
        if workflow_file:
            with open(workflow_file, 'r', encoding='utf-8') as f:
                if workflow_file.endswith('.yaml') or workflow_file.endswith('.yml'):
                    workflow_def = yaml.safe_load(f)
                else:
                    workflow_def = json.load(f)
        
        # 执行项目
        asyncio.run(execute_project_with_workflow(
            project_commands, project_config, workflow_def, parallel
        ))
        
    except Exception as e:
        click.echo(f"❌ 项目执行失败: {e}", err=True)


@cli.group()
@click.pass_context
def agents(ctx):
    """智能体管理命令"""
    pass


@agents.command(name="list")
@click.pass_context
def list_agents(ctx):
    """列出所有智能体"""
    agent_commands = ctx.obj['agent_commands']
    agents_info = agent_commands.get_agents_info()
    
    click.echo("🤖 专业智能体列表:")
    for agent_id, info in agents_info.items():
        click.echo(f"\n  📋 {agent_id} ({info['name']})")
        click.echo(f"    能力: {', '.join(info['capabilities'])}")
        
        metrics = info['performance_metrics']
        if metrics.get('total_tasks', 0) > 0:
            click.echo(f"    性能: 成功率 {metrics['success_rate']:.1%}, "
                      f"平均执行时间 {metrics['average_execution_time']:.1f}s")


@agents.command()
@click.option('--agent-id', '-a', required=True, help='智能体ID')
@click.pass_context
def capabilities(ctx, agent_id):
    """显示智能体能力详情"""
    agent_commands = ctx.obj['agent_commands']
    capabilities = agent_commands.get_agent_capabilities(agent_id)
    
    if not capabilities:
        click.echo(f"❌ 智能体 {agent_id} 不存在")
        return
    
    click.echo(f"🎯 智能体 {agent_id} 能力详情:")
    click.echo(f"  名称: {capabilities['name']}")
    click.echo(f"  专业能力: {', '.join(capabilities['capabilities'])}")
    
    click.echo("\n  📊 能力评分:")
    for cap, score in capabilities['capability_scores'].items():
        if score > 0:
            stars = "⭐" * int(score * 5)
            click.echo(f"    {cap}: {score:.2f} {stars}")


@agents.command()
@click.option('--capability', '-c', 
              type=click.Choice([cap.value for cap in AgentCapability]),
              required=True, help='所需能力')
@click.pass_context
def recommend(ctx, capability):
    """推荐最佳智能体"""
    agent_commands = ctx.obj['agent_commands']
    recommendation = agent_commands.recommend_agent_for_capability(capability)
    
    if recommendation:
        click.echo(f"💡 推荐智能体: {recommendation['agent_id']}")
        click.echo(f"   能力评分: {recommendation['score']:.2f}")
        click.echo(f"   推荐理由: {recommendation['reason']}")
    else:
        click.echo(f"❌ 没有找到适合 {capability} 能力的智能体")


@cli.group()
@click.pass_context
def workflow(ctx):
    """工作流管理命令"""
    pass


@workflow.command(name="create")
@click.option('--template', '-t', help='工作流模板名称')
@click.option('--output', '-o', type=click.Path(), required=True, help='输出文件路径')
@click.option('--format', type=click.Choice(['yaml', 'json']), default='yaml', help='输出格式')
@click.pass_context
def create_workflow(ctx, template, output, format):
    """创建工作流定义"""
    workflow_commands = ctx.obj['workflow_commands']
    
    if template:
        # 从模板创建
        workflow_def = workflow_commands.create_from_template(template)
    else:
        # 交互式创建
        workflow_def = workflow_commands.create_interactive()
    
    # 保存到文件
    with open(output, 'w', encoding='utf-8') as f:
        if format == 'yaml':
            yaml.dump(workflow_def, f, default_flow_style=False, allow_unicode=True)
        else:
            json.dump(workflow_def, f, ensure_ascii=False, indent=2)
    
    click.echo(f"✅ 工作流定义已保存到: {output}")


@workflow.command()
@click.pass_context
def templates(ctx):
    """列出工作流模板"""
    workflow_commands = ctx.obj['workflow_commands']
    templates = workflow_commands.list_templates()
    
    click.echo("📋 可用工作流模板:")
    for template in templates:
        click.echo(f"  • {template['name']}: {template['description']}")


@workflow.command(name="execute")
@click.option('--workflow-file', '-f', type=click.Path(exists=True), required=True, help='工作流定义文件')
@click.option('--input-data', '-i', type=str, help='输入数据 (JSON格式)')
@click.option('--parallel', '-p', is_flag=True, help='启用并行执行')
@click.pass_context
def execute_workflow(ctx, workflow_file, input_data, parallel):
    """执行工作流"""
    workflow_commands = ctx.obj['workflow_commands']
    
    # 解析输入数据
    input_dict = {}
    if input_data:
        try:
            input_dict = json.loads(input_data)
        except json.JSONDecodeError:
            click.echo("❌ 输入数据格式错误，请使用有效的JSON格式", err=True)
            return
    
    # 执行工作流
    asyncio.run(execute_workflow_file(workflow_commands, workflow_file, input_dict, parallel))


@cli.group()
@click.pass_context
def monitor(ctx):
    """监控和分析命令"""
    pass


@monitor.command()
@click.pass_context
def dashboard(ctx):
    """显示监控仪表板"""
    monitoring_commands = ctx.obj['monitoring_commands']
    dashboard_data = monitoring_commands.get_dashboard_data()
    
    click.echo("📊 系统监控仪表板")
    click.echo("=" * 50)
    
    # 系统状态
    click.echo(f"🟢 系统状态: {dashboard_data['system_status']}")
    click.echo(f"🤖 活跃智能体: {dashboard_data['active_agents']}")
    click.echo(f"⚡ 运行中任务: {dashboard_data['running_tasks']}")
    
    # 性能指标
    perf = dashboard_data['performance_metrics']
    click.echo(f"\n📈 性能指标:")
    click.echo(f"  成功率: {perf['success_rate']:.1%}")
    click.echo(f"  平均响应时间: {perf['avg_response_time']:.2f}s")
    click.echo(f"  吞吐量: {perf['throughput']:.1f} 任务/分钟")


@monitor.command()
@click.option('--agent-id', '-a', help='特定智能体ID')
@click.option('--format', type=click.Choice(['table', 'json']), default='table', help='输出格式')
@click.pass_context
def performance(ctx, agent_id, format):
    """显示性能指标"""
    monitoring_commands = ctx.obj['monitoring_commands']
    
    if agent_id:
        metrics = monitoring_commands.get_agent_performance(agent_id)
        if not metrics:
            click.echo(f"❌ 智能体 {agent_id} 不存在或无性能数据")
            return
        
        if format == 'json':
            click.echo(json.dumps(metrics, ensure_ascii=False, indent=2))
        else:
            click.echo(f"📊 智能体 {agent_id} 性能指标:")
            click.echo(f"  总任务数: {metrics['total_tasks']}")
            click.echo(f"  成功率: {metrics['success_rate']:.1%}")
            click.echo(f"  平均执行时间: {metrics['average_execution_time']:.2f}s")
    else:
        all_metrics = monitoring_commands.get_all_performance_metrics()
        
        if format == 'json':
            click.echo(json.dumps(all_metrics, ensure_ascii=False, indent=2))
        else:
            click.echo("📊 所有智能体性能指标:")
            for agent_id, metrics in all_metrics.items():
                click.echo(f"\n  {agent_id}:")
                click.echo(f"    任务数: {metrics['total_tasks']}")
                click.echo(f"    成功率: {metrics['success_rate']:.1%}")


@cli.command()
@click.pass_context
def interactive(ctx):
    """启动交互式命令行界面"""
    click.echo("🎯 启动交互式命令行界面 v0.3.0...")
    interactive_cli = InteractiveCLI(ctx.obj['supervisor'])
    asyncio.run(interactive_cli.start())


@cli.command()
@click.pass_context
def status(ctx):
    """显示系统状态"""
    supervisor = ctx.obj['supervisor']
    status_info = supervisor.get_system_status()
    
    click.echo("📊 LangGraph多智能体系统 v0.3.0 状态:")
    click.echo(f"  版本: {status_info['supervisor_version']}")
    click.echo(f"  专业智能体: {status_info['total_agents']} 个")
    click.echo(f"  协作模式: {len(status_info['collaboration_modes'])} 种")
    click.echo(f"  系统能力: {len(status_info['capabilities'])} 项")
    click.echo(f"  模型: {status_info['model']}")
    click.echo(f"  状态: {status_info['status']}")
    
    # 性能指标
    perf = status_info['performance_metrics']
    if perf['total_tasks'] > 0:
        click.echo(f"\n📈 性能指标:")
        click.echo(f"  总任务数: {perf['total_tasks']}")
        click.echo(f"  成功率: {perf['success_rate']:.1%}")
        click.echo(f"  平均执行时间: {perf['average_execution_time']:.2f}s")


# 辅助函数
async def execute_project(project_commands, project_config, save_plan_path=None):
    """执行项目 v0.3.0"""
    try:
        click.echo("⚡ 开始执行项目...")
        
        # 创建项目
        project_state = await project_commands.create_project(project_config)
        
        # 生成执行计划
        plan = await project_commands.create_execution_plan(project_state)
        
        if save_plan_path:
            # 保存执行计划
            with open(save_plan_path, 'w', encoding='utf-8') as f:
                json.dump(plan, f, ensure_ascii=False, indent=2, default=str)
            click.echo(f"📋 执行计划已保存到: {save_plan_path}")
        
        # 执行项目
        result = await project_commands.execute_project(project_state)
        
        if result['status'] == 'completed':
            click.echo("✅ 项目执行完成!")
            click.echo(f"📊 执行摘要: {result['execution_summary']}")
            
            # 显示智能体贡献
            if 'agent_contributions' in result:
                click.echo("\n🤖 智能体贡献:")
                for agent_id, contribution in result['agent_contributions'].items():
                    click.echo(f"  {agent_id}: {contribution[:100]}...")
        else:
            click.echo(f"❌ 项目执行失败: {result.get('error', '未知错误')}")
            if 'errors' in result:
                for error in result['errors']:
                    click.echo(f"  • {error}")
                    
    except Exception as e:
        click.echo(f"💥 执行过程中发生错误: {e}")


async def execute_project_with_workflow(project_commands, project_config, workflow_def, parallel):
    """使用工作流执行项目"""
    try:
        click.echo("⚡ 使用工作流执行项目...")
        
        if workflow_def:
            # 使用自定义工作流
            result = await project_commands.execute_with_workflow(project_config, workflow_def, parallel)
        else:
            # 使用默认工作流
            result = await project_commands.execute_project_advanced(project_config, parallel)
        
        if result['status'] == 'completed':
            click.echo("✅ 工作流执行完成!")
            click.echo(f"📊 执行时间: {result.get('execution_time', 'N/A')}")
        else:
            click.echo(f"❌ 工作流执行失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        click.echo(f"💥 工作流执行错误: {e}")


async def execute_workflow_file(workflow_commands, workflow_file, input_data, parallel):
    """执行工作流文件"""
    try:
        click.echo(f"⚡ 执行工作流: {workflow_file}")
        
        result = await workflow_commands.execute_workflow_file(workflow_file, input_data, parallel)
        
        if result['status'] == 'completed':
            click.echo("✅ 工作流执行完成!")
            click.echo(f"📊 结果: {result['result'][:200]}...")
        else:
            click.echo(f"❌ 工作流执行失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        click.echo(f"💥 工作流执行错误: {e}")


if __name__ == '__main__':
    cli()