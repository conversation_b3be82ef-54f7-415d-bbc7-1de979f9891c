"""
LangGraph多智能体系统
基于LangGraph和Supervisor模式的多智能体协调系统
"""

__version__ = "0.2.0"
__author__ = "LangGraph Team"

# 核心系统
from .core import LangGraphSystem, AgentRegistry, ToolRegistry
from .core.exceptions import LangGraphError, AgentError, ToolError

# 状态管理
from .states.project_state import ProjectState, TaskType, AgentStatus, MessageType

# 智能体
from .agents.supervisor_agent import SupervisorAgent
from .agents.coder_agent import CoderAgent
from .agents.researcher_agent import ResearcherAgent

# 工作流
from .graphs.project_workflow import ProjectWorkflow

# LLM配置
from .llm.config import LLMConfig

__all__ = [
    # 核心系统
    "LangGraphSystem",
    "AgentRegistry",
    "ToolRegistry",
    
    # 异常
    "LangGraphError",
    "AgentError",
    "ToolError",
    
    # 状态管理
    "ProjectState",
    "TaskType",
    "AgentStatus",
    "MessageType",
    
    # 智能体
    "SupervisorAgent",
    "CoderAgent",
    "ResearcherAgent",
    
    # 工作流
    "ProjectWorkflow",
    
    # 配置
    "LLMConfig"
]

# 版本信息
def get_version():
    """获取版本信息"""
    return __version__

def get_system_info():
    """获取系统信息"""
    return {
        "name": "LangGraph Multi-Agent System",
        "version": __version__,
        "author": __author__,
        "description": "基于LangGraph和Supervisor模式的多智能体协调系统"
    }
