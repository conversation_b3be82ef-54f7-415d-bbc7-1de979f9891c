#!/usr/bin/env python3
"""
快速测试生成的智能待办事项管理器
"""

import sys
import subprocess
import time
import requests
from pathlib import Path


def test_backend_directly():
    """直接测试后端代码"""
    print("🧪 直接测试后端代码...")
    
    project_dir = Path(__file__).parent.parent / "workspace" / "SmartTodoManager"
    backend_file = project_dir / "backend" / "main.py"
    
    if not backend_file.exists():
        print("❌ 后端文件不存在")
        return False
    
    try:
        # 直接导入并测试后端模块
        sys.path.insert(0, str(project_dir))
        
        # 测试导入
        from backend.main import app, TodoCreate, TodoResponse
        print("✅ 后端模块导入成功")
        
        # 测试FastAPI应用
        from fastapi.testclient import TestClient
        client = TestClient(app)
        
        # 测试根端点
        response = client.get("/")
        if response.status_code == 200:
            print("✅ 根端点测试通过")
        else:
            print(f"❌ 根端点测试失败: {response.status_code}")
        
        # 测试获取待办事项
        response = client.get("/todos")
        if response.status_code == 200:
            todos = response.json()
            print(f"✅ 获取待办事项成功: {len(todos)} 个")
        else:
            print(f"❌ 获取待办事项失败: {response.status_code}")
        
        # 测试创建待办事项
        todo_data = {
            "title": "测试任务",
            "description": "这是一个测试任务",
            "priority": 2
        }
        response = client.post("/todos", json=todo_data)
        if response.status_code == 200:
            todo = response.json()
            print(f"✅ 创建待办事项成功: {todo['title']}")
        else:
            print(f"❌ 创建待办事项失败: {response.status_code}")
        
        # 再次获取待办事项，验证创建成功
        response = client.get("/todos")
        if response.status_code == 200:
            todos = response.json()
            print(f"✅ 验证创建成功: 现在有 {len(todos)} 个待办事项")
        
        return True
        
    except Exception as e:
        print(f"❌ 后端测试失败: {e}")
        return False


def test_frontend_file():
    """测试前端文件"""
    print("\n🌐 测试前端文件...")
    
    project_dir = Path(__file__).parent.parent / "workspace" / "SmartTodoManager"
    frontend_file = project_dir / "frontend" / "index.html"
    
    if not frontend_file.exists():
        print("❌ 前端文件不存在")
        return False
    
    try:
        content = frontend_file.read_text(encoding='utf-8')
        
        # 检查关键内容
        checks = [
            ("标题", "智能待办事项管理器" in content),
            ("表单", "todo-form" in content),
            ("JavaScript", "addTodo" in content),
            ("API调用", "fetch('/todos'" in content),
            ("样式", "style>" in content)
        ]
        
        for check_name, result in checks:
            if result:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
        
        file_size = frontend_file.stat().st_size
        print(f"   📊 文件大小: {file_size} bytes")
        
        return all(result for _, result in checks)
        
    except Exception as e:
        print(f"❌ 前端测试失败: {e}")
        return False


def test_project_files():
    """测试项目文件完整性"""
    print("\n📁 测试项目文件...")
    
    project_dir = Path(__file__).parent.parent / "workspace" / "SmartTodoManager"
    
    expected_files = {
        "README.md": "项目说明",
        "requirements.txt": "依赖列表",
        "docker-compose.yml": "Docker配置",
        "backend/main.py": "后端代码",
        "frontend/index.html": "前端界面",
        "tests/test_main.py": "测试代码",
        "docs/README.md": "文档"
    }
    
    results = {}
    for file_path, description in expected_files.items():
        full_path = project_dir / file_path
        exists = full_path.exists()
        results[file_path] = exists
        
        if exists:
            size = full_path.stat().st_size
            print(f"   ✅ {file_path} ({size} bytes) - {description}")
        else:
            print(f"   ❌ {file_path} - {description}")
    
    success_rate = sum(results.values()) / len(results) * 100
    print(f"   📊 完整性: {success_rate:.1f}% ({sum(results.values())}/{len(results)})")
    
    return success_rate >= 80


def test_code_quality():
    """测试代码质量"""
    print("\n🔍 测试代码质量...")
    
    project_dir = Path(__file__).parent.parent / "workspace" / "SmartTodoManager"
    backend_file = project_dir / "backend" / "main.py"
    
    if not backend_file.exists():
        print("❌ 后端文件不存在")
        return False
    
    try:
        content = backend_file.read_text(encoding='utf-8')
        
        # 代码质量检查
        quality_checks = [
            ("导入语句", "from fastapi import" in content),
            ("数据模型", "class TodoItem" in content),
            ("API端点", "@app.get" in content and "@app.post" in content),
            ("数据库配置", "SQLAlchemy" in content),
            ("类型注解", "-> " in content),
            ("文档字符串", '"""' in content),
            ("错误处理", "HTTPException" in content or "try:" in content)
        ]
        
        passed_checks = 0
        for check_name, result in quality_checks:
            if result:
                print(f"   ✅ {check_name}")
                passed_checks += 1
            else:
                print(f"   ❌ {check_name}")
        
        # 代码行数统计
        lines = content.split('\n')
        code_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]
        
        print(f"   📊 代码统计:")
        print(f"      总行数: {len(lines)}")
        print(f"      代码行数: {len(code_lines)}")
        print(f"      质量评分: {passed_checks}/{len(quality_checks)} ({passed_checks/len(quality_checks)*100:.1f}%)")
        
        return passed_checks >= len(quality_checks) * 0.7
        
    except Exception as e:
        print(f"❌ 代码质量测试失败: {e}")
        return False


def generate_summary_report():
    """生成测试总结报告"""
    print("\n📊 生成测试总结...")
    
    # 运行所有测试
    test_results = {
        "后端功能": test_backend_directly(),
        "前端文件": test_frontend_file(), 
        "项目文件": test_project_files(),
        "代码质量": test_code_quality()
    }
    
    # 计算总体结果
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    success_rate = passed_tests / total_tests * 100
    
    print(f"\n🎯 测试总结:")
    print(f"   总测试项: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, result in test_results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
    
    # 生成建议
    print(f"\n💡 建议:")
    if success_rate >= 90:
        print("   🎉 项目生成质量优秀！可以直接使用。")
    elif success_rate >= 70:
        print("   👍 项目生成质量良好，建议进行小幅优化。")
    elif success_rate >= 50:
        print("   ⚠️  项目生成质量一般，需要进行一些修复。")
    else:
        print("   🔧 项目生成需要大幅改进。")
    
    print(f"\n🚀 下一步:")
    print("   1. 查看生成的代码: workspace/SmartTodoManager/")
    print("   2. 运行后端: cd workspace/SmartTodoManager && python backend/main.py")
    print("   3. 访问API文档: http://localhost:8000/docs")
    print("   4. 查看前端: workspace/SmartTodoManager/frontend/index.html")
    
    return success_rate >= 70


def main():
    """主函数"""
    print("🎯 智能待办事项管理器 - 快速测试")
    print("=" * 50)
    
    success = generate_summary_report()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 测试完成 - 项目生成成功！")
        return 0
    else:
        print("❌ 测试完成 - 项目需要改进")
        return 1


if __name__ == "__main__":
    sys.exit(main())
