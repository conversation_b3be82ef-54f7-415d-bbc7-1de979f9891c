#!/usr/bin/env python3
"""
重构后系统验证脚本
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_core_system():
    """测试核心系统"""
    print("🧪 测试核心系统...")
    
    try:
        from langgraph_system import LangGraphSystem, TaskType, LLMConfig
        
        # 测试系统初始化
        print("  ✓ 导入成功")
        
        # 创建系统实例
        system = LangGraphSystem()
        print("  ✓ 系统创建成功")
        
        # 测试系统信息
        info = system.get_system_info()
        print(f"  ✓ 系统信息: {info['name']} v{info['version']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 核心系统测试失败: {e}")
        return False

def test_registry_system():
    """测试注册中心"""
    print("🧪 测试注册中心...")
    
    try:
        from langgraph_system.core.registry import agent_registry, tool_registry, register_tool
        
        # 测试工具注册
        @register_tool("test_tool", {"description": "测试工具"})
        def test_tool(message: str) -> str:
            return f"测试: {message}"
        
        print("  ✓ 工具注册成功")
        
        # 测试工具执行
        result = tool_registry.execute_tool("test_tool", message="Hello")
        assert result == "测试: Hello"
        print("  ✓ 工具执行成功")
        
        # 测试工具列表
        tools = tool_registry.list_tools()
        assert "test_tool" in tools
        print(f"  ✓ 工具列表: {len(tools)} 个工具")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 注册中心测试失败: {e}")
        return False

def test_enhanced_tools():
    """测试增强工具"""
    print("🧪 测试增强工具...")
    
    try:
        from langgraph_system.core.registry import tool_registry
        
        # 确保增强工具已加载
        from langgraph_system.tools import enhanced_tools
        
        # 测试文件写入
        result = tool_registry.execute_tool(
            "write_file", 
            file_path="test_file.txt", 
            content="这是一个测试文件"
        )
        print("  ✓ 文件写入成功")
        
        # 测试文件读取
        content = tool_registry.execute_tool("read_file", file_path="test_file.txt")
        assert content == "这是一个测试文件"
        print("  ✓ 文件读取成功")
        
        # 测试文件列表
        files = tool_registry.execute_tool("list_files", directory_path=".")
        assert "test_file.txt" in files
        print("  ✓ 文件列表成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 增强工具测试失败: {e}")
        return False

async def test_agents():
    """测试智能体"""
    print("🧪 测试智能体...")
    
    try:
        from langgraph_system.agents.coder_agent import CoderAgent
        from langgraph_system.agents.researcher_agent import ResearcherAgent
        from langgraph_system.core.registry import agent_registry
        
        # 创建智能体实例
        coder = CoderAgent()
        researcher = ResearcherAgent()
        
        print("  ✓ 智能体创建成功")
        
        # 检查注册
        agents = agent_registry.list_agents()
        print(f"  ✓ 已注册智能体: {agents}")
        
        # 测试智能体能力
        coder_caps = coder.get_capabilities()
        researcher_caps = researcher.get_capabilities()
        
        print(f"  ✓ 编码智能体能力: {coder_caps['supported_tasks']}")
        print(f"  ✓ 研究智能体能力: {researcher_caps['supported_tasks']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 智能体测试失败: {e}")
        return False

def test_exceptions():
    """测试异常处理"""
    print("🧪 测试异常处理...")
    
    try:
        from langgraph_system.core.exceptions import LangGraphError, AgentError, ToolError
        
        # 测试基础异常
        try:
            raise LangGraphError("测试异常", "TEST_ERROR", {"detail": "测试详情"})
        except LangGraphError as e:
            assert e.error_code == "TEST_ERROR"
            assert e.details["detail"] == "测试详情"
        
        print("  ✓ 基础异常处理正常")
        
        # 测试智能体异常
        try:
            raise AgentError("test_agent", "智能体错误", "AGENT_ERROR")
        except AgentError as e:
            assert e.agent_name == "test_agent"
            assert e.error_code == "AGENT_ERROR"
        
        print("  ✓ 智能体异常处理正常")
        
        # 测试工具异常
        try:
            raise ToolError("test_tool", "工具错误", "TOOL_ERROR")
        except ToolError as e:
            assert e.tool_name == "test_tool"
            assert e.error_code == "TOOL_ERROR"
        
        print("  ✓ 工具异常处理正常")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 异常处理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始验证重构后的系统...\n")
    
    tests = [
        ("核心系统", test_core_system()),
        ("注册中心", test_registry_system()),
        ("增强工具", test_enhanced_tools()),
        ("智能体", test_agents()),
        ("异常处理", test_exceptions())
    ]
    
    results = []
    for name, test in tests:
        if asyncio.iscoroutine(test):
            result = await test
        else:
            result = test
        results.append((name, result))
        print()
    
    # 汇总结果
    print("📊 测试结果汇总:")
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统重构成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)