"""基础LLM客户端"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage


class BaseLLMClient(ABC):
    """基础LLM客户端抽象类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化客户端"""
        self.config = config
        self._client = None
    
    @abstractmethod
    def create_client(self) -> Any:
        """创建LLM客户端"""
        pass
    
    @abstractmethod
    async def generate(
        self,
        messages: List[BaseMessage],
        **kwargs
    ) -> AIMessage:
        """生成回复"""
        pass
    
    @abstractmethod
    async def stream(
        self,
        messages: List[BaseMessage],
        **kwargs
    ) -> Any:
        """流式生成回复"""
        pass
    
    def format_messages(self, messages: List[Dict[str, str]]) -> List[BaseMessage]:
        """格式化消息"""
        formatted = []
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            if role == "user":
                formatted.append(HumanMessage(content=content))
            elif role == "assistant":
                formatted.append(AIMessage(content=content))
            else:
                formatted.append(HumanMessage(content=content))
        
        return formatted
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """获取使用统计"""
        return {
            "provider": self.config.get("provider", "unknown"),
            "model": self.config.get("model", "unknown"),
            "config": self.config
        }
    
    def get_model(self) -> Any:
        """获取LangChain模型实例"""
        return self.create_client()
