# TestProject_20250726_100441 API 文档

## 概述

TestProject_20250726_100441 提供了一套完整的 RESTful API，用于管理项目数据。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: v1
- **数据格式**: JSON

## 认证

目前API不需要认证，但建议在生产环境中添加适当的认证机制。

## API端点

### 1. 健康检查

**GET** `/health`

检查API服务状态。

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 2. 获取项目列表

**GET** `/api/items`

获取所有项目的列表。

**响应示例**:
```json
[
  {
    "id": 1,
    "title": "项目标题",
    "description": "项目描述",
    "completed": false,
    "created_at": "2024-01-01T12:00:00"
  }
]
```

### 3. 创建新项目

**POST** `/api/items`

创建一个新的项目。

**请求体**:
```json
{
  "title": "项目标题",
  "description": "项目描述（可选）"
}
```

**响应示例**:
```json
{
  "id": 1,
  "title": "项目标题",
  "description": "项目描述",
  "completed": false,
  "created_at": "2024-01-01T12:00:00"
}
```

### 4. 更新项目状态

**PUT** `/api/items/{item_id}`

更新指定项目的完成状态。

**查询参数**:
- `completed` (boolean): 项目是否完成

**响应示例**:
```json
{
  "message": "Item updated successfully"
}
```

### 5. 删除项目

**DELETE** `/api/items/{item_id}`

删除指定的项目。

**响应示例**:
```json
{
  "message": "Item deleted successfully"
}
```

## 错误处理

API使用标准的HTTP状态码：

- `200` - 成功
- `404` - 资源未找到
- `422` - 请求数据验证失败
- `500` - 服务器内部错误

错误响应格式：
```json
{
  "detail": "错误描述"
}
```

## 开发工具

### 交互式API文档

启动服务后，可以访问以下地址查看交互式API文档：

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### 测试

运行测试：
```bash
pytest tests/
```
