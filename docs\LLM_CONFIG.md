# LLM配置指南

本文档介绍如何在LangGraph多智能体系统中配置和使用不同的LLM模型。

## 支持的LLM提供商

### 1. Moonshot AI (kimi-k2-0711-preview)
- **模型名称**: `kimi-k2-0711-preview`
- **提供商**: Moonshot AI
- **API端点**: `https://api.moonshot.cn/v1`

### 2. OpenAI
- **模型名称**: `gpt-4`, `gpt-3.5-turbo` 等
- **提供商**: OpenAI
- **API端点**: `https://api.openai.com/v1`

### 3. Anthropic
- **模型名称**: `claude-3-sonnet-20240229`, `claude-3-opus-20240229` 等
- **提供商**: Anthropic
- **API端点**: 官方Anthropic API

## 配置方法

### 环境变量配置

在 `.env` 文件中设置：

```bash
# 选择提供商和模型
LANGGRAPH_DEFAULT_PROVIDER=moonshot
LANGGRAPH_DEFAULT_MODEL=kimi-k2-0711-preview

# API密钥
LANGGRAPH_MOONSHOT_API_KEY=your-moonshot-api-key-here
LANGGRAPH_OPENAI_API_KEY=your-openai-api-key-here
LANGGRAPH_ANTHROPIC_API_KEY=your-anthropic-api-key-here
```

### 代码配置

```python
from langgraph_system.llm import LLMConfig, LLMFactory

# 使用kimi-k2-0711-preview
config = LLMConfig.for_kimi_k2_0711_preview(
    temperature=0.7,
    max_tokens=2000
)

# 创建客户端
client = LLMFactory.get_client(config)

# 使用客户端
messages = [{"role": "user", "content": "你好！"}]
response = await client.generate(client.format_messages(messages))
```

## 使用示例

### 1. 基本使用

```python
import asyncio
from langgraph_system.llm import LLMConfig, LLMFactory

async def main():
    # 配置kimi-k2-0711-preview
    config = LLMConfig.for_kimi_k2_0711_preview()
    client = LLMFactory.get_client(config)
    
    # 发送消息
    messages = [{"role": "user", "content": "你好！"}]
    response = await client.generate(client.format_messages(messages))
    print(response.content)

asyncio.run(main())
```

### 2. 自定义参数

```python
config = LLMConfig(
    provider="moonshot",
    model="kimi-k2-0711-preview",
    temperature=0.8,
    max_tokens=1500,
    timeout=120
)
```

### 3. 切换提供商

```python
# 切换到OpenAI
config = LLMConfig(
    provider="openai",
    model="gpt-4",
    openai_api_key="your-key"
)

# 切换到Anthropic
config = LLMConfig(
    provider="anthropic",
    model="claude-3-sonnet-20240229",
    anthropic_api_key="your-key"
)
```

## 环境变量参考

| 变量名 | 描述 | 示例 |
|--------|------|------|
| `LANGGRAPH_DEFAULT_PROVIDER` | 默认LLM提供商 | `moonshot` |
| `LANGGRAPH_DEFAULT_MODEL` | 默认模型名称 | `kimi-k2-0711-preview` |
| `LANGGRAPH_MOONSHOT_API_KEY` | Moonshot API密钥 | `sk-...` |
| `LANGGRAPH_OPENAI_API_KEY` | OpenAI API密钥 | `sk-...` |
| `LANGGRAPH_ANTHROPIC_API_KEY` | Anthropic API密钥 | `sk-ant-...` |

## 故障排除

### 常见问题

1. **API密钥错误**
   - 确保API密钥正确且有效
   - 检查环境变量是否正确设置

2. **网络连接问题**
   - 检查网络连接
   - 确认API端点可访问

3. **模型不可用**
   - 确认模型名称正确
   - 检查API密钥是否有权限访问该模型

### 调试模式

启用调试日志：
```bash
export LANGGRAPH_DEBUG=true
export LANGGRAPH_LOG_LEVEL=DEBUG
