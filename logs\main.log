2025-07-26 06:56:58,222 - langgraph_system.workflow.workflow_engine - INFO - 高级工作流引擎初始化完成
2025-07-26 06:56:58,222 - langgraph_system.workflow.workflow_monitor - INFO - 工作流监控器初始化完成
2025-07-26 06:56:58,339 - langgraph_system.agents.supervisor_agent - INFO - Supervisor智能体 v0.3.0 初始化完成
2025-07-26 06:57:46,424 - langgraph_system.core.system - INFO - 初始化 LangGraph Multi-Agent System v0.2.0
2025-07-26 06:57:46,424 - main - INFO - ✅ LangGraph系统初始化成功
2025-07-26 06:57:46,426 - main - INFO - 🔄 系统正在关闭...
2025-07-26 06:57:46,426 - main - INFO - ✅ 系统已关闭
2025-07-26 06:57:46,427 - langgraph_system.core.system - INFO - 初始化 LangGraph Multi-Agent System v0.2.0
2025-07-26 06:57:46,428 - langgraph_system.core.system - INFO - 初始化 LangGraph Multi-Agent System v0.2.0
2025-07-26 06:57:46,428 - main - INFO - ✅ LangGraph系统初始化成功
2025-07-26 06:57:46,428 - main - INFO - 系统状态: {'status': 'error', 'message': "'capabilities'"}
2025-07-26 06:57:47,553 - langgraph_system.workflow.workflow_engine - INFO - 高级工作流引擎初始化完成
2025-07-26 06:57:47,553 - langgraph_system.workflow.workflow_monitor - INFO - 工作流监控器初始化完成
2025-07-26 06:57:47,676 - langgraph_system.agents.supervisor_agent - INFO - Supervisor智能体 v0.3.0 初始化完成
2025-07-26 06:57:47,678 - langgraph_system.workflow.workflow_engine - INFO - 高级工作流引擎初始化完成
2025-07-26 06:57:47,678 - langgraph_system.workflow.workflow_monitor - INFO - 工作流监控器初始化完成
2025-07-26 06:57:47,727 - langgraph_system.agents.supervisor_agent - INFO - Supervisor智能体 v0.3.0 初始化完成
2025-07-26 06:57:47,729 - langgraph_system.core.registry - INFO - 工具 'read_file' 注册成功
2025-07-26 06:57:47,729 - langgraph_system.core.registry - INFO - 工具 'write_file' 注册成功
2025-07-26 06:57:47,729 - langgraph_system.core.registry - INFO - 工具 'list_files' 注册成功
2025-07-26 06:57:47,730 - langgraph_system.core.registry - INFO - 工具 'create_directory' 注册成功
2025-07-26 06:57:47,730 - langgraph_system.core.registry - INFO - 工具 'delete_file' 注册成功
2025-07-26 06:57:47,730 - langgraph_system.core.registry - INFO - 工具 'read_json' 注册成功
2025-07-26 06:57:47,730 - langgraph_system.core.registry - INFO - 工具 'write_json' 注册成功
2025-07-26 06:57:47,730 - langgraph_system.core.registry - INFO - 工具 'execute_command' 注册成功
2025-07-26 06:57:47,730 - langgraph_system.core.registry - INFO - 工具 'search_files' 注册成功
2025-07-26 06:57:47,730 - langgraph_system.core.registry - INFO - 工具 'get_file_info' 注册成功
2025-07-26 06:57:47,730 - langgraph_system.tools.tool_executor - INFO - 增强工具模块已加载
2025-07-26 06:57:47,730 - langgraph_system.tools.tool_executor - INFO - 工具执行器已初始化，可用工具: ['read_file', 'write_file', 'list_files', 'create_directory', 'delete_file', 'read_json', 'write_json', 'execute_command', 'search_files', 'get_file_info']
2025-07-26 06:57:47,738 - langgraph_system.core.registry - WARNING - 工具 'read_file' 已存在，将被覆盖
2025-07-26 06:57:47,738 - langgraph_system.core.registry - INFO - 工具 'read_file' 注册成功
2025-07-26 06:57:47,738 - langgraph_system.core.registry - WARNING - 工具 'write_file' 已存在，将被覆盖
2025-07-26 06:57:47,738 - langgraph_system.core.registry - INFO - 工具 'write_file' 注册成功
2025-07-26 06:57:47,738 - langgraph_system.core.registry - WARNING - 工具 'list_files' 已存在，将被覆盖
2025-07-26 06:57:47,738 - langgraph_system.core.registry - INFO - 工具 'list_files' 注册成功
2025-07-26 06:57:47,739 - langgraph_system.core.system - INFO - 系统初始化完成
2025-07-26 06:57:47,739 - langgraph_system.core.system - INFO - 创建项目: DemoProject
2025-07-26 06:57:47,739 - langgraph_system.graphs.project_workflow - INFO - Starting workflow execution for project: DemoProject
2025-07-26 06:57:47,745 - langgraph_system.graphs.project_workflow - INFO - Executing supervisor node
2025-07-26 06:57:47,745 - langgraph_system.graphs.project_workflow - INFO - --- SUPERVISOR NODE START ---
2025-07-26 06:57:47,745 - langgraph_system.graphs.project_workflow - INFO - State before supervisor: {}
2025-07-26 06:57:58,282 - httpx - INFO - HTTP Request: POST https://api.moonshot.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-26 06:58:11,526 - httpx - INFO - HTTP Request: POST https://api.moonshot.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-26 07:00:11,540 - openai._base_client - INFO - Retrying request to /chat/completions in 0.432737 seconds
2025-07-26 07:00:17,961 - httpx - INFO - HTTP Request: POST https://api.moonshot.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-26 07:00:25,019 - httpx - INFO - HTTP Request: POST https://api.moonshot.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-26 07:02:25,028 - openai._base_client - INFO - Retrying request to /chat/completions in 0.435844 seconds
2025-07-26 07:04:25,641 - openai._base_client - INFO - Retrying request to /chat/completions in 0.841268 seconds
2025-07-26 07:09:19,943 - langgraph_system.agents.supervisor_agent - INFO - Supervisor智能体 v0.3.0 初始化完成（轻量级模式）
2025-07-26 07:09:37,563 - langgraph_system.agents.supervisor_agent - INFO - Supervisor智能体 v0.3.0 初始化完成（轻量级模式）
2025-07-26 07:15:14,623 - langgraph_system.core.system - INFO - 初始化 LangGraph Multi-Agent System v0.2.0
2025-07-26 07:15:14,623 - main - INFO - ✅ LangGraph系统初始化成功（轻量级模式）
2025-07-26 07:15:14,623 - main - INFO - 🔄 系统正在关闭...
2025-07-26 07:15:14,624 - main - INFO - ✅ 系统已关闭
2025-07-26 07:15:14,625 - langgraph_system.core.system - INFO - 初始化 LangGraph Multi-Agent System v0.2.0
2025-07-26 07:15:14,627 - langgraph_system.core.system - INFO - 初始化 LangGraph Multi-Agent System v0.2.0
2025-07-26 07:15:14,627 - main - INFO - ✅ LangGraph系统初始化成功（轻量级模式）
2025-07-26 07:15:14,627 - main - INFO - 系统状态: {'status': 'healthy', 'version': '0.2.0', 'name': 'LangGraph Multi-Agent System', 'initialized': True, 'timestamp': 696330.577725, 'mode': 'lightweight'}
2025-07-26 07:15:14,627 - langgraph_system.core.system - INFO - 创建项目: DemoProject
2025-07-26 07:15:14,627 - main - INFO - 🔄 系统正在关闭...
2025-07-26 07:15:14,627 - main - INFO - ✅ 系统已关闭
