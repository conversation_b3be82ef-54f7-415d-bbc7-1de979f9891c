# Core Framework
langgraph>=0.2.0
langchain>=0.1.0
langchain-core>=0.1.0
langchain-openai>=0.1.0
langchain-anthropic>=0.1.0
pydantic>=2.0.0
fastapi>=0.104.0
uvicorn>=0.24.0

# Async and Concurrency
asyncio-mqtt>=0.13.0
aiohttp>=3.9.0
aiofiles>=23.2.0
asyncpg>=0.29.0

# Data Storage and Cache
redis>=5.0.0
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0
alembic>=1.12.0

# Vector Database
chromadb>=0.4.0

# Knowledge Graph
neo4j>=5.13.0
py2neo>=2021.2.3

# Monitoring and Logging
prometheus-client>=0.19.0
psutil>=5.9.0
structlog>=23.2.0

# Web Interface
streamlit>=1.28.0
plotly>=5.17.0
pandas>=2.1.0

# CLI Tools
click>=8.1.0
rich>=13.7.0
typer>=0.9.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
pytest-cov>=4.1.0

# Development Tools
black>=23.9.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.6.0
pre-commit>=3.5.0

# Environment Configuration
python-dotenv>=1.0.0
pyyaml>=6.0.1

# HTTP Client
httpx>=0.25.0
requests>=2.31.0

# Serialization
msgpack>=1.0.7

# Time Handling
python-dateutil>=2.8.2
pytz>=2023.3

# Security
cryptography>=41.0.0
passlib>=1.7.4
python-jose>=3.3.0

# File Handling
openpyxl>=3.1.0
python-multipart>=0.0.6

# Network and Communication
websockets>=12.0
pika>=1.3.0

# Machine Learning and AI
numpy>=1.24.0
scikit-learn>=1.3.0
transformers>=4.35.0

# Visualization
matplotlib>=3.8.0
seaborn>=0.13.0
networkx>=3.2.0

# Configuration Management
hydra-core>=1.3.0
omegaconf>=2.3.0

# Distributed Computing
celery>=5.3.0
flower>=2.0.0

# Containerization
docker>=6.1.0

# Development and Debugging
ipython>=8.17.0
jupyter>=1.0.0
memory-profiler>=0.61.0
line-profiler>=4.1.0

# API Documentation
fastapi-users>=12.1.0

# Internationalization
babel>=2.13.0

# Utilities
tqdm>=4.66.0
colorama>=0.4.6
tabulate>=0.9.0
