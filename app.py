import streamlit as st
import streamlit as st
import asyncio
import threading
import time
import queue
import logging
from src.langgraph_system import ProjectState, TaskType, MessageType, AgentStatus
from src.langgraph_system.graphs import ProjectWorkflow

# --- 日志配置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# --- UI 配置 ---
st.set_page_config(page_title="LangGraph Multi-Agent System", layout="wide")
st.title("🤖 LangGraph 多智能体协作平台")

# --- 状态管理 ---
if "state" not in st.session_state:
    st.session_state.state = None
if "workflow_running" not in st.session_state:
    st.session_state.workflow_running = False
if "state_queue" not in st.session_state:
    st.session_state.state_queue = queue.Queue()

# --- 侧边栏输入 ---
with st.sidebar:
    st.header("项目配置")
    project_name = st.text_input("项目名称", "WebApp Project")
    task_options = [t.value for t in TaskType]
    task_type_str = st.selectbox("任务类型", options=task_options, index=task_options.index("development"))
    description = st.text_area("项目需求描述", "Create a simple Python web server using Flask.")

    if st.button("🚀 启动项目", disabled=st.session_state.workflow_running):
        st.session_state.workflow_running = True
        # 初始化状态
        st.session_state.state = ProjectState(
            project_name=project_name,
            current_task=TaskType(task_type_str),
            context={"description": description, "topic": description}, # 将描述也用作研究主题
            execution_status="running"
        )
        st.rerun()

# --- 主内容区域 ---
if st.session_state.state:
    state = st.session_state.state
    
    # --- 状态和消息显示 ---
    col1, col2 = st.columns(2)
    with col1:
        st.subheader("📊 项目状态")
        st.json(state.model_dump(exclude={'messages'}), expanded=True)

    with col2:
        st.subheader("💬 消息流")
        chat_container = st.container(height=400)
        for msg in state.messages:
            with chat_container.chat_message(name=msg.sender, avatar="🧑‍💻" if msg.sender != "user" else "👤"):
                st.write(f"**To: {msg.recipient}** ({msg.message_type.value})")
                st.write(msg.content)

    # --- 工作流执行 (使用线程和队列进行通信) ---
    def run_workflow_in_thread(initial_state: ProjectState, q: queue.Queue):
        """在单独的线程中运行工作流"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        workflow = ProjectWorkflow()
        
        current_state = initial_state
        while current_state.execution_status in ["running"]:
            result = loop.run_until_complete(workflow.execute(current_state))
            
            if "final_state" in result:
                current_state = ProjectState.from_dict(result["final_state"])
                q.put(current_state) # 将新状态放入队列
            else:
                st.error(f"工作流执行出错: {result.get('error', '未知错误')}")
                current_state.execution_status = "failed"
                q.put(current_state)
                break
        
        # 最终状态
        q.put(current_state)

    # 从队列中获取更新
    try:
        while not st.session_state.state_queue.empty():
            st.session_state.state = st.session_state.state_queue.get_nowait()
            if st.session_state.state.execution_status not in ["running"]:
                st.session_state.workflow_running = False
    except queue.Empty:
        pass

    if st.session_state.workflow_running:
        # 检查是否已有线程在运行
        if 'workflow_thread' not in st.session_state or not st.session_state.workflow_thread.is_alive():
            thread = threading.Thread(
                target=run_workflow_in_thread,
                args=(st.session_state.state, st.session_state.state_queue)
            )
            st.session_state.workflow_thread = thread
            thread.start()
        
        # 定期刷新UI以显示更新
        time.sleep(1)
        st.rerun()


    # --- 人机交互处理 ---
    if state.execution_status == "paused_for_input":
        st.info("⏳ 工作流已暂停，等待您的输入。")
        prompt_message = "请提供您的反馈或输入 'approve' 继续。"
        if state.messages and state.messages[-1].message_type == MessageType.QUERY:
            prompt_message = state.messages[-1].content

        with st.form("user_input_form"):
            user_input = st.text_area("您的反馈:", key="user_feedback")
            submitted = st.form_submit_button("✅ 提交")

            if submitted:
                # 更新状态
                state.add_message("user", "system", user_input, MessageType.RESULT)
                if user_input.lower() == 'approve':
                    waiting_agent = next((agent for agent, status in state.agent_status.items() if status == "waiting_for_user"), None)
                    if waiting_agent:
                        state.update_agent_status(waiting_agent, "completed")
                
                state.execution_status = "running"
                st.session_state.state = state
                st.session_state.workflow_running = True
                st.rerun()

else:
    st.info("请在左侧配置您的项目并点击“启动项目”开始。")
