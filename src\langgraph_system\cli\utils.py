"""
CLI工具函数
"""

import sys
import time
import signal
import asyncio
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, timedelta
from pathlib import Path
from contextlib import contextmanager

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax
from rich.tree import Tree
from rich.text import Text

from ..states.project_state import ProjectState, TaskType, AgentStatus, MessageType

class CLIFormatter:
    """CLI格式化工具"""
    
    def __init__(self, console: Console = None):
        self.console = console or Console()
    
    def format_project_state(self, state: ProjectState) -> Table:
        """格式化项目状态"""
        table = Table(title="项目状态", show_header=True, header_style="bold magenta")
        table.add_column("属性", style="cyan", no_wrap=True)
        table.add_column("值", style="white")
        
        table.add_row("项目名称", state.project_name)
        table.add_row("项目ID", state.project_id)
        table.add_row("当前任务", str(state.current_task) if state.current_task else "无")
        table.add_row("执行状态", state.execution_status)
        table.add_row("当前智能体", str(state.current_agent) if state.current_agent else "无")
        table.add_row("消息数量", str(len(state.messages)))
        table.add_row("开始时间", state.start_time.strftime("%Y-%m-%d %H:%M:%S"))
        table.add_row("最后更新", state.last_update.strftime("%Y-%m-%d %H:%M:%S"))
        
        return table
    
    def format_agent_status(self, agent_status: Dict[str, AgentStatus]) -> Table:
        """格式化智能体状态"""
        table = Table(title="智能体状态", show_header=True, header_style="bold blue")
        table.add_column("智能体", style="cyan", no_wrap=True)
        table.add_column("状态", style="white")
        table.add_column("状态图标", style="white", no_wrap=True)
        
        status_icons = {
            AgentStatus.IDLE: "⚪",
            AgentStatus.WORKING: "🟡",
            AgentStatus.COMPLETED: "🟢",
            AgentStatus.FAILED: "🔴",
            AgentStatus.WAITING: "🟠",
            AgentStatus.WAITING_FOR_USER: "🔵"
        }
        
        for agent, status in agent_status.items():
            icon = status_icons.get(status, "❓")
            table.add_row(agent, status.value, icon)
        
        return table
    
    def format_messages(self, messages: List[Any], limit: int = 10) -> Table:
        """格式化消息列表"""
        table = Table(title="消息历史", show_header=True, header_style="bold green")
        table.add_column("时间", style="dim", width=12)
        table.add_column("发送者", style="cyan", width=12)
        table.add_column("接收者", style="green", width=12)
        table.add_column("类型", style="yellow", width=8)
        table.add_column("内容", style="white")
        
        recent_messages = messages[-limit:] if len(messages) > limit else messages
        
        for msg in recent_messages:
            content = str(msg.content)
            if len(content) > 50:
                content = content[:47] + "..."
            
            table.add_row(
                msg.timestamp.strftime("%H:%M:%S"),
                msg.sender,
                msg.recipient,
                msg.message_type.value,
                content
            )
        
        return table
    
    def format_task_plan(self, plan: List[Dict[str, Any]], task_type: str) -> Table:
        """格式化任务计划"""
        table = Table(title=f"{task_type} 任务执行计划", show_header=True, header_style="bold yellow")
        table.add_column("步骤", style="cyan", width=6)
        table.add_column("智能体", style="green", width=12)
        table.add_column("动作", style="yellow", width=15)
        table.add_column("描述", style="white")
        
        for step in plan:
            table.add_row(
                str(step.get('step', '')),
                step.get('agent', ''),
                step.get('action', ''),
                step.get('description', '')
            )
        
        return table
    
    def format_system_info(self, info: Dict[str, Any]) -> Panel:
        """格式化系统信息"""
        content = []
        for key, value in info.items():
            if isinstance(value, list):
                value = ", ".join(str(v) for v in value)
            content.append(f"[bold]{key}:[/bold] {value}")
        
        return Panel(
            "\n".join(content),
            title="系统信息",
            border_style="blue"
        )
    
    def format_tree_structure(self, data: Dict[str, Any], title: str = "数据结构") -> Tree:
        """格式化树形结构"""
        tree = Tree(title)
        
        def add_items(parent, items):
            if isinstance(items, dict):
                for key, value in items.items():
                    if isinstance(value, (dict, list)):
                        branch = parent.add(f"[bold]{key}[/bold]")
                        add_items(branch, value)
                    else:
                        parent.add(f"{key}: {value}")
            elif isinstance(items, list):
                for i, item in enumerate(items):
                    if isinstance(item, (dict, list)):
                        branch = parent.add(f"[bold][{i}][/bold]")
                        add_items(branch, item)
                    else:
                        parent.add(f"[{i}]: {item}")
        
        add_items(tree, data)
        return tree

class CLIValidator:
    """CLI验证工具"""
    
    @staticmethod
    def validate_project_name(name: str) -> bool:
        """验证项目名称"""
        if not name or len(name.strip()) == 0:
            return False
        
        # 检查非法字符
        invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        return not any(char in name for char in invalid_chars)
    
    @staticmethod
    def validate_task_type(task_type: str) -> bool:
        """验证任务类型"""
        try:
            TaskType(task_type)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_file_path(path: str, must_exist: bool = False) -> bool:
        """验证文件路径"""
        try:
            file_path = Path(path)
            if must_exist:
                return file_path.exists()
            else:
                # 检查父目录是否可写
                return file_path.parent.exists() or file_path.parent == file_path
        except Exception:
            return False
    
    @staticmethod
    def validate_json_string(json_str: str) -> bool:
        """验证JSON字符串"""
        try:
            import json
            json.loads(json_str)
            return True
        except (json.JSONDecodeError, TypeError):
            return False

class CLIProgress:
    """CLI进度管理"""
    
    def __init__(self, console: Console = None):
        self.console = console or Console()
        self.progress = None
    
    @contextmanager
    def spinner(self, description: str = "处理中..."):
        """旋转进度条上下文管理器"""
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task(description, total=None)
            yield progress, task
    
    @contextmanager
    def progress_bar(self, total: int, description: str = "进度"):
        """进度条上下文管理器"""
        with Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=self.console
        ) as progress:
            task = progress.add_task(description, total=total)
            yield progress, task

class CLIInput:
    """CLI输入工具"""
    
    def __init__(self, console: Console = None):
        self.console = console or Console()
    
    def prompt_project_info(self) -> Dict[str, str]:
        """提示输入项目信息"""
        project_name = Prompt.ask("项目名称")
        while not CLIValidator.validate_project_name(project_name):
            self.console.print("[red]项目名称无效，请重新输入[/red]")
            project_name = Prompt.ask("项目名称")
        
        task_type = Prompt.ask(
            "任务类型",
            choices=[t.value for t in TaskType],
            default="development"
        )
        
        description = Prompt.ask("项目描述", default="")
        
        return {
            "name": project_name,
            "task_type": task_type,
            "description": description
        }
    
    def confirm_action(self, message: str, default: bool = False) -> bool:
        """确认操作"""
        return Confirm.ask(message, default=default)
    
    def select_from_list(self, items: List[str], title: str = "请选择") -> Optional[str]:
        """从列表中选择"""
        if not items:
            return None
        
        self.console.print(f"\n[bold]{title}:[/bold]")
        for i, item in enumerate(items, 1):
            self.console.print(f"  {i}. {item}")
        
        while True:
            try:
                choice = Prompt.ask("请输入选项编号", default="1")
                index = int(choice) - 1
                if 0 <= index < len(items):
                    return items[index]
                else:
                    self.console.print("[red]无效选择，请重新输入[/red]")
            except ValueError:
                self.console.print("[red]请输入有效数字[/red]")

class CLIOutput:
    """CLI输出工具"""
    
    def __init__(self, console: Console = None):
        self.console = console or Console()
    
    def success(self, message: str):
        """成功消息"""
        self.console.print(f"[green]✅ {message}[/green]")
    
    def error(self, message: str):
        """错误消息"""
        self.console.print(f"[red]❌ {message}[/red]")
    
    def warning(self, message: str):
        """警告消息"""
        self.console.print(f"[yellow]⚠️  {message}[/yellow]")
    
    def info(self, message: str):
        """信息消息"""
        self.console.print(f"[blue]ℹ️  {message}[/blue]")
    
    def debug(self, message: str):
        """调试消息"""
        self.console.print(f"[dim]🔍 {message}[/dim]")
    
    def print_code(self, code: str, language: str = "python"):
        """打印代码"""
        syntax = Syntax(code, language, theme="monokai", line_numbers=True)
        self.console.print(syntax)
    
    def print_json(self, data: Any):
        """打印JSON数据"""
        import json
        json_str = json.dumps(data, ensure_ascii=False, indent=2)
        syntax = Syntax(json_str, "json", theme="monokai")
        self.console.print(syntax)

class CLITimer:
    """CLI计时器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """开始计时"""
        self.start_time = datetime.now()
    
    def stop(self):
        """停止计时"""
        self.end_time = datetime.now()
    
    def elapsed(self) -> timedelta:
        """获取经过时间"""
        if self.start_time is None:
            return timedelta(0)
        
        end = self.end_time or datetime.now()
        return end - self.start_time
    
    def elapsed_str(self) -> str:
        """获取经过时间字符串"""
        elapsed = self.elapsed()
        total_seconds = elapsed.total_seconds()
        
        hours, remainder = divmod(total_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f"{int(hours)}h {int(minutes)}m {int(seconds)}s"
        elif minutes > 0:
            return f"{int(minutes)}m {int(seconds)}s"
        else:
            return f"{seconds:.1f}s"

class CLISignalHandler:
    """CLI信号处理器"""
    
    def __init__(self):
        self.interrupted = False
        self.cleanup_callbacks = []
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        self.interrupted = True
        self._run_cleanup()
        sys.exit(0)
    
    def add_cleanup_callback(self, callback: Callable):
        """添加清理回调"""
        self.cleanup_callbacks.append(callback)
    
    def _run_cleanup(self):
        """运行清理回调"""
        for callback in self.cleanup_callbacks:
            try:
                callback()
            except Exception:
                pass

# 工具函数
def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """截断字符串"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def safe_filename(filename: str) -> str:
    """生成安全的文件名"""
    import re
    # 移除或替换非法字符
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除多余的空格和点
    safe_name = re.sub(r'\s+', '_', safe_name.strip())
    safe_name = safe_name.strip('.')
    return safe_name or "untitled"

async def run_with_timeout(coro, timeout: float):
    """带超时的异步运行"""
    try:
        return await asyncio.wait_for(coro, timeout=timeout)
    except asyncio.TimeoutError:
        raise TimeoutError(f"操作超时 ({timeout}秒)")