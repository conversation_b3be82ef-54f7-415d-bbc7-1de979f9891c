"""
分布式状态管理器 - v0.3增强版
支持分布式状态同步、版本控制和事件系统
"""

import asyncio
import json
import logging
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from contextlib import asynccontextmanager
import uuid
import hashlib

from redis import asyncio as aioredis
from pydantic import BaseModel, Field

from ..states.project_state import ProjectState
from .exceptions import StateManagerError, StateLockError, StateVersionError

logger = logging.getLogger(__name__)


class StateVersion(BaseModel):
    """状态版本信息"""
    version_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    project_id: str
    version_number: int
    state_hash: str
    created_at: datetime = Field(default_factory=datetime.now)
    created_by: str = "system"
    description: str = ""
    metadata: Dict[str, Any] = Field(default_factory=dict)


class StateEvent(BaseModel):
    """状态事件"""
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str
    project_id: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)
    source: str = "system"


class DistributedStateLock:
    """分布式状态锁"""
    
    def __init__(self, redis_client: aioredis.Redis, lock_key: str, timeout: int = 30):
        self.redis = redis_client
        self.lock_key = f"lock:{lock_key}"
        self.timeout = timeout
        self.lock_value = str(uuid.uuid4())
        self._acquired = False
    
    async def acquire(self) -> bool:
        """获取锁"""
        try:
            # 使用SET命令的NX和EX选项实现分布式锁
            result = await self.redis.set(
                self.lock_key, 
                self.lock_value, 
                nx=True, 
                ex=self.timeout
            )
            self._acquired = bool(result)
            return self._acquired
        except Exception as e:
            logger.error(f"Failed to acquire lock {self.lock_key}: {e}")
            return False
    
    async def release(self) -> bool:
        """释放锁"""
        if not self._acquired:
            return True
        
        try:
            # 使用Lua脚本确保只有锁的持有者才能释放锁
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
            """
            result = await self.redis.eval(lua_script, 1, self.lock_key, self.lock_value)
            self._acquired = False
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to release lock {self.lock_key}: {e}")
            return False
    
    async def extend(self, additional_time: int = 30) -> bool:
        """延长锁的有效期"""
        if not self._acquired:
            return False
        
        try:
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("expire", KEYS[1], ARGV[2])
            else
                return 0
            end
            """
            result = await self.redis.eval(
                lua_script, 1, self.lock_key, self.lock_value, additional_time
            )
            return bool(result)
        except Exception as e:
            logger.error(f"Failed to extend lock {self.lock_key}: {e}")
            return False


class DistributedStateManager:
    """分布式状态管理器 - 支持多种Redis模式"""

    def __init__(self, redis_url: str = "redis://localhost:6379", use_redis_manager: bool = True):
        self.redis_url = redis_url
        self.use_redis_manager = use_redis_manager
        self.redis = None
        self.state_cache: Dict[str, ProjectState] = {}
        self.version_history: Dict[str, List[StateVersion]] = {}
        self.event_handlers: Dict[str, List[Callable]] = {}
        self.event_queue = asyncio.Queue()
        self._initialized = False
        self._event_processor_task: Optional[asyncio.Task] = None
    
    async def initialize(self):
        """初始化状态管理器 - 支持多种Redis模式"""
        if self._initialized:
            return

        try:
            if self.use_redis_manager:
                # 使用新的Redis管理器
                from ..config.redis_config import get_redis_client
                self.redis = await get_redis_client()
                logger.info("状态管理器使用Redis管理器连接成功")
            else:
                # 传统方式连接Redis
                try:
                    import aioredis
                    self.redis = aioredis.from_url(
                        self.redis_url,
                        encoding="utf-8",
                        decode_responses=False,
                        max_connections=20
                    )
                    await self.redis.ping()
                    logger.info("状态管理器传统方式Redis连接成功")
                except Exception as e:
                    logger.warning(f"Redis连接失败: {e}，使用本地状态管理")
                    self.redis = None

            # 启动事件处理器
            self._event_processor_task = asyncio.create_task(self._process_events())

            self._initialized = True
            logger.info("DistributedStateManager初始化完成")

        except Exception as e:
            logger.error(f"状态管理器初始化失败: {e}")
            # 降级到本地状态管理
            self.redis = None
            self._initialized = True
            logger.info("降级到本地状态管理模式")
    
    async def shutdown(self):
        """关闭状态管理器"""
        if not self._initialized:
            return
        
        try:
            # 停止事件处理器
            if self._event_processor_task:
                self._event_processor_task.cancel()
                try:
                    await self._event_processor_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭Redis连接
            if self.redis:
                await self.redis.close()
            
            self._initialized = False
            logger.info("DistributedStateManager shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    @asynccontextmanager
    async def acquire_lock(self, project_id: str, timeout: int = 30):
        """获取项目状态锁的上下文管理器"""
        lock = DistributedStateLock(self.redis, f"project_state:{project_id}", timeout)
        
        acquired = await lock.acquire()
        if not acquired:
            raise StateLockError(f"Failed to acquire lock for project {project_id}")
        
        try:
            yield lock
        finally:
            await lock.release()
    
    async def get_project_state(self, project_id: str) -> Optional[ProjectState]:
        """获取项目状态"""
        if not self._initialized:
            await self.initialize()
        
        try:
            # 先从缓存获取
            if project_id in self.state_cache:
                return self.state_cache[project_id]
            
            # 从Redis获取
            state_key = f"project_state:{project_id}"
            state_data = await self.redis.get(state_key)
            
            if state_data:
                # 反序列化状态
                state_dict = pickle.loads(state_data)
                state = ProjectState.from_dict(state_dict)
                
                # 更新缓存
                self.state_cache[project_id] = state
                return state
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get project state {project_id}: {e}")
            raise StateManagerError(f"Failed to get state: {e}")
    
    async def save_project_state(self, project_state: ProjectState, 
                                create_checkpoint: bool = True) -> bool:
        """保存项目状态"""
        if not self._initialized:
            await self.initialize()
        
        project_id = project_state.project_id
        
        try:
            async with self.acquire_lock(project_id):
                # 更新时间戳
                project_state.last_update = datetime.now()
                
                # 创建检查点
                if create_checkpoint:
                    await self._create_checkpoint(project_state)
                
                # 序列化状态
                state_data = pickle.dumps(project_state.to_dict())
                
                # 保存到Redis
                state_key = f"project_state:{project_id}"
                await self.redis.setex(state_key, 3600, state_data)  # 1小时过期
                
                # 更新缓存
                self.state_cache[project_id] = project_state
                
                # 发布状态变更事件
                await self._emit_event("state_updated", project_id, {
                    "project_id": project_id,
                    "update_time": project_state.last_update.isoformat()
                })
                
                logger.debug(f"Project state saved: {project_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to save project state {project_id}: {e}")
            raise StateManagerError(f"Failed to save state: {e}")
    
    async def _create_checkpoint(self, project_state: ProjectState) -> str:
        """创建状态检查点"""
        project_id = project_state.project_id
        
        # 计算状态哈希
        state_hash = hashlib.md5(
            json.dumps(project_state.to_dict(), sort_keys=True).encode()
        ).hexdigest()
        
        # 获取版本号
        version_number = await self._get_next_version_number(project_id)
        
        # 创建版本信息
        version = StateVersion(
            project_id=project_id,
            version_number=version_number,
            state_hash=state_hash,
            description=f"Auto checkpoint v{version_number}"
        )
        
        # 保存版本信息
        version_key = f"project_version:{project_id}:{version.version_id}"
        version_data = pickle.dumps(version.dict())
        await self.redis.setex(version_key, 86400 * 7, version_data)  # 7天过期
        
        # 保存状态快照
        snapshot_key = f"project_snapshot:{project_id}:{version.version_id}"
        snapshot_data = pickle.dumps(project_state.to_dict())
        await self.redis.setex(snapshot_key, 86400 * 7, snapshot_data)  # 7天过期
        
        # 更新版本历史
        if project_id not in self.version_history:
            self.version_history[project_id] = []
        self.version_history[project_id].append(version)
        
        # 限制版本历史数量（保留最近50个版本）
        if len(self.version_history[project_id]) > 50:
            old_version = self.version_history[project_id].pop(0)
            # 删除旧的快照
            old_snapshot_key = f"project_snapshot:{project_id}:{old_version.version_id}"
            await self.redis.delete(old_snapshot_key)
        
        logger.debug(f"Checkpoint created: {project_id} v{version_number}")
        return version.version_id
    
    async def _get_next_version_number(self, project_id: str) -> int:
        """获取下一个版本号"""
        if project_id in self.version_history:
            return len(self.version_history[project_id]) + 1
        
        # 从Redis加载版本历史
        pattern = f"project_version:{project_id}:*"
        keys = await self.redis.keys(pattern)
        return len(keys) + 1
    
    async def restore_from_checkpoint(self, project_id: str, 
                                    version_id: str) -> Optional[ProjectState]:
        """从检查点恢复状态"""
        if not self._initialized:
            await self.initialize()
        
        try:
            async with self.acquire_lock(project_id):
                # 获取快照
                snapshot_key = f"project_snapshot:{project_id}:{version_id}"
                snapshot_data = await self.redis.get(snapshot_key)
                
                if not snapshot_data:
                    raise StateVersionError(f"Checkpoint not found: {version_id}")
                
                # 反序列化状态
                state_dict = pickle.loads(snapshot_data)
                restored_state = ProjectState.from_dict(state_dict)
                
                # 保存恢复的状态
                await self.save_project_state(restored_state, create_checkpoint=True)
                
                # 发布恢复事件
                await self._emit_event("state_restored", project_id, {
                    "project_id": project_id,
                    "version_id": version_id,
                    "restore_time": datetime.now().isoformat()
                })
                
                logger.info(f"State restored from checkpoint: {project_id} -> {version_id}")
                return restored_state
                
        except Exception as e:
            logger.error(f"Failed to restore from checkpoint: {e}")
            raise StateManagerError(f"Restore failed: {e}")
    
    async def get_version_history(self, project_id: str) -> List[StateVersion]:
        """获取版本历史"""
        if project_id in self.version_history:
            return self.version_history[project_id].copy()
        
        # 从Redis加载版本历史
        pattern = f"project_version:{project_id}:*"
        keys = await self.redis.keys(pattern)
        
        versions = []
        for key in keys:
            version_data = await self.redis.get(key)
            if version_data:
                version_dict = pickle.loads(version_data)
                versions.append(StateVersion(**version_dict))
        
        # 按版本号排序
        versions.sort(key=lambda v: v.version_number)
        self.version_history[project_id] = versions
        
        return versions.copy()
    
    def register_event_handler(self, event_type: str, handler: Callable):
        """注册事件处理器"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        logger.debug(f"Event handler registered for: {event_type}")
    
    async def _emit_event(self, event_type: str, project_id: str, data: Dict[str, Any]):
        """发出事件"""
        event = StateEvent(
            event_type=event_type,
            project_id=project_id,
            data=data
        )
        
        # 添加到事件队列
        await self.event_queue.put(event)
        
        # 发布到Redis频道
        channel = f"state_events:{project_id}"
        event_data = json.dumps(event.dict(), default=str)
        await self.redis.publish(channel, event_data)
    
    async def _process_events(self):
        """处理事件队列"""
        while True:
            try:
                # 从队列获取事件
                event = await self.event_queue.get()
                
                # 调用注册的处理器
                handlers = self.event_handlers.get(event.event_type, [])
                for handler in handlers:
                    try:
                        if asyncio.iscoroutinefunction(handler):
                            await handler(event)
                        else:
                            handler(event)
                    except Exception as e:
                        logger.error(f"Event handler error: {e}")
                
                # 标记任务完成
                self.event_queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Event processing error: {e}")
    
    async def subscribe_to_events(self, project_id: str, handler: Callable):
        """订阅项目事件"""
        if not self._initialized:
            await self.initialize()
        
        channel = f"state_events:{project_id}"
        pubsub = self.redis.pubsub()
        await pubsub.subscribe(channel)
        
        async def event_listener():
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    try:
                        event_data = json.loads(message['data'])
                        event = StateEvent(**event_data)
                        
                        if asyncio.iscoroutinefunction(handler):
                            await handler(event)
                        else:
                            handler(event)
                    except Exception as e:
                        logger.error(f"Event subscription handler error: {e}")
        
        # 启动监听任务
        asyncio.create_task(event_listener())
        logger.info(f"Subscribed to events for project: {project_id}")
    
    async def get_state_statistics(self) -> Dict[str, Any]:
        """获取状态管理统计信息"""
        if not self._initialized:
            await self.initialize()
        
        try:
            # 统计项目数量
            project_keys = await self.redis.keys("project_state:*")
            project_count = len(project_keys)
            
            # 统计版本数量
            version_keys = await self.redis.keys("project_version:*")
            version_count = len(version_keys)
            
            # 统计快照数量
            snapshot_keys = await self.redis.keys("project_snapshot:*")
            snapshot_count = len(snapshot_keys)
            
            # 缓存统计
            cache_count = len(self.state_cache)
            
            return {
                "project_count": project_count,
                "version_count": version_count,
                "snapshot_count": snapshot_count,
                "cache_count": cache_count,
                "redis_connected": bool(self.redis),
                "initialized": self._initialized
            }
            
        except Exception as e:
            logger.error(f"Failed to get statistics: {e}")
            return {"error": str(e)}


# 全局状态管理器实例
state_manager = DistributedStateManager()


async def get_state_manager() -> DistributedStateManager:
    """获取状态管理器实例"""
    if not state_manager._initialized:
        await state_manager.initialize()
    return state_manager
