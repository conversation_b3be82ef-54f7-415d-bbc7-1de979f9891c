#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Kimi K2配置脚本
确认系统已正确配置为使用kimi-k2-0711-preview作为默认模型
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def safe_print(message):
    """安全打印函数"""
    try:
        print(message)
    except UnicodeEncodeError:
        safe_msg = (message
                   .replace('✅', '[OK]')
                   .replace('❌', '[ERROR]')
                   .replace('🔧', '[CONFIG]')
                   .replace('📋', '[INFO]')
                   .replace('🎯', '[TARGET]'))
        print(safe_msg)

def check_env_file():
    """检查.env文件配置"""
    safe_print("🔧 检查.env文件配置...")
    
    env_file = Path(".env")
    if not env_file.exists():
        safe_print("❌ .env文件不存在")
        return False
    
    content = env_file.read_text(encoding='utf-8')
    
    checks = [
        ('LLM_PROVIDER="moonshot"', 'LLM提供商设置为moonshot'),
        ('DEFAULT_MODEL="kimi-k2-0711-preview"', '默认模型设置为kimi-k2-0711-preview'),
        ('MOONSHOT_API_KEY=', 'Moonshot API密钥配置'),
    ]
    
    results = []
    for check, description in checks:
        if check in content:
            safe_print(f"   ✅ {description}")
            results.append(True)
        else:
            safe_print(f"   ❌ {description}")
            results.append(False)
    
    return all(results)

def check_llm_config():
    """检查LLM配置类"""
    safe_print("\n🔧 检查LLM配置类...")
    
    try:
        from langgraph_system.llm.config import LLMConfig
        
        # 测试默认配置
        config = LLMConfig()
        
        checks = [
            (config.provider == "moonshot", f"默认提供商: {config.provider}"),
            (config.model == "kimi-k2-0711-preview", f"默认模型: {config.model}"),
            (config.temperature == 0.7, f"温度参数: {config.temperature}"),
        ]
        
        results = []
        for check, description in checks:
            if check:
                safe_print(f"   ✅ {description}")
                results.append(True)
            else:
                safe_print(f"   ❌ {description}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        safe_print(f"   ❌ LLM配置检查失败: {e}")
        return False

def check_settings():
    """检查系统设置"""
    safe_print("\n🔧 检查系统设置...")
    
    try:
        from langgraph_system.config.settings import Settings
        
        settings = Settings()
        
        checks = [
            (settings.default_provider == "moonshot", f"默认提供商: {settings.default_provider}"),
            (settings.default_model == "kimi-k2-0711-preview", f"默认模型: {settings.default_model}"),
        ]
        
        results = []
        for check, description in checks:
            if check:
                safe_print(f"   ✅ {description}")
                results.append(True)
            else:
                safe_print(f"   ❌ {description}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        safe_print(f"   ❌ 系统设置检查失败: {e}")
        return False

def check_cli_defaults():
    """检查CLI默认参数"""
    safe_print("\n🔧 检查CLI默认参数...")
    
    cli_file = Path("src/langgraph_system/cli/cli_main.py")
    if not cli_file.exists():
        safe_print("   ❌ CLI文件不存在")
        return False
    
    content = cli_file.read_text(encoding='utf-8')
    
    checks = [
        ("default='moonshot'", "CLI默认提供商设置为moonshot"),
        ("kimi-k2-0711-preview", "CLI包含kimi-k2模型配置"),
    ]
    
    results = []
    for check, description in checks:
        if check in content:
            safe_print(f"   ✅ {description}")
            results.append(True)
        else:
            safe_print(f"   ❌ {description}")
            results.append(False)
    
    return all(results)

def test_actual_usage():
    """测试实际使用"""
    safe_print("\n🔧 测试实际使用...")
    
    try:
        from langgraph_system.llm.config import LLMConfig
        from langgraph_system.llm.factory import LLMFactory
        
        # 创建默认配置
        config = LLMConfig()
        safe_print(f"   📋 创建的配置: provider={config.provider}, model={config.model}")
        
        # 测试客户端创建
        if config.moonshot_api_key and config.moonshot_api_key != "YOUR_MOONSHOT_API_KEY_HERE":
            try:
                client = LLMFactory.create_client(config)
                safe_print(f"   ✅ 客户端创建成功: {type(client).__name__}")
                return True
            except Exception as e:
                safe_print(f"   ❌ 客户端创建失败: {e}")
                return False
        else:
            safe_print("   ⚠️  Moonshot API密钥未配置，跳过客户端测试")
            return True
            
    except Exception as e:
        safe_print(f"   ❌ 实际使用测试失败: {e}")
        return False

def main():
    """主函数"""
    safe_print("🎯 Kimi K2配置验证")
    safe_print("=" * 50)
    
    # 切换到项目根目录
    os.chdir(Path(__file__).parent.parent)
    
    tests = [
        ("环境文件配置", check_env_file),
        ("LLM配置类", check_llm_config),
        ("系统设置", check_settings),
        ("CLI默认参数", check_cli_defaults),
        ("实际使用测试", test_actual_usage),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            safe_print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 生成总结
    safe_print("\n" + "=" * 50)
    safe_print("📋 配置验证总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        safe_print(f"   {status} {test_name}")
    
    success_rate = passed / total * 100
    safe_print(f"\n🎯 验证结果: {passed}/{total} 通过 ({success_rate:.1f}%)")
    
    if success_rate == 100:
        safe_print("🎉 配置验证成功！系统已正确配置为使用Kimi K2")
        safe_print("\n📋 配置摘要:")
        safe_print("   • 默认提供商: moonshot")
        safe_print("   • 默认模型: kimi-k2-0711-preview")
        safe_print("   • 温度参数: 0.7")
        safe_print("   • API基础URL: https://api.moonshot.cn/v1")
    elif success_rate >= 80:
        safe_print("👍 配置基本正确，部分项目需要检查")
    else:
        safe_print("⚠️ 配置存在问题，需要修复")
    
    safe_print("\n💡 使用方法:")
    safe_print("   # 现在可以直接使用，无需指定提供商")
    safe_print("   python scripts/run_cli.py status")
    safe_print("   python scripts/run_cli.py agents list")
    safe_print("   ")
    safe_print("   # 或者显式指定（可选）")
    safe_print("   python scripts/run_cli.py --llm-provider moonshot status")
    
    return 0 if success_rate >= 80 else 1

if __name__ == "__main__":
    sys.exit(main())
