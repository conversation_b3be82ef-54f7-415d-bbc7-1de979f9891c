"""
工具执行器，负责调用已注册的工具。
"""
from typing import Dict, Any, List, Callable
import logging

from ..core.registry import tool_registry
from ..core.exceptions import ToolError

logger = logging.getLogger(__name__)

class ToolExecutor:
    """增强的工具执行器，使用注册中心管理工具"""
    
    def __init__(self):
        # 确保增强工具已加载
        try:
            from . import enhanced_tools
            logger.info("增强工具模块已加载")
        except ImportError:
            logger.warning("无法加载增强工具模块")
        
        logger.info(f"工具执行器已初始化，可用工具: {tool_registry.list_tools()}")

    def execute(self, tool_call: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行单个工具调用。

        Args:
            tool_call: 一个包含 'tool_name' 和 'tool_args' 的字典。

        Returns:
            一个包含工具执行结果的字典。
        """
        tool_name = tool_call.get("tool_name")
        tool_args = tool_call.get("tool_args", {})

        if not tool_name:
            error_message = "工具调用缺少 'tool_name' 参数"
            logger.error(error_message)
            return {"status": "error", "result": error_message}

        try:
            logger.info(f"正在执行工具 '{tool_name}'，参数: {tool_args}")
            
            # 使用注册中心执行工具
            result = tool_registry.execute_tool(tool_name, **tool_args)
            
            logger.info(f"工具 '{tool_name}' 执行成功")
            return {"status": "success", "result": result}
            
        except ToolError as e:
            logger.error(f"工具执行失败: {e}")
            return {"status": "error", "result": str(e), "error_code": e.error_code}
            
        except Exception as e:
            error_message = f"执行工具 '{tool_name}' 时发生未知错误: {e}"
            logger.error(error_message, exc_info=True)
            return {"status": "error", "result": error_message}
    
    def list_available_tools(self) -> List[str]:
        """列出所有可用工具"""
        return tool_registry.list_tools()
    
    def get_tool_metadata(self, tool_name: str) -> Dict[str, Any]:
        """获取工具元数据"""
        try:
            return tool_registry.get_metadata(tool_name)
        except ToolError:
            return {}
    
    def execute_batch(self, tool_calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量执行工具调用"""
        results = []
        for tool_call in tool_calls:
            result = self.execute(tool_call)
            results.append(result)
        return results
