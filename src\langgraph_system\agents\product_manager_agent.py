#!/usr/bin/env python3
"""
LangGraph多智能体系统 v0.3 - 产品经理智能体 (重构)
负责需求分析、产品规划、用户体验设计和项目管理
"""

import asyncio
import json
import uuid
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from enum import Enum
import logging

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.tools import tool

from .base_v3 import SpecialistAgent, AgentCapability

logger = logging.getLogger(__name__)

# ============================================================================
# 产品经理智能体专用数据结构
# ============================================================================

class RequirementType(str, Enum):
    FUNCTIONAL = "functional"
    NON_FUNCTIONAL = "non_functional"
    BUSINESS = "business"

class Priority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class UserStory:
    id: str
    title: str
    description: str
    acceptance_criteria: List[str] = field(default_factory=list)
    priority: Priority = Priority.MEDIUM

@dataclass
class Feature:
    id: str
    name: str
    description: str
    user_stories: List[UserStory] = field(default_factory=list)

@dataclass
class ProductRequirement:
    id: str
    title: str
    description: str
    req_type: RequirementType = RequirementType.FUNCTIONAL
    priority: Priority = Priority.MEDIUM

# ============================================================================
# 产品经理智能体实现 (v3)
# ============================================================================

class ProductManagerAgent(SpecialistAgent):
    """
    产品经理智能体 v3
    
    核心职责：
    - 需求分析、用户故事编写、产品功能规划
    """
    
    def __init__(self, model: BaseLanguageModel, custom_tools: List = None, **kwargs):
        self.requirements: Dict[str, ProductRequirement] = {}
        self.features: Dict[str, Feature] = {}
        self.user_stories: Dict[str, UserStory] = {}
        
        agent_tools = [
            self.analyze_requirements,
            self.create_user_stories,
            self.plan_features,
            self.prioritize_backlog,
        ]
        if custom_tools:
            agent_tools.extend(custom_tools)

        super().__init__(
            agent_id="product_manager_001",
            name="产品经理",
            capabilities=[AgentCapability.REQUIREMENT_ANALYSIS],
            model=model,
            tools=agent_tools,
            **kwargs,
        )

    # ========================================================================
    # 智能体工具定义
    # ========================================================================

    @tool
    async def analyze_requirements(self, raw_requirements_data: str) -> str:
        """
        分析原始需求，将其结构化为产品需求。
        Args:
            raw_requirements_data (str): JSON格式的原始需求列表，每个需求包含'title'和'description'.
        Returns:
            str: JSON格式的分析结果, 包括创建的需求ID列表.
        """
        try:
            raw_requirements = json.loads(raw_requirements_data)
            logger.info("开始需求分析")
            
            analyzed_reqs = []
            for req_data in raw_requirements:
                req_id = f"req_{uuid.uuid4().hex[:8]}"
                requirement = ProductRequirement(
                    id=req_id,
                    title=req_data['title'],
                    description=req_data['description'],
                    priority=await self._assess_priority(req_data['description'])
                )
                self.requirements[req_id] = requirement
                analyzed_reqs.append({"id": req_id, "title": requirement.title})

            return json.dumps({
                "analyzed_count": len(analyzed_reqs),
                "requirements": analyzed_reqs,
            }, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"需求分析失败: {e}", exc_info=True)
            return json.dumps({"error": f"需求分析失败: {str(e)}"})

    @tool
    async def create_user_stories(self, requirement_id: str) -> str:
        """
        为一个特定的产品需求创建用户故事。
        Args:
            requirement_id (str): 产品需求的ID.
        Returns:
            str: JSON格式的用户故事列表.
        """
        try:
            if requirement_id not in self.requirements:
                raise ValueError(f"需求 '{requirement_id}' 不存在")
            
            requirement = self.requirements[requirement_id]
            logger.info(f"为需求 '{requirement.title}' 创建用户故事")
            
            stories_data = await self._invoke_llm_for_story_generation(requirement)
            
            created_stories = []
            for story_data in stories_data:
                story_id = f"story_{uuid.uuid4().hex[:8]}"
                story = UserStory(
                    id=story_id,
                    title=story_data['title'],
                    description=story_data['description'],
                    acceptance_criteria=story_data['acceptance_criteria'],
                    priority=requirement.priority,
                )
                self.user_stories[story_id] = story
                created_stories.append({"id": story.id, "title": story.title})
            
            return json.dumps(created_stories, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"用户故事创建失败: {e}", exc_info=True)
            return json.dumps({"error": f"用户故事创建失败: {str(e)}"})
            
    @tool
    async def plan_features(self, story_ids: List[str]) -> str:
        """
        将一组用户故事组合成一个产品功能。
        Args:
            story_ids (List[str]): 要组合成功能的用户故事ID列表.
        Returns:
            str: JSON格式的功能创建结果.
        """
        try:
            stories = [self.user_stories[sid] for sid in story_ids if sid in self.user_stories]
            if not stories:
                raise ValueError("没有有效的用户故事ID")
            
            feature_data = await self._invoke_llm_for_feature_planning(stories)
            
            feature_id = f"feat_{uuid.uuid4().hex[:8]}"
            feature = Feature(
                id=feature_id,
                name=feature_data['name'],
                description=feature_data['description'],
                user_stories=stories,
            )
            self.features[feature_id] = feature
            
            return json.dumps({
                "feature_id": feature.id,
                "name": feature.name,
                "user_story_count": len(stories),
            }, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"功能规划失败: {e}", exc_info=True)
            return json.dumps({"error": f"功能规划失败: {str(e)}"})
    
    @tool
    async def prioritize_backlog(self, item_ids: List[str], method: str = "value_vs_effort") -> str:
        """
        对待办事项列表（需求、功能或用户故事）进行优先级排序。
        Args:
            item_ids (List[str]): 待办事项的ID列表.
            method (str, optional): 排序方法 (e.g., 'value_vs_effort', 'moscow'). Defaults to "value_vs_effort".
        Returns:
            str: JSON格式的排序后的ID列表.
        """
        try:
            items = []
            for item_id in item_ids:
                if item_id in self.requirements:
                    items.append(self.requirements[item_id])
                elif item_id in self.features:
                    items.append(self.features[item_id])
                elif item_id in self.user_stories:
                    items.append(self.user_stories[item_id])
            
            if not items:
                raise ValueError("没有有效的待办事项ID")
                
            sorted_item_ids = await self._sort_items_by_priority(items, method)
            
            return json.dumps(sorted_item_ids, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"优先级排序失败: {e}", exc_info=True)
            return json.dumps({"error": f"优先级排序失败: {str(e)}"})

    # ========================================================================
    # 内部辅助方法 & LLM 调用
    # ========================================================================

    async def _assess_priority(self, text: str) -> Priority:
        """使用LLM评估需求的优先级"""
        prompt = f"Based on the requirement description, assess its priority as 'low', 'medium', 'high', or 'critical'.\nDescription: {text}\nPriority:"
        response = await self.model.ainvoke(prompt)
        priority_str = response.content.strip().lower()
        return Priority(priority_str) if priority_str in Priority.__members__.values() else Priority.MEDIUM

    async def _invoke_llm_for_story_generation(self, requirement: ProductRequirement) -> List[Dict[str, Any]]:
        """使用LLM为需求生成用户故事"""
        prompt = f"""As a product manager, break down the following requirement into user stories.
Requirement: {requirement.title} - {requirement.description}
Provide a JSON list of stories, each with 'title', 'description', and 'acceptance_criteria' (a list of strings).
Example: [{"title": "...", "description": "As a user, I want to...", "acceptance_criteria": ["Given..., When..., Then..."]}]"""
        response = await self.model.ainvoke(prompt)
        return json.loads(response.content)
        
    async def _invoke_llm_for_feature_planning(self, stories: List[UserStory]) -> Dict[str, str]:
        """使用LLM规划功能"""
        stories_summary = "\n".join([f"- {s.title}" for s in stories])
        prompt = f"""As a product manager, group the following user stories into a single feature.
User Stories:
{stories_summary}
Provide a JSON object with a 'name' and 'description' for the feature."""
        response = await self.model.ainvoke(prompt)
        return json.loads(response.content)

    async def _sort_items_by_priority(self, items: List[Any], method: str) -> List[str]:
        """根据不同方法对待办事项进行排序"""
        item_scores = {}
        for item in items:
            value = {"critical": 4, "high": 3, "medium": 2, "low": 1}.get(getattr(item, 'priority', 'medium'), 2)
            # 简化版：effort都设为中等
            effort = 2 
            item_scores[item.id] = value / effort

        sorted_ids = sorted(item_scores, key=item_scores.get, reverse=True)
        return sorted_ids

__all__ = ['ProductManagerAgent', 'UserStory', 'Feature', 'ProductRequirement']