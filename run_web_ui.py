#!/usr/bin/env python3
"""
启动LangGraph多智能体系统Web界面
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """启动Streamlit应用"""
    # 获取项目根目录
    project_root = Path(__file__).parent
    
    # Streamlit应用路径
    app_path = project_root / "src" / "langgraph_system" / "web" / "streamlit_app.py"
    
    if not app_path.exists():
        print(f"❌ 找不到Streamlit应用文件: {app_path}")
        sys.exit(1)
    
    print("🚀 启动LangGraph多智能体系统Web界面...")
    print(f"📁 应用路径: {app_path}")
    print("🌐 Web界面将在浏览器中打开...")
    print("⏹️  按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        # 启动Streamlit应用
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            str(app_path),
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ]
        
        subprocess.run(cmd, cwd=str(project_root))
        
    except KeyboardInterrupt:
        print("\n⏹️  Web界面已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()