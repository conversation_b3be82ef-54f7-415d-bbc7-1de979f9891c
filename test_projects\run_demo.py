#!/usr/bin/env python3
"""
运行智能待办事项管理器演示
测试生成的代码是否可以实际运行
"""

import asyncio
import subprocess
import sys
import time
import requests
from pathlib import Path
import threading
import signal
import os


class TodoManagerDemo:
    """待办事项管理器演示"""
    
    def __init__(self):
        self.project_dir = Path(__file__).parent.parent / "workspace" / "SmartTodoManager"
        self.backend_process = None
        self.demo_running = False
        
    def check_dependencies(self):
        """检查依赖是否安装"""
        print("🔍 检查依赖...")
        
        required_packages = ["fastapi", "uvicorn", "sqlalchemy", "pydantic"]
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"   ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"   ❌ {package}")
        
        if missing_packages:
            print(f"\n📦 安装缺失的依赖...")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install"
                ] + missing_packages, check=True)
                print("✅ 依赖安装完成")
                return True
            except subprocess.CalledProcessError:
                print("❌ 依赖安装失败")
                return False
        else:
            print("✅ 所有依赖已安装")
            return True
    
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        backend_file = self.project_dir / "backend" / "main.py"
        if not backend_file.exists():
            print(f"❌ 后端文件不存在: {backend_file}")
            return False
        
        try:
            # 启动FastAPI服务
            self.backend_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", 
                "backend.main:app", 
                "--host", "0.0.0.0", 
                "--port", "8000",
                "--reload"
            ], 
            cwd=str(self.project_dir),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
            )
            
            # 等待服务启动
            print("⏳ 等待服务启动...")
            for i in range(10):
                try:
                    response = requests.get("http://localhost:8000/", timeout=2)
                    if response.status_code == 200:
                        print("✅ 后端服务启动成功")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
                    print(f"   等待中... ({i+1}/10)")
            
            print("❌ 后端服务启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 启动后端服务失败: {e}")
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        print("\n🧪 测试API端点...")
        
        base_url = "http://localhost:8000"
        
        # 测试根端点
        try:
            response = requests.get(f"{base_url}/")
            if response.status_code == 200:
                print("   ✅ GET / - 根端点正常")
            else:
                print(f"   ❌ GET / - 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ GET / - 错误: {e}")
        
        # 测试获取待办事项
        try:
            response = requests.get(f"{base_url}/todos")
            if response.status_code == 200:
                todos = response.json()
                print(f"   ✅ GET /todos - 返回 {len(todos)} 个待办事项")
            else:
                print(f"   ❌ GET /todos - 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ GET /todos - 错误: {e}")
        
        # 测试创建待办事项
        try:
            todo_data = {
                "title": "测试任务",
                "description": "这是一个API测试任务",
                "priority": 2
            }
            response = requests.post(f"{base_url}/todos", json=todo_data)
            if response.status_code == 200:
                todo = response.json()
                print(f"   ✅ POST /todos - 创建任务: {todo['title']}")
                return todo['id']
            else:
                print(f"   ❌ POST /todos - 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ POST /todos - 错误: {e}")
        
        return None
    
    def test_frontend(self):
        """测试前端文件"""
        print("\n🌐 测试前端...")
        
        frontend_file = self.project_dir / "frontend" / "index.html"
        if frontend_file.exists():
            file_size = frontend_file.stat().st_size
            print(f"   ✅ 前端文件存在: {frontend_file.name} ({file_size} bytes)")
            
            # 检查HTML内容
            content = frontend_file.read_text(encoding='utf-8')
            if "智能待办事项管理器" in content:
                print("   ✅ 前端内容包含标题")
            if "addTodo" in content:
                print("   ✅ 前端包含JavaScript功能")
            if "todo-form" in content:
                print("   ✅ 前端包含表单样式")
                
            print(f"   🌐 前端访问地址: http://localhost:8000/frontend/index.html")
        else:
            print("   ❌ 前端文件不存在")
    
    def test_project_structure(self):
        """测试项目结构"""
        print("\n📁 测试项目结构...")
        
        expected_files = [
            "README.md",
            "requirements.txt", 
            "docker-compose.yml",
            "backend/main.py",
            "frontend/index.html",
            "tests/test_main.py",
            "docs/README.md"
        ]
        
        existing_files = []
        for file_path in expected_files:
            full_path = self.project_dir / file_path
            if full_path.exists():
                existing_files.append(file_path)
                print(f"   ✅ {file_path}")
            else:
                print(f"   ❌ {file_path}")
        
        print(f"\n   📊 文件完整性: {len(existing_files)}/{len(expected_files)} ({len(existing_files)/len(expected_files)*100:.1f}%)")
    
    def run_tests(self):
        """运行项目测试"""
        print("\n🧪 运行项目测试...")
        
        test_file = self.project_dir / "tests" / "test_main.py"
        if not test_file.exists():
            print("   ❌ 测试文件不存在")
            return
        
        try:
            # 尝试运行pytest
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                str(test_file), 
                "-v"
            ], 
            capture_output=True, 
            text=True,
            cwd=str(self.project_dir)
            )
            
            if result.returncode == 0:
                print("   ✅ 测试通过")
                print(f"   📝 测试输出:\n{result.stdout}")
            else:
                print("   ❌ 测试失败")
                print(f"   📝 错误信息:\n{result.stderr}")
                
        except Exception as e:
            print(f"   ❌ 运行测试失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 清理资源...")
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("   ✅ 后端服务已停止")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                print("   ⚠️  强制停止后端服务")
            except Exception as e:
                print(f"   ❌ 停止后端服务失败: {e}")
    
    def run_demo(self):
        """运行完整演示"""
        print("🎯 智能待办事项管理器演示")
        print("=" * 50)
        
        try:
            # 1. 检查项目结构
            self.test_project_structure()
            
            # 2. 检查依赖
            if not self.check_dependencies():
                return False
            
            # 3. 启动后端服务
            if not self.start_backend():
                return False
            
            # 4. 测试API
            todo_id = self.test_api_endpoints()
            
            # 5. 测试前端
            self.test_frontend()
            
            # 6. 运行测试
            self.run_tests()
            
            # 7. 显示访问信息
            print("\n🎉 演示完成!")
            print("\n📖 访问信息:")
            print("   🔗 API文档: http://localhost:8000/docs")
            print("   🔗 前端界面: http://localhost:8000/frontend/index.html")
            print("   🔗 API根路径: http://localhost:8000/")
            
            print("\n💡 提示:")
            print("   • 后端服务正在运行，可以通过浏览器访问")
            print("   • 按 Ctrl+C 停止演示")
            
            # 保持服务运行
            try:
                print("\n⏳ 服务运行中... (按 Ctrl+C 停止)")
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 用户中断演示")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 演示运行失败: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """主函数"""
    demo = TodoManagerDemo()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print("\n🛑 收到停止信号")
        demo.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    success = demo.run_demo()
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
