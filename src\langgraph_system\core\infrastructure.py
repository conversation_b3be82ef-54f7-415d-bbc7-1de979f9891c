"""
基础设施集成模块 - v0.3
整合所有基础设施组件，提供统一的初始化和管理接口
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from .state_manager import DistributedStateManager, get_state_manager
from .cache_manager import IntelligentCacheManager, get_cache_manager
from .agent_pool import AgentPoolManager, get_pool_manager
from .task_scheduler import AsyncTaskScheduler, get_task_scheduler
from .performance_monitor import PerformanceMonitor, get_performance_monitor
from .exceptions import InfrastructureError

logger = logging.getLogger(__name__)


class InfrastructureManager:
    """基础设施管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.components: Dict[str, Any] = {}
        self._initialized = False
        self._startup_time: Optional[datetime] = None
        
        # 组件初始化状态
        self.component_status = {
            "state_manager": False,
            "cache_manager": False,
            "agent_pool": False,
            "task_scheduler": False,
            "performance_monitor": False
        }
    
    async def initialize(self):
        """初始化所有基础设施组件"""
        if self._initialized:
            logger.warning("Infrastructure already initialized")
            return
        
        self._startup_time = datetime.now()
        logger.info("Starting infrastructure initialization...")
        
        try:
            # 1. 初始化状态管理器
            await self._initialize_state_manager()
            
            # 2. 初始化缓存管理器
            await self._initialize_cache_manager()
            
            # 3. 初始化性能监控器
            await self._initialize_performance_monitor()
            
            # 4. 初始化任务调度器
            await self._initialize_task_scheduler()
            
            # 5. 初始化智能体池管理器
            await self._initialize_agent_pool()
            
            # 6. 设置组件间集成
            await self._setup_integrations()
            
            self._initialized = True
            
            startup_duration = (datetime.now() - self._startup_time).total_seconds()
            logger.info(f"Infrastructure initialization completed in {startup_duration:.2f}s")
            
            # 记录启动指标
            perf_monitor = await get_performance_monitor()
            perf_monitor.set_gauge("infrastructure_startup_time", startup_duration)
            perf_monitor.increment_counter("infrastructure_startups")
            
        except Exception as e:
            logger.error(f"Infrastructure initialization failed: {e}")
            await self.shutdown()
            raise InfrastructureError(f"Initialization failed: {e}")
    
    async def _initialize_state_manager(self):
        """初始化状态管理器"""
        try:
            logger.info("Initializing state manager...")
            
            redis_url = self.config.get("redis_url", "redis://localhost:6379")
            state_manager = DistributedStateManager(redis_url)
            await state_manager.initialize()
            
            self.components["state_manager"] = state_manager
            self.component_status["state_manager"] = True
            
            logger.info("State manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize state manager: {e}")
            raise
    
    async def _initialize_cache_manager(self):
        """初始化缓存管理器"""
        try:
            logger.info("Initializing cache manager...")
            
            redis_url = self.config.get("redis_url", "redis://localhost:6379")
            memory_cache_size = self.config.get("memory_cache_size", 1000)
            default_ttl = self.config.get("cache_default_ttl", 3600)
            
            cache_manager = IntelligentCacheManager(
                redis_url=redis_url,
                memory_cache_size=memory_cache_size,
                default_ttl=default_ttl
            )
            await cache_manager.initialize()
            
            self.components["cache_manager"] = cache_manager
            self.component_status["cache_manager"] = True
            
            logger.info("Cache manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cache manager: {e}")
            raise
    
    async def _initialize_performance_monitor(self):
        """初始化性能监控器"""
        try:
            logger.info("Initializing performance monitor...")
            
            perf_monitor = PerformanceMonitor()
            await perf_monitor.start_monitoring()
            
            self.components["performance_monitor"] = perf_monitor
            self.component_status["performance_monitor"] = True
            
            logger.info("Performance monitor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize performance monitor: {e}")
            raise
    
    async def _initialize_task_scheduler(self):
        """初始化任务调度器"""
        try:
            logger.info("Initializing task scheduler...")
            
            max_workers = self.config.get("scheduler_max_workers", 10)
            max_queue_size = self.config.get("scheduler_max_queue_size", 1000)
            
            scheduler = AsyncTaskScheduler(
                max_workers=max_workers,
                max_queue_size=max_queue_size
            )
            await scheduler.initialize()
            
            self.components["task_scheduler"] = scheduler
            self.component_status["task_scheduler"] = True
            
            logger.info("Task scheduler initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize task scheduler: {e}")
            raise
    
    async def _initialize_agent_pool(self):
        """初始化智能体池管理器"""
        try:
            logger.info("Initializing agent pool manager...")
            
            pool_manager = AgentPoolManager()
            await pool_manager.initialize()
            
            self.components["agent_pool"] = pool_manager
            self.component_status["agent_pool"] = True
            
            logger.info("Agent pool manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize agent pool manager: {e}")
            raise
    
    async def _setup_integrations(self):
        """设置组件间集成"""
        try:
            logger.info("Setting up component integrations...")
            
            # 设置性能监控集成
            await self._setup_performance_monitoring_integration()
            
            # 设置缓存集成
            await self._setup_cache_integration()
            
            # 设置状态管理集成
            await self._setup_state_management_integration()
            
            logger.info("Component integrations setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup integrations: {e}")
            raise
    
    async def _setup_performance_monitoring_integration(self):
        """设置性能监控集成"""
        perf_monitor = self.components["performance_monitor"]
        
        # 添加告警处理器
        async def alert_handler(alert):
            logger.warning(f"Performance Alert: {alert.message}")
            # 这里可以添加更多告警处理逻辑，如发送通知等
        
        perf_monitor.add_alert_handler(alert_handler)
        
        # 注册自定义指标
        perf_monitor.add_alert_rule(
            "high_cache_miss_rate",
            "cache_misses",
            "gt",
            1000,
            duration=60.0,
            message_template="缓存未命中率过高: {value} > {threshold}"
        )
    
    async def _setup_cache_integration(self):
        """设置缓存集成"""
        cache_manager = self.components["cache_manager"]
        
        # 注册缓存预热函数
        async def preload_common_data():
            """预热常用数据"""
            # 这里可以添加预热逻辑
            logger.debug("Preloading common cache data...")
        
        cache_manager.register_preload_function("common_data", preload_common_data)
        
        # 设置缓存失效规则
        cache_manager.add_invalidation_rule(
            "project_state_updated",
            ["project_summary:*", "project_stats:*"]
        )
    
    async def _setup_state_management_integration(self):
        """设置状态管理集成"""
        state_manager = self.components["state_manager"]
        perf_monitor = self.components["performance_monitor"]
        
        # 注册状态事件处理器
        async def state_event_handler(event):
            """状态事件处理器"""
            if event.event_type == "state_updated":
                perf_monitor.increment_counter("state_updates")
            elif event.event_type == "state_restored":
                perf_monitor.increment_counter("state_restores")
        
        state_manager.register_event_handler("state_updated", state_event_handler)
        state_manager.register_event_handler("state_restored", state_event_handler)
    
    async def shutdown(self):
        """关闭所有基础设施组件"""
        if not self._initialized:
            return
        
        logger.info("Shutting down infrastructure...")
        
        shutdown_tasks = []
        
        # 按相反顺序关闭组件
        if "agent_pool" in self.components:
            shutdown_tasks.append(self.components["agent_pool"].shutdown())
        
        if "task_scheduler" in self.components:
            shutdown_tasks.append(self.components["task_scheduler"].shutdown())
        
        if "performance_monitor" in self.components:
            shutdown_tasks.append(self.components["performance_monitor"].stop_monitoring())
        
        if "cache_manager" in self.components:
            # 缓存管理器没有显式的shutdown方法，但可以清理资源
            pass
        
        if "state_manager" in self.components:
            shutdown_tasks.append(self.components["state_manager"].shutdown())
        
        # 并行执行关闭任务
        if shutdown_tasks:
            await asyncio.gather(*shutdown_tasks, return_exceptions=True)
        
        # 清理状态
        self.components.clear()
        self.component_status = {key: False for key in self.component_status}
        self._initialized = False
        
        logger.info("Infrastructure shutdown completed")
    
    def get_component(self, component_name: str) -> Optional[Any]:
        """获取组件实例"""
        return self.components.get(component_name)
    
    def is_component_ready(self, component_name: str) -> bool:
        """检查组件是否就绪"""
        return self.component_status.get(component_name, False)
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        return {
            "initialized": self._initialized,
            "startup_time": self._startup_time.isoformat() if self._startup_time else None,
            "components": self.component_status.copy(),
            "uptime_seconds": (
                (datetime.now() - self._startup_time).total_seconds()
                if self._startup_time else 0
            )
        }
    
    async def get_detailed_status(self) -> Dict[str, Any]:
        """获取详细状态"""
        status = self.get_health_status()
        
        # 添加组件详细状态
        if self.is_component_ready("state_manager"):
            state_manager = self.components["state_manager"]
            status["state_manager_stats"] = await state_manager.get_state_statistics()
        
        if self.is_component_ready("cache_manager"):
            cache_manager = self.components["cache_manager"]
            status["cache_manager_stats"] = cache_manager.get_statistics()
        
        if self.is_component_ready("agent_pool"):
            pool_manager = self.components["agent_pool"]
            status["agent_pool_stats"] = pool_manager.get_all_stats()
        
        if self.is_component_ready("task_scheduler"):
            scheduler = self.components["task_scheduler"]
            status["task_scheduler_stats"] = scheduler.get_scheduler_stats()
        
        if self.is_component_ready("performance_monitor"):
            perf_monitor = self.components["performance_monitor"]
            status["performance_stats"] = perf_monitor.get_metrics_summary()
            status["alerts_stats"] = perf_monitor.get_alerts_summary()
        
        return status
    
    async def run_health_check(self) -> Dict[str, Any]:
        """运行健康检查"""
        health_status = {
            "overall_status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "checks": {}
        }
        
        # 检查各个组件
        for component_name, is_ready in self.component_status.items():
            if not is_ready:
                health_status["checks"][component_name] = {
                    "status": "unhealthy",
                    "message": f"{component_name} is not ready"
                }
                health_status["overall_status"] = "unhealthy"
            else:
                health_status["checks"][component_name] = {
                    "status": "healthy",
                    "message": f"{component_name} is ready"
                }
        
        # 运行特定组件的健康检查
        if self.is_component_ready("state_manager"):
            try:
                state_manager = self.components["state_manager"]
                # 简单的Redis连接测试
                await state_manager.redis.ping()
                health_status["checks"]["redis_connection"] = {
                    "status": "healthy",
                    "message": "Redis connection is working"
                }
            except Exception as e:
                health_status["checks"]["redis_connection"] = {
                    "status": "unhealthy",
                    "message": f"Redis connection failed: {e}"
                }
                health_status["overall_status"] = "unhealthy"
        
        return health_status


# 全局基础设施管理器实例
infrastructure_manager = InfrastructureManager()


async def initialize_infrastructure(config: Optional[Dict[str, Any]] = None) -> InfrastructureManager:
    """初始化基础设施"""
    global infrastructure_manager
    
    if config:
        infrastructure_manager.config.update(config)
    
    await infrastructure_manager.initialize()
    return infrastructure_manager


async def shutdown_infrastructure():
    """关闭基础设施"""
    await infrastructure_manager.shutdown()


def get_infrastructure_manager() -> InfrastructureManager:
    """获取基础设施管理器"""
    return infrastructure_manager


# 便捷函数，用于获取各个组件
async def get_state_manager_instance() -> DistributedStateManager:
    """获取状态管理器实例"""
    if infrastructure_manager.is_component_ready("state_manager"):
        return infrastructure_manager.get_component("state_manager")
    return await get_state_manager()


async def get_cache_manager_instance() -> IntelligentCacheManager:
    """获取缓存管理器实例"""
    if infrastructure_manager.is_component_ready("cache_manager"):
        return infrastructure_manager.get_component("cache_manager")
    return await get_cache_manager()


async def get_pool_manager_instance() -> AgentPoolManager:
    """获取池管理器实例"""
    if infrastructure_manager.is_component_ready("agent_pool"):
        return infrastructure_manager.get_component("agent_pool")
    return await get_pool_manager()


async def get_task_scheduler_instance() -> AsyncTaskScheduler:
    """获取任务调度器实例"""
    if infrastructure_manager.is_component_ready("task_scheduler"):
        return infrastructure_manager.get_component("task_scheduler")
    return await get_task_scheduler()


async def get_performance_monitor_instance() -> PerformanceMonitor:
    """获取性能监控器实例"""
    if infrastructure_manager.is_component_ready("performance_monitor"):
        return infrastructure_manager.get_component("performance_monitor")
    return await get_performance_monitor()