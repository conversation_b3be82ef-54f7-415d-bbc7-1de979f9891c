#!/usr/bin/env python3
"""测试新的基于LangGraph Supervisor的智能体系统"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.langgraph_system.agents.supervisor_agent import SupervisorAgent
from src.langgraph_system.states.project_state import ProjectState, TaskType

async def test_supervisor():
    """测试新的Supervisor"""
    print("🚀 测试新的LangGraph Supervisor系统...")
    
    # 创建Supervisor实例
    supervisor = SupervisorAgent()
    
    # 测试系统状态
    status = supervisor.get_system_status()
    print(f"✅ 系统状态: {status}")
    
    # 测试任务处理
    test_tasks = [
        {
            "type": "development",
            "description": "创建一个简单的Python计算器",
            "requirements": {
                "功能": "支持加减乘除",
                "输入": "两个数字和一个运算符",
                "输出": "计算结果"
            }
        }
    ]
    
    for task in test_tasks:
        print(f"\n📝 处理任务: {task['description']}")
        result = await supervisor.process_task(task)
        
        if result["status"] == "completed":
            print(f"✅ 任务完成")
            print(f"📊 结果: {result['result'][:200]}...")
        else:
            print(f"❌ 任务失败: {result['error']}")
    
    # 测试项目处理
    print("\n🏗️ 测试项目处理...")
    project_state = ProjectState(
        current_task=TaskType.DEVELOPMENT,
        requirements={"目标": "构建一个Web应用"}
    )
    
    project_result = await supervisor.process_project(project_state)
    print(f"📋 项目处理结果: {project_result['status']}")
    
    # 测试任务计划
    plan = supervisor.create_task_plan(TaskType.DEVELOPMENT, {})
    print(f"\n📅 任务计划: {plan}")

if __name__ == "__main__":
    asyncio.run(test_supervisor())
