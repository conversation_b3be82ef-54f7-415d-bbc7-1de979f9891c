"""OpenAI LLM客户端"""

import os
from typing import Dict, Any, List
from langchain_openai import ChatOpenAI
from langchain_core.messages import BaseMessage, AIMessage
from .base_client import BaseLLMClient


class OpenAIClient(BaseLLMClient):
    """OpenAI LLM客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化OpenAI客户端"""
        super().__init__(config)
        self._client = self.create_client()
    
    def create_client(self) -> ChatOpenAI:
        """创建OpenAI客户端"""
        api_key = self.config.get("api_key")
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")
        
        if not api_key:
            raise ValueError("OpenAI API key is required")
        
        base_url = self.config.get("base_url")
        model = self.config.get("model", "gpt-4")
        
        return ChatOpenAI(
            api_key=api_key,
            base_url=base_url,
            model=model,
            temperature=self.config.get("temperature", 0.7),
            max_tokens=self.config.get("max_tokens"),
            timeout=self.config.get("timeout", 60),
        )
    
    async def generate(
        self,
        messages: List[BaseMessage],
        **kwargs
    ) -> AIMessage:
        """生成回复"""
        response = await self._client.ainvoke(messages, **kwargs)
        return response
    
    async def stream(
        self,
        messages: List[BaseMessage],
        **kwargs
    ):
        """流式生成回复"""
        async for chunk in self._client.astream(messages, **kwargs):
            yield chunk
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """获取使用统计"""
        stats = super().get_usage_stats()
        stats.update({
            "provider": "openai",
            "model": self.config.get("model", "gpt-4"),
            "base_url": self.config.get("base_url", "https://api.openai.com/v1")
        })
        return stats
