# LangGraph Supervisor 使用指南

本文档介绍如何使用基于LangGraph Supervisor的新版中央协调智能体系统。

## 概述

新的`SupervisorAgent`类基于`langgraph-supervisor`库构建，提供了更强大的多智能体协调功能：

- **智能路由**: 基于任务类型自动选择最合适的智能体
- **工作流管理**: 支持复杂的任务执行计划
- **动态加载**: 自动加载现有的智能体
- **向后兼容**: 保持与旧接口的兼容性

## 快速开始

### 1. 基本使用

```python
from src.langgraph_system.agents.supervisor_agent import SupervisorAgent
from src.langgraph_system.llm.config import LLMConfig

# 创建配置
llm_config = LLMConfig(
    provider="openai",
    model="gpt-4o",
    temperature=0.7
)

# 创建Supervisor
supervisor = SupervisorAgent(llm_config)

# 获取系统状态
status = supervisor.get_system_status()
print(status)
```

### 2. 处理任务

```python
import asyncio

async def process_task():
    task = {
        "id": "task-001",
        "type": "development",
        "description": "创建一个Python计算器",
        "requirements": {
            "功能": ["加法", "减法", "乘法", "除法"]
        }
    }
    
    result = await supervisor.process_task(task)
    print(result["result"])

asyncio.run(process_task())
```

### 3. 处理项目

```python
from src.langgraph_system.states.project_state import ProjectState, TaskType

async def process_project():
    project_state = ProjectState(
        project_id="project-001",
        current_task=TaskType.DEVELOPMENT,
        requirements={"目标": "构建Web应用"}
    )
    
    result = await supervisor.process_project(project_state)
    print(result)

asyncio.run(process_project())
```

## 智能体系统

### 内置智能体

1. **researcher**: 研究分析师
   - 技术调研
   - 需求分析
   - 架构设计

2. **coder**: 开发工程师
   - 代码编写
   - 调试修复
   - 单元测试

3. **tester**: 测试工程师
   - 测试设计
   - 自动化测试
   - 性能测试

### 动态加载

系统会自动加载`src/langgraph_system/agents/`目录下的所有智能体：
- 继承自`LangGraphAgentAdapter`的类
- 文件名格式为`*_agent.py`

## 任务计划

### 创建任务计划

```python
from src.langgraph_system.states.project_state import TaskType

plan = supervisor.create_task_plan(
    TaskType.DEVELOPMENT,
    {"目标": "构建API服务"}
)

for step in plan:
    print(f"步骤{step['step']}: {step['agent']} - {step['action']}")
```

### 支持的任务类型

- `TaskType.ARCHITECTURE`: 架构设计
- `TaskType.DEVELOPMENT`: 开发任务
- `TaskType.TESTING`: 测试任务
- `TaskType.RESEARCH`: 研究任务
- `TaskType.CODING`: 编码任务
- `TaskType.REVIEW`: 代码审查

## 配置

### 环境变量

```bash
# OpenAI
export OPENAI_API_KEY=your-openai-key

# Moonshot
export MOONSHOT_API_KEY=your-moonshot-key

# Anthropic
export ANTHROPIC_API_KEY=your-anthropic-key
```

### LLM配置

```python
from src.langgraph_system.llm.config import LLMConfig

# 使用OpenAI
config = LLMConfig(
    provider="openai",
    model="gpt-4o",
    temperature=0.7
)

# 使用Moonshot
config = LLMConfig(
    provider="moonshot",
    model="moonshot-v1-8k",
    temperature=0.7
)
```

## 向后兼容

新的`SupervisorAgent`提供了`LegacySupervisorAgent`类以保持向后兼容：

```python
from src.langgraph_system.agents.supervisor_agent import LegacySupervisorAgent

# 使用旧接口
legacy_supervisor = LegacySupervisorAgent()
```

## 高级功能

### 获取智能体能力

```python
capabilities = supervisor.get_agent_capabilities()
for agent_name, capability in capabilities.items():
    print(f"{agent_name}: {capability['description']}")
```

### 自定义工具

可以通过继承`LangGraphAgentAdapter`来创建自定义智能体：

```python
from src.langgraph_system.agents.base_agent import LangGraphAgentAdapter

class MyCustomAgent(LangGraphAgentAdapter):
    def __init__(self):
        super().__init__("my_agent")
    
    def get_tools(self):
        return [self.my_custom_tool]
    
    def my_custom_tool(self, input_data):
        return "处理结果"
```

## 错误处理

系统包含完善的错误处理机制：

```python
try:
    result = await supervisor.process_task(task)
except Exception as e:
    print(f"处理失败: {e}")
    # 可以查看详细的错误信息
```

## 最佳实践

1. **API密钥管理**: 使用环境变量存储敏感信息
2. **错误处理**: 始终包含try-catch块
3. **任务分解**: 将大任务分解为小任务
4. **监控日志**: 查看日志了解系统运行状态

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查环境变量是否正确设置
   - 验证API密钥是否有效

2. **智能体加载失败**
   - 检查智能体文件格式
   - 确保继承自`LangGraphAgentAdapter`

3. **任务处理超时**
   - 检查网络连接
   - 调整超时设置

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

supervisor = SupervisorAgent()
# 查看详细日志
```

## 更新日志

- **v2.0**: 基于LangGraph Supervisor重构
- **v1.0**: 原始Supervisor实现
