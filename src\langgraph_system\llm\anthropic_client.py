"""Anthropic LLM客户端"""

import os
from typing import Dict, Any, List
from langchain_anthropic import <PERSON>tAnt<PERSON>
from langchain_core.messages import BaseMessage, AIMessage
from .base_client import BaseLLMClient


class AnthropicClient(BaseLLMClient):
    """Anthropic LLM客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化Anthropic客户端"""
        super().__init__(config)
        self._client = self.create_client()
    
    def create_client(self) -> ChatAnthropic:
        """创建Anthropic客户端"""
        api_key = self.config.get("api_key")
        if not api_key:
            api_key = os.getenv("ANTHROPIC_API_KEY")
        
        if not api_key:
            raise ValueError("Anthropic API key is required")
        
        model = self.config.get("model", "claude-3-sonnet-20240229")
        
        return ChatAnthropic(
            api_key=api_key,
            model=model,
            temperature=self.config.get("temperature", 0.7),
            max_tokens=self.config.get("max_tokens", 1024),
            timeout=self.config.get("timeout", 60),
        )
    
    async def generate(
        self,
        messages: List[BaseMessage],
        **kwargs
    ) -> AIMessage:
        """生成回复"""
        response = await self._client.ainvoke(messages, **kwargs)
        return response
    
    async def stream(
        self,
        messages: List[BaseMessage],
        **kwargs
    ):
        """流式生成回复"""
        async for chunk in self._client.astream(messages, **kwargs):
            yield chunk
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """获取使用统计"""
        stats = super().get_usage_stats()
        stats.update({
            "provider": "anthropic",
            "model": self.config.get("model", "claude-3-sonnet-20240229")
        })
        return stats
