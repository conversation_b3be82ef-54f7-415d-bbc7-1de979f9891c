"""
第一阶段基础设施组件测试
测试分布式状态管理、缓存系统、智能体池化、任务调度和性能监控
"""

import asyncio
import pytest
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.langgraph_system.core.infrastructure import (
    initialize_infrastructure, 
    shutdown_infrastructure,
    get_infrastructure_manager
)
from src.langgraph_system.core.state_manager import DistributedStateManager, StateVersion
from src.langgraph_system.core.cache_manager import IntelligentCacheManager, CacheLevel
from src.langgraph_system.core.agent_pool import AgentPoolManager, PooledAgent
from src.langgraph_system.core.task_scheduler import AsyncTaskScheduler, TaskPriority
from src.langgraph_system.core.performance_monitor import PerformanceMonitor
from src.langgraph_system.states.project_state import ProjectState, TaskType
from src.langgraph_system.agents.base_agent import EnhancedAgentAdapter


class MockAgent(EnhancedAgentAdapter):
    """模拟智能体用于测试"""
    
    def __init__(self, agent_name: str = "test_agent"):
        self.agent_name = agent_name
        self.capabilities = {"test_capability": 0.9}
    
    async def process(self, state: dict, context: dict) -> dict:
        await asyncio.sleep(0.1)  # 模拟处理时间
        return {
            "status": "completed",
            "result": f"Processed by {self.agent_name}",
            "processing_time": 0.1
        }
    
    async def cleanup(self):
        pass


@pytest.fixture
async def infrastructure():
    """基础设施测试夹具"""
    config = {
        "redis_url": "redis://localhost:6379",
        "memory_cache_size": 100,
        "cache_default_ttl": 300,
        "scheduler_max_workers": 3,
        "scheduler_max_queue_size": 100
    }
    
    # 初始化基础设施
    manager = await initialize_infrastructure(config)
    
    yield manager
    
    # 清理
    await shutdown_infrastructure()


@pytest.mark.asyncio
class TestDistributedStateManager:
    """分布式状态管理器测试"""
    
    async def test_state_manager_initialization(self, infrastructure):
        """测试状态管理器初始化"""
        assert infrastructure.is_component_ready("state_manager")
        
        state_manager = infrastructure.get_component("state_manager")
        assert isinstance(state_manager, DistributedStateManager)
        assert state_manager._initialized
    
    async def test_project_state_operations(self, infrastructure):
        """测试项目状态操作"""
        state_manager = infrastructure.get_component("state_manager")
        
        # 创建测试项目状态
        project_state = ProjectState(
            project_name="test_project",
            current_task=TaskType.DEVELOPMENT,
            description="测试项目"
        )
        
        # 保存状态
        success = await state_manager.save_project_state(project_state)
        assert success
        
        # 获取状态
        retrieved_state = await state_manager.get_project_state(project_state.project_id)
        assert retrieved_state is not None
        assert retrieved_state.project_name == "test_project"
        assert retrieved_state.current_task == TaskType.DEVELOPMENT
    
    async def test_state_versioning(self, infrastructure):
        """测试状态版本控制"""
        state_manager = infrastructure.get_component("state_manager")
        
        # 创建项目状态
        project_state = ProjectState(
            project_name="version_test_project",
            description="版本测试项目"
        )
        
        # 保存多个版本
        await state_manager.save_project_state(project_state, create_checkpoint=True)
        
        project_state.description = "更新的描述"
        await state_manager.save_project_state(project_state, create_checkpoint=True)
        
        # 获取版本历史
        versions = await state_manager.get_version_history(project_state.project_id)
        assert len(versions) >= 2
        
        # 测试恢复
        if versions:
            first_version = versions[0]
            restored_state = await state_manager.restore_from_checkpoint(
                project_state.project_id, 
                first_version.version_id
            )
            assert restored_state is not None


@pytest.mark.asyncio
class TestIntelligentCacheManager:
    """智能缓存管理器测试"""
    
    async def test_cache_manager_initialization(self, infrastructure):
        """测试缓存管理器初始化"""
        assert infrastructure.is_component_ready("cache_manager")
        
        cache_manager = infrastructure.get_component("cache_manager")
        assert isinstance(cache_manager, IntelligentCacheManager)
        assert cache_manager._initialized
    
    async def test_basic_cache_operations(self, infrastructure):
        """测试基本缓存操作"""
        cache_manager = infrastructure.get_component("cache_manager")
        
        # 设置缓存
        key = "test_key"
        value = {"data": "test_value", "timestamp": time.time()}
        
        success = await cache_manager.set(key, value, ttl=60)
        assert success
        
        # 获取缓存
        cached_value = await cache_manager.get(key)
        assert cached_value is not None
        assert cached_value["data"] == "test_value"
        
        # 删除缓存
        success = await cache_manager.delete(key)
        assert success
        
        # 验证删除
        cached_value = await cache_manager.get(key)
        assert cached_value is None
    
    async def test_cache_levels(self, infrastructure):
        """测试缓存级别"""
        cache_manager = infrastructure.get_component("cache_manager")
        
        # 测试内存缓存
        await cache_manager.set("memory_key", "memory_value", cache_level=CacheLevel.MEMORY)
        value = await cache_manager.get("memory_key", cache_level=CacheLevel.MEMORY)
        assert value == "memory_value"
        
        # 测试Redis缓存
        await cache_manager.set("redis_key", "redis_value", cache_level=CacheLevel.REDIS)
        value = await cache_manager.get("redis_key", cache_level=CacheLevel.REDIS)
        assert value == "redis_value"
    
    async def test_cache_decorator(self, infrastructure):
        """测试缓存装饰器"""
        cache_manager = infrastructure.get_component("cache_manager")
        
        call_count = 0
        
        @cache_manager.cache_result(ttl=60, key_prefix="test_func")
        async def expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.1)
            return x + y
        
        # 第一次调用
        result1 = await expensive_function(1, 2)
        assert result1 == 3
        assert call_count == 1
        
        # 第二次调用（应该从缓存获取）
        result2 = await expensive_function(1, 2)
        assert result2 == 3
        assert call_count == 1  # 没有增加，说明使用了缓存


@pytest.mark.asyncio
class TestAgentPoolManager:
    """智能体池管理器测试"""
    
    async def test_pool_manager_initialization(self, infrastructure):
        """测试池管理器初始化"""
        assert infrastructure.is_component_ready("agent_pool")
        
        pool_manager = infrastructure.get_component("agent_pool")
        assert isinstance(pool_manager, AgentPoolManager)
        assert pool_manager._initialized
    
    async def test_agent_registration_and_pooling(self, infrastructure):
        """测试智能体注册和池化"""
        pool_manager = infrastructure.get_component("agent_pool")
        
        # 注册智能体类型
        def create_mock_agent():
            return MockAgent("pooled_test_agent")
        
        pool_manager.register_agent_type(
            agent_type="test_agent",
            factory=create_mock_agent,
            min_size=1,
            max_size=3
        )
        
        # 获取智能体
        agent = await pool_manager.get_agent("test_agent")
        assert isinstance(agent, PooledAgent)
        assert agent.agent_type == "test_agent"
        assert agent.is_available
        
        # 归还智能体
        await pool_manager.return_agent(agent)
    
    async def test_agent_execution(self, infrastructure):
        """测试智能体执行"""
        pool_manager = infrastructure.get_component("agent_pool")
        
        # 注册智能体
        pool_manager.register_agent_type(
            "execution_test_agent",
            lambda: MockAgent("execution_test"),
            min_size=1,
            max_size=2
        )
        
        # 执行任务
        async def test_task(agent_instance):
            return await agent_instance.process({}, {})
        
        result = await pool_manager.execute_with_agent(
            "execution_test_agent",
            test_task
        )
        
        assert result["status"] == "completed"
        assert "Processed by execution_test" in result["result"]


@pytest.mark.asyncio
class TestAsyncTaskScheduler:
    """异步任务调度器测试"""
    
    async def test_scheduler_initialization(self, infrastructure):
        """测试调度器初始化"""
        assert infrastructure.is_component_ready("task_scheduler")
        
        scheduler = infrastructure.get_component("task_scheduler")
        assert isinstance(scheduler, AsyncTaskScheduler)
        assert scheduler._running
    
    async def test_basic_task_scheduling(self, infrastructure):
        """测试基本任务调度"""
        scheduler = infrastructure.get_component("task_scheduler")
        
        # 定义测试任务
        result_container = []
        
        async def test_task(value):
            await asyncio.sleep(0.1)
            result_container.append(value)
            return f"Task completed with {value}"
        
        # 调度任务
        task_id = await scheduler.schedule_task(
            name="test_task",
            func=test_task,
            args=[42],
            priority=TaskPriority.HIGH
        )
        
        # 等待任务完成
        result = await scheduler.wait_for_task(task_id, timeout=5.0)
        
        assert result.status.value == "completed"
        assert 42 in result_container
    
    async def test_task_dependencies(self, infrastructure):
        """测试任务依赖"""
        scheduler = infrastructure.get_component("task_scheduler")
        
        execution_order = []
        
        async def task_a():
            execution_order.append("A")
            return "A completed"
        
        async def task_b():
            execution_order.append("B")
            return "B completed"
        
        async def task_c():
            execution_order.append("C")
            return "C completed"
        
        # 调度任务（C依赖B，B依赖A）
        task_a_id = await scheduler.schedule_task("task_a", task_a)
        task_b_id = await scheduler.schedule_task("task_b", task_b, dependencies=[task_a_id])
        task_c_id = await scheduler.schedule_task("task_c", task_c, dependencies=[task_b_id])
        
        # 等待所有任务完成
        await scheduler.wait_for_task(task_c_id, timeout=10.0)
        
        # 验证执行顺序
        assert execution_order == ["A", "B", "C"]
    
    async def test_task_retry(self, infrastructure):
        """测试任务重试"""
        scheduler = infrastructure.get_component("task_scheduler")
        
        attempt_count = 0
        
        async def failing_task():
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                raise Exception(f"Attempt {attempt_count} failed")
            return "Success on attempt 3"
        
        from src.langgraph_system.core.task_scheduler import RetryConfig, RetryStrategy
        
        retry_config = RetryConfig(
            strategy=RetryStrategy.FIXED_DELAY,
            max_retries=3,
            initial_delay=0.1
        )
        
        task_id = await scheduler.schedule_task(
            name="retry_task",
            func=failing_task,
            retry_config=retry_config
        )
        
        result = await scheduler.wait_for_task(task_id, timeout=10.0)
        
        assert result.status.value == "completed"
        assert attempt_count == 3


@pytest.mark.asyncio
class TestPerformanceMonitor:
    """性能监控器测试"""
    
    async def test_monitor_initialization(self, infrastructure):
        """测试监控器初始化"""
        assert infrastructure.is_component_ready("performance_monitor")
        
        monitor = infrastructure.get_component("performance_monitor")
        assert isinstance(monitor, PerformanceMonitor)
        assert monitor._running
    
    async def test_metrics_collection(self, infrastructure):
        """测试指标收集"""
        monitor = infrastructure.get_component("performance_monitor")
        
        # 测试计数器
        monitor.increment_counter("test_counter", 5)
        counter_metric = monitor.get_metric("test_counter")
        assert counter_metric is not None
        
        # 测试仪表
        monitor.set_gauge("test_gauge", 42.5)
        gauge_metric = monitor.get_metric("test_gauge")
        assert gauge_metric is not None
        
        # 测试直方图
        monitor.observe_histogram("test_histogram", 1.5)
        histogram_metric = monitor.get_metric("test_histogram")
        assert histogram_metric is not None
        
        # 测试计时器
        with monitor.time_operation("test_timer"):
            await asyncio.sleep(0.1)
        
        timer_metric = monitor.get_metric("test_timer")
        assert timer_metric is not None
    
    async def test_alerts(self, infrastructure):
        """测试告警"""
        monitor = infrastructure.get_component("performance_monitor")
        
        # 添加告警规则
        monitor.add_alert_rule(
            "test_alert",
            "test_gauge",
            "gt",
            100.0,
            duration=0.1  # 很短的持续时间用于测试
        )
        
        # 触发告警
        monitor.set_gauge("test_gauge", 150.0)
        
        # 等待告警检查
        await asyncio.sleep(0.2)
        
        # 检查告警
        alerts_summary = monitor.get_alerts_summary()
        assert alerts_summary["total_alerts"] > 0


@pytest.mark.asyncio
class TestInfrastructureIntegration:
    """基础设施集成测试"""
    
    async def test_full_infrastructure_health(self, infrastructure):
        """测试完整基础设施健康状态"""
        health_status = await infrastructure.run_health_check()
        
        assert health_status["overall_status"] == "healthy"
        assert all(
            check["status"] == "healthy" 
            for check in health_status["checks"].values()
        )
    
    async def test_component_interactions(self, infrastructure):
        """测试组件间交互"""
        # 获取各个组件
        state_manager = infrastructure.get_component("state_manager")
        cache_manager = infrastructure.get_component("cache_manager")
        pool_manager = infrastructure.get_component("agent_pool")
        scheduler = infrastructure.get_component("task_scheduler")
        monitor = infrastructure.get_component("performance_monitor")
        
        # 创建一个复杂的工作流来测试组件交互
        
        # 1. 创建项目状态
        project_state = ProjectState(
            project_name="integration_test",
            current_task=TaskType.DEVELOPMENT
        )
        await state_manager.save_project_state(project_state)
        
        # 2. 缓存项目信息
        await cache_manager.set(
            f"project:{project_state.project_id}",
            {"name": project_state.project_name, "status": "active"}
        )
        
        # 3. 注册智能体并执行任务
        pool_manager.register_agent_type(
            "integration_agent",
            lambda: MockAgent("integration_test"),
            min_size=1,
            max_size=2
        )
        
        # 4. 调度任务
        async def integration_task():
            # 使用智能体池
            async def agent_work(agent):
                # 记录性能指标
                monitor.increment_counter("integration_tasks")
                
                with monitor.time_operation("integration_task_duration"):
                    result = await agent.process({}, {})
                
                return result
            
            return await pool_manager.execute_with_agent(
                "integration_agent",
                agent_work
            )
        
        task_id = await scheduler.schedule_task(
            "integration_task",
            integration_task
        )
        
        # 5. 等待任务完成
        result = await scheduler.wait_for_task(task_id, timeout=10.0)
        
        assert result.status.value == "completed"
        
        # 6. 验证各组件状态
        detailed_status = await infrastructure.get_detailed_status()
        
        assert detailed_status["initialized"]
        assert detailed_status["components"]["state_manager"]
        assert detailed_status["components"]["cache_manager"]
        assert detailed_status["components"]["agent_pool"]
        assert detailed_status["components"]["task_scheduler"]
        assert detailed_status["components"]["performance_monitor"]


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--asyncio-mode=auto"])