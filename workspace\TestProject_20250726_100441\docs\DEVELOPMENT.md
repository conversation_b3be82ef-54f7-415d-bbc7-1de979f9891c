# TestProject_20250726_100441 开发指南

## 开发环境设置

### 1. 环境要求

- Python 3.8+
- pip 或 poetry

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动开发服务器

```bash
python backend/main.py
```

服务器将在 `http://localhost:8000` 启动。

## 项目结构

```
TestProject_20250726_100441/
├── backend/              # 后端代码
│   └── main.py          # FastAPI应用主文件
├── frontend/            # 前端代码
│   ├── index.html       # 主页面
│   ├── style.css        # 样式文件
│   └── script.js        # JavaScript代码
├── tests/               # 测试代码
│   └── test_main.py     # 主要测试用例
├── docs/                # 文档
│   ├── API.md           # API文档
│   └── DEVELOPMENT.md   # 开发指南
├── config/              # 配置文件
│   └── settings.py      # 应用设置
├── scripts/             # 脚本文件
├── requirements.txt     # Python依赖
├── docker-compose.yml   # Docker配置
├── Dockerfile          # Docker镜像配置
└── README.md           # 项目说明
```

## 开发工作流

### 1. 功能开发

1. 创建新分支
2. 实现功能
3. 编写测试
4. 运行测试确保通过
5. 提交代码

### 2. 测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_main.py

# 运行测试并生成覆盖率报告
pytest --cov=backend tests/
```

### 3. 代码质量

建议使用以下工具保证代码质量：

```bash
# 代码格式化
black backend/

# 代码检查
flake8 backend/

# 类型检查
mypy backend/
```

## 数据库

### 1. 数据库迁移

项目使用SQLAlchemy ORM，数据库表会在应用启动时自动创建。

### 2. 数据库操作

```python
# 添加新的数据模型
class NewModel(Base):
    __tablename__ = "new_table"
    
    id = Column(Integer, primary_key=True)
    name = Column(String, index=True)
```

## 部署

### 1. Docker部署

```bash
# 构建镜像
docker build -t testproject_20250726_100441 .

# 运行容器
docker run -p 8000:8000 testproject_20250726_100441
```

### 2. Docker Compose

```bash
docker-compose up -d
```

## 常见问题

### 1. 端口被占用

如果8000端口被占用，可以修改`backend/main.py`中的端口号：

```python
uvicorn.run(app, host="0.0.0.0", port=8001, reload=True)
```

### 2. 数据库连接问题

检查数据库文件权限和路径是否正确。

### 3. 依赖安装失败

尝试升级pip：
```bash
pip install --upgrade pip
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。
