"""主项目工作流图"""

from typing import Dict, Any, List, Optional
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage, AIMessage, BaseMessage
import logging
from datetime import datetime

from ..states.project_state import ProjectState, TaskType, AgentStatus, MessageType
from ..agents.supervisor_agent import SupervisorAgent
from ..tools.tool_executor import ToolExecutor

logger = logging.getLogger(__name__)

class ProjectWorkflow:
    """基于LangGraph的主项目工作流"""
    
    def __init__(self):
        """初始化工作流"""
        self.supervisor = SupervisorAgent()
        self.tool_executor = ToolExecutor()
        self.graph = StateGraph(ProjectState)
        self.memory_saver = MemorySaver()
        
        # 初始化智能体
        # 注意：我们现在直接使用从 supervisor 动态加载的 agent 实例
        self.agents = self.supervisor.specialist_agents
        self.agents["supervisor"] = self.supervisor
        
        self._setup_graph()
        
    def _setup_graph(self):
        """设置工作流图"""
        # 添加节点
        for agent_name in self.agents.keys():
            self.graph.add_node(agent_name, self._create_node_function(agent_name))
        self.graph.add_node("tool_executor", self._tool_executor_node)
        self.graph.add_node("human_in_the_loop", self._human_in_the_loop_node)

        # 添加边
        self._setup_edges()
        
        # 设置检查点
        self.graph = self.graph.compile(checkpointer=self.memory_saver)
        
    def _setup_edges(self):
        """设置工作流边"""
        # Supervisor到各个智能体的条件边
        self.graph.add_conditional_edges(
            "supervisor",
            self._supervisor_router,
            {**{agent_name: agent_name for agent_name in self.agents if agent_name != "supervisor"},
             "end": END,
             "wait": "supervisor"}
        )
        
        # 从各个智能体返回的条件路由
        for agent_name in self.agents:
            if agent_name != "supervisor":
                self.graph.add_conditional_edges(
                    agent_name,
                    self._agent_router,
                    {
                        "supervisor": "supervisor",
                        "tool_executor": "tool_executor"
                    }
                )
        
        # 从工具执行器返回到刚刚调用它的那个Agent
        self.graph.add_conditional_edges(
            "tool_executor",
            # 这个 lambda 函数读取 state.current_agent 并返回它作为下一个节点
            lambda state: state.current_agent,
            # 创建一个从 agent 名称到其自身的映射
            {agent_name: agent_name for agent_name in self.agents if agent_name != "supervisor"}
        )
        
        # 从人机交互节点返回 supervisor
        self.graph.add_edge("human_in_the_loop", "supervisor")

        # 设置入口点
        self.graph.set_entry_point("supervisor")
        
    def _create_node_function(self, agent_name: str):
        """创建节点函数"""
        async def node_function(state: ProjectState) -> ProjectState:
            """节点执行函数"""
            logger.info(f"Executing {agent_name} node")
            
            if agent_name == "supervisor":
                return await self._supervisor_node(state)
            else:
                return await self._agent_node(state, agent_name)
                
        return node_function
        
    async def _supervisor_node(self, state: ProjectState) -> ProjectState:
        """Supervisor节点"""
        logger.info("--- SUPERVISOR NODE START ---")
        logger.info(f"State before supervisor: {state.agent_status}")
        
        # 调用 supervisor agent 来获取下一步行动, 传递正确的 inputs 结构
        result = await self.supervisor.invoke({"state": state.to_dict()})
        state.next_action = result
        
        logger.info(f"Supervisor decision: {state.next_action}")
        logger.info("--- SUPERVISOR NODE END ---")
        return state
        
    async def _agent_node(self, state: ProjectState, agent_name: str) -> ProjectState:
        """智能体节点"""
        logger.info(f"--- AGENT NODE START: {agent_name} ---")
        logger.info(f"State before {agent_name}: {state.agent_status}")
        
        try:
            agent = self.agents[agent_name]
            
            # 从状态中获取最新的上下文信息
            context = {"agent_name": agent_name, "tool_results": state.tool_results}
            
            # 调用 agent.invoke，它会处理 state 和 context 的提取
            result = await agent.invoke({"state": state.to_dict(), "context": context})
            
            # 清空上一次的工具调用和结果，为下一次迭代做准备
            state.tool_results = []

            if result.get("status") == "error":
                state.update_agent_status(agent_name, AgentStatus.FAILED)
                state.add_message(agent_name, "supervisor", result.get("message", "Unknown error"), "error")
                state.tool_calls = [] # 确保失败时也清空
            elif "tool_calls" in result and result["tool_calls"]:
                # 如果有工具调用，则设置它们，Agent状态保持WORKING
                state.tool_calls = result["tool_calls"]
                logger.info(f"Agent {agent_name} requested tool calls: {state.tool_calls}")
            else:
                # 如果没有工具调用且成功，则认为任务完成
                state.update_agent_status(agent_name, AgentStatus.COMPLETED)
                state.add_message(agent_name, "supervisor", result.get("message", "Task completed."), "result")
                state.tool_calls = [] # 确保完成时也清空

        except Exception as e:
            logger.error(f"Error in {agent_name} node: {str(e)}", exc_info=True)
            state.update_agent_status(agent_name, AgentStatus.FAILED)
            state.add_message(agent_name, "supervisor", str(e), "error")
        
        logger.info(f"State after {agent_name}: {state.agent_status}")
        logger.info(f"--- AGENT NODE END: {agent_name} ---")
        return state

    def _agent_router(self, state: ProjectState) -> str:
        """决定Agent执行后的下一步"""
        if state.tool_calls:
            return "tool_executor"
        
        # 在 CoderAgent 完成后，需要人工审核
        if state.current_agent == "coder": # 'coder' 是 CoderAgent 的名字
            return "human_in_the_loop"
        
        return "supervisor"

    def _human_in_the_loop_node(self, state: ProjectState) -> ProjectState:
        """人机交互节点，暂停等待用户输入"""
        logger.info("Human-in-the-loop node executing. Waiting for user input.")
        
        # 更新状态以表示正在等待用户
        state.update_agent_status(state.current_agent, AgentStatus.WAITING_FOR_USER)
        state.execution_status = "paused_for_input"
        
        # 添加提示用户审核的消息
        # 尝试从最后一个工具结果中获取更具体的文件路径信息
        last_result = state.tool_results[-1] if state.tool_results else {}
        tool_output = last_result.get('result', {})
        file_path_info = tool_output.get('result', 'N/A') if isinstance(tool_output, dict) else "N/A"

        state.add_message(
            "system",
            "user",
            f"代码已生成/修改于 '{file_path_info}'. 请进行审核。您可以回复 'approve' 以继续，或提供反馈。",
            MessageType.QUERY
        )
        
        return state

    def _tool_executor_node(self, state: ProjectState) -> ProjectState:
        """执行工具调用的节点"""
        logger.info("Tool executor node executing")
        tool_results = []
        for tool_call in state.tool_calls:
            result = self.tool_executor.execute(tool_call)
            tool_results.append({
                "tool_name": tool_call["tool_name"],
                "result": result
            })
        
        state.tool_results = tool_results
        logger.info(f"Tool execution completed with results: {tool_results}")
        # 清空工具调用，因为它们已经被执行
        state.tool_calls = []
        return state
        
    def _supervisor_router(self, state: ProjectState) -> str:
        """Supervisor路由函数"""
        logger.info("Supervisor routing...")
        
        # 从状态中获取下一步行动
        next_action = state.next_action
        action = next_action.get("action")
        agent_name = next_action.get("agent")

        if action == "end" or state.execution_status == "completed":
            logger.info("Routing to end.")
            state.execution_status = "completed"
            return "end"

        if action == "execute" and agent_name:
            logger.info(f"Routing to agent: {agent_name}")
            state.update_agent_status(agent_name, AgentStatus.WORKING)
            state.current_agent = agent_name
            state.add_message(
                "supervisor",
                agent_name,
                f"Assigned task: {state.current_task}",
                "task"
            )
            return agent_name
        
        logger.info("Routing to wait (re-planning).")
        return "wait"
        
    # _xxx_process 存根方法已被移除，因为 Agent 现在是动态加载和执行的
        
    async def execute(self, initial_state: ProjectState,
                      config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行工作流
        
        Args:
            initial_state: 初始状态
            config: 配置选项
            
        Returns:
            执行结果
        """
        logger.info(f"Starting workflow execution for project: {initial_state.project_name}")
        
        try:
            # 设置配置
            if config is None:
                config = {"configurable": {"thread_id": initial_state.project_id}}
            
            # 执行工作流
            final_state_data = await self.graph.ainvoke(initial_state, config)

            # 确保我们处理的是ProjectState对象
            if isinstance(final_state_data, dict):
                final_state = ProjectState.from_dict(final_state_data)
            else:
                final_state = final_state_data
            
            return {
                "status": "success",
                "final_state": final_state.to_dict(),
                "project_id": final_state.project_id,
                "execution_time": str(datetime.now() - final_state.start_time)
            }
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "project_id": initial_state.project_id
            }
            
    def get_workflow_info(self) -> Dict[str, Any]:
        """获取工作流信息"""
        return {
            "name": "ProjectWorkflow",
            "nodes": list(self.agents.keys()),
            "description": "Main project workflow using LangGraph",
            "capabilities": list(TaskType)
        }
