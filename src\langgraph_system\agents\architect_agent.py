#!/usr/bin/env python3
"""
LangGraph多智能体系统 v0.3 - 架构师智能体 (重构)
基于 v0.3.0 SpecialistAgent
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, field
import logging

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.tools import tool

from .base_v3 import SpecialistAgent, AgentCapability

logger = logging.getLogger(__name__)

# ============================================================================
# 架构师智能体专用数据结构
# ============================================================================

@dataclass
class ArchitecturalComponent:
    """架构组件"""
    name: str
    type: str  # service, database, cache, queue, etc.
    description: str
    dependencies: List[str] = field(default_factory=list)
    interfaces: List[str] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)
    constraints: List[str] = field(default_factory=list)

@dataclass
class ArchitecturalPattern:
    """架构模式"""
    name: str
    description: str
    use_cases: List[str] = field(default_factory=list)
    pros: List[str] = field(default_factory=list)
    cons: List[str] = field(default_factory=list)
    implementation_notes: str = ""

@dataclass
class TechnologyStack:
    """技术栈"""
    category: str  # frontend, backend, database, etc.
    technologies: List[str] = field(default_factory=list)
    rationale: str = ""
    alternatives: List[str] = field(default_factory=list)

@dataclass
class ArchitecturalDecision:
    """架构决策记录"""
    id: str
    title: str
    status: str  # proposed, accepted, rejected, superseded
    context: str
    decision: str
    consequences: List[str] = field(default_factory=list)
    alternatives: List[str] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)

@dataclass
class SystemDesign:
    """系统设计"""
    name: str
    description: str
    components: List[ArchitecturalComponent] = field(default_factory=list)
    patterns: List[ArchitecturalPattern] = field(default_factory=list)
    technology_stack: List[TechnologyStack] = field(default_factory=list)
    decisions: List[ArchitecturalDecision] = field(default_factory=list)
    quality_attributes: Dict[str, str] = field(default_factory=dict)
    constraints: List[str] = field(default_factory=list)

# ============================================================================
# 架构师智能体实现 (v3)
# ============================================================================

class ArchitectAgent(SpecialistAgent):
    """
    架构师智能体 v3
    
    核心职责：
    - 系统架构设计
    - 技术选型和评估
    - 架构模式推荐
    - 技术债务分析
    - 架构决策记录
    - 性能和可扩展性分析
    """
    
    def __init__(self, model: BaseLanguageModel, custom_tools: List = None, **kwargs):
        # 1. 初始化架构师的知识库
        self.architectural_patterns = self._load_architectural_patterns()
        self.technology_catalog = self._load_technology_catalog()
        self.design_principles = self._load_design_principles()
        self.quality_attributes = self._load_quality_attributes()
        
        # 2. 当前设计项目状态
        self.current_designs: Dict[str, SystemDesign] = {}
        self.decision_history: List[ArchitecturalDecision] = []
        
        # 3. 定义工具列表
        agent_tools = [
            self.design_system,
            self.evaluate_architecture,
            self.select_technology,
            self.record_decision,
            self.analyze_technical_debt,
            self.recommend_patterns,
            self.review_design,
        ]
        if custom_tools:
            agent_tools.extend(custom_tools)

        # 4. 调用父类初始化
        super().__init__(
            agent_id="architect_001",
            name="系统架构师",
            capabilities=[
                AgentCapability.ARCHITECTURE_DESIGN,
                AgentCapability.REQUIREMENT_ANALYSIS,
            ],
            model=model,
            tools=agent_tools,
            **kwargs  # Pass any remaining kwargs to the parent
        )

    # ========================================================================
    # 智能体工具定义
    # ========================================================================

    @tool
    async def design_system(self, requirements: str, constraints: List[str] = None, quality_attributes: Dict[str, str] = None) -> str:
        """
        根据需求、约束和质量属性设计系统架构。
        Args:
            requirements (str): JSON格式的系统需求描述，应包含 'name' 和 'description' 字段.
            constraints (List[str], optional): 系统设计需要遵守的约束条件列表.
            quality_attributes (Dict[str, str], optional): 需要关注的质量属性, e.g., {'performance': 'high', 'scalability': 'very high'}.
        Returns:
            str: JSON格式的系统设计方案摘要.
        """
        try:
            requirements_dict = json.loads(requirements)
            constraints = constraints or []
            quality_attributes = quality_attributes or {}
            
            logger.info(f"开始设计系统: {requirements_dict.get('name', 'Unknown System')}")
            
            design = SystemDesign(
                name=requirements_dict.get("name", "New System"),
                description=requirements_dict.get("description", ""),
                constraints=constraints,
                quality_attributes=quality_attributes
            )
            
            recommended_patterns = await self._recommend_architectural_patterns(requirements_dict)
            design.patterns = recommended_patterns
            
            components = await self._design_system_components(requirements_dict, recommended_patterns)
            design.components = components
            
            tech_stack = await self._recommend_technology_stack(requirements_dict, quality_attributes)
            design.technology_stack = tech_stack
            
            design_id = f"design_{int(time.time())}"
            self.current_designs[design_id] = design
            
            result = {
                "design_id": design_id,
                "system_name": design.name,
                "components_count": len(design.components),
                "patterns_used": [p.name for p in design.patterns],
                "technology_stack": {ts.category: ts.technologies for ts in design.technology_stack},
                "design_summary": self._generate_design_summary(design)
            }
            
            logger.info(f"系统设计完成: {design.name}")
            return json.dumps(result, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"系统设计失败: {e}", exc_info=True)
            return json.dumps({"error": f"系统设计失败: {str(e)}"})

    @tool
    async def evaluate_architecture(self, architecture_data: str, criteria: List[str] = None) -> str:
        """
        评估一个现有的架构设计方案。
        Args:
            architecture_data (str): JSON格式的架构信息.
            criteria (List[str], optional): 评估标准列表. 默认评估所有支持的质量属性.
        Returns:
            str: JSON格式的评估结果.
        """
        try:
            architecture_dict = json.loads(architecture_data)
            evaluation_criteria = criteria or list(self.quality_attributes.keys())
            
            logger.info("开始架构评估")
            
            evaluation_results = {}
            for criterion in evaluation_criteria:
                if criterion in self.quality_attributes:
                    score, feedback = await self._evaluate_quality_attribute(architecture_dict, criterion)
                    evaluation_results[criterion] = {
                        "score": score,
                        "feedback": feedback,
                        "recommendations": await self._get_improvement_recommendations(criterion, score)
                    }
            
            if not evaluation_results:
                return json.dumps({"error": "没有有效的评估标准。"})

            overall_score = sum(result["score"] for result in evaluation_results.values()) / len(evaluation_results)
            
            critical_issues = [
                criterion for criterion, result in evaluation_results.items()
                if result["score"] < 6.0
            ]
            
            result = {
                "overall_score": round(overall_score, 2),
                "evaluation_results": evaluation_results,
                "critical_issues": critical_issues,
                "recommendations": await self._generate_architecture_recommendations(evaluation_results),
                "evaluation_timestamp": time.time()
            }
            
            logger.info(f"架构评估完成，总体评分: {overall_score:.2f}")
            return json.dumps(result, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"架构评估失败: {e}", exc_info=True)
            return json.dumps({"error": f"架构评估失败: {str(e)}"})

    @tool
    async def select_technology(self, category: str, requirements: str, constraints: str = '{}') -> str:
        """
        为特定类别（如 'frontend', 'backend'）推荐并选择技术。
        Args:
            category (str): 技术类别, e.g., 'frontend', 'backend', 'database'.
            requirements (str): JSON格式的需求描述.
            constraints (str, optional): JSON格式的约束条件.
        Returns:
            str: JSON格式的技术选型建议, 包括推荐技术、备选方案和决策理由.
        """
        try:
            requirements_dict = json.loads(requirements)
            constraints_dict = json.loads(constraints)
            
            logger.info(f"开始技术选型: {category}")
            
            candidates = self.technology_catalog.get(category, [])
            if not candidates:
                raise ValueError(f"未找到类别 '{category}' 的技术选项")
            
            evaluations = []
            for tech in candidates:
                evaluation = await self._evaluate_technology(tech, requirements_dict, constraints_dict)
                evaluations.append(evaluation)
            
            evaluations.sort(key=lambda x: x["total_score"], reverse=True)
            recommended = evaluations[0]
            
            decision = ArchitecturalDecision(
                id=f"tech_decision_{int(time.time())}",
                title=f"技术选型: {category}",
                status="accepted",
                context=f"需求: {requirements}, 约束: {constraints}",
                decision=f"选择 {recommended['technology']} 作为 {category} 技术",
                consequences=[f"评分: {recommended['total_score']:.2f}"],
                alternatives=[e['technology'] for e in evaluations[1:3]]
            )
            self.decision_history.append(decision)
            
            result = {
                "category": category,
                "recommended_technology": recommended["technology"],
                "score": recommended["total_score"],
                "rationale": recommended["rationale"],
                "alternatives": evaluations[1:3],
                "decision_id": decision.id
            }
            
            logger.info(f"技术选型完成: {recommended['technology']}")
            return json.dumps(result, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"技术选型失败: {e}", exc_info=True)
            return json.dumps({"error": f"技术选型失败: {str(e)}"})

    @tool
    async def record_decision(self, decision_data: str) -> str:
        """
        记录一个架构决策。
        Args:
            decision_data (str): JSON格式的决策信息, 包含 'title', 'status', 'context', 'decision' 等字段.
        Returns:
            str: JSON格式的确认信息, 包括决策ID.
        """
        try:
            decision_dict = json.loads(decision_data)
            
            decision = ArchitecturalDecision(
                id=decision_dict.get("id", f"decision_{int(time.time())}"),
                title=decision_dict.get("title", ""),
                status=decision_dict.get("status", "proposed"),
                context=decision_dict.get("context", ""),
                decision=decision_dict.get("decision", ""),
                consequences=decision_dict.get("consequences", []),
                alternatives=decision_dict.get("alternatives", [])
            )
            
            self.decision_history.append(decision)
            
            logger.info(f"架构决策已记录: {decision.title}")
            
            return json.dumps({
                "decision_id": decision.id,
                "status": "recorded",
                "timestamp": decision.timestamp
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"记录架构决策失败: {e}", exc_info=True)
            return json.dumps({"error": f"记录架构决策失败: {str(e)}"})

    @tool
    async def analyze_technical_debt(self, codebase_info: str, scope: List[str] = None) -> str:
        """
        分析技术债务。
        Args:
            codebase_info (str): JSON格式的代码库信息, e.g., '{"code_coverage": 75, "cyclomatic_complexity": 12}'.
            scope (List[str], optional): 分析范围, e.g., ["code", "architecture"]. Defaults to ["code", "architecture", "documentation"].
        Returns:
            str: JSON格式的技术债务分析报告.
        """
        try:
            codebase_dict = json.loads(codebase_info)
            analysis_scope = scope or ["code", "architecture", "documentation"]
            
            logger.info("开始技术债务分析")
            
            debt_analysis = {}
            total_debt_score = 0
            
            for s in analysis_scope:
                analysis = await self._analyze_debt_in_scope(codebase_dict, s)
                debt_analysis[s] = analysis
                total_debt_score += analysis["severity_score"]
            
            recommendations = await self._generate_debt_reduction_plan(debt_analysis)
            
            result = {
                "total_debt_score": total_debt_score,
                "debt_analysis": debt_analysis,
                "priority_issues": [
                    issue for scope_analysis in debt_analysis.values()
                    for issue in scope_analysis["issues"]
                    if issue["severity"] in ["high", "critical"]
                ],
                "recommendations": recommendations,
                "estimated_effort": self._estimate_debt_reduction_effort(debt_analysis)
            }
            
            logger.info(f"技术债务分析完成，总债务评分: {total_debt_score}")
            return json.dumps(result, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"技术债务分析失败: {e}", exc_info=True)
            return json.dumps({"error": f"技术债务分析失败: {str(e)}"})

    @tool
    async def recommend_patterns(self, problem: str, context: str = '{}', constraints: List[str] = None) -> str:
        """
        根据问题描述和上下文推荐架构模式。
        Args:
            problem (str): 需要解决的问题的描述.
            context (str, optional): JSON格式的上下文信息.
            constraints (List[str], optional): 约束条件列表.
        Returns:
            str: JSON格式的推荐模式列表.
        """
        try:
            context_dict = json.loads(context)
            constraints = constraints or []
            
            logger.info("开始架构模式推荐")
            
            recommended_patterns = []
            for pattern in self.architectural_patterns:
                relevance_score = await self._calculate_pattern_relevance(pattern, problem, context_dict, constraints)
                if relevance_score > 0.6:
                    recommended_patterns.append({
                        "pattern_name": pattern.name,
                        "description": pattern.description,
                        "relevance_score": relevance_score,
                        "application_notes": await self._generate_application_notes(pattern, context_dict)
                    })
            
            recommended_patterns.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            result = {
                "problem": problem,
                "recommended_patterns": recommended_patterns[:5],
                "pattern_combinations": await self._suggest_pattern_combinations(recommended_patterns[:3])
            }
            
            logger.info(f"架构模式推荐完成，推荐 {len(recommended_patterns)} 个模式")
            return json.dumps(result, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"架构模式推荐失败: {e}", exc_info=True)
            return json.dumps({"error": f"架构模式推荐失败: {str(e)}"})

    @tool
    async def review_design(self, design_id: str, criteria: List[str] = None) -> str:
        """
        评审一个已有的系统设计。
        Args:
            design_id (str): 要评审的设计方案ID.
            criteria (List[str], optional): 评审标准列表. Defaults to ["completeness", "consistency", "feasibility", "maintainability"].
        Returns:
            str: JSON格式的评审结果.
        """
        try:
            review_criteria = criteria or ["completeness", "consistency", "feasibility", "maintainability"]
            
            if design_id not in self.current_designs:
                raise ValueError(f"设计 '{design_id}' 不存在")
            
            design = self.current_designs[design_id]
            logger.info(f"开始设计评审: {design.name}")
            
            review_results = {}
            for criterion in review_criteria:
                score, feedback = await self._review_design_criterion(design, criterion)
                review_results[criterion] = {"score": score, "feedback": feedback}
            
            overall_score = sum(result["score"] for result in review_results.values()) / len(review_results)
            
            improvements = await self._generate_design_improvements(design, review_results)
            
            result = {
                "design_id": design_id,
                "design_name": design.name,
                "overall_score": round(overall_score, 2),
                "review_results": review_results,
                "improvements": improvements,
                "approval_status": "approved" if overall_score >= 7.0 else "needs_improvement"
            }
            
            logger.info(f"设计评审完成: {design.name}, 评分: {overall_score:.2f}")
            return json.dumps(result, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"设计评审失败: {e}", exc_info=True)
            return json.dumps({"error": f"设计评审失败: {str(e)}"})
    
    # ========================================================================
    # 内部知识库和辅助方法
    # ========================================================================
    
    def _load_architectural_patterns(self) -> List[ArchitecturalPattern]:
        """加载架构模式库"""
        return [
            ArchitecturalPattern(
                name="微服务架构",
                description="将应用程序构建为一套小型服务的方法",
                use_cases=["大型应用", "团队独立开发", "技术栈多样化"],
                pros=["独立部署", "技术多样性", "故障隔离"],
                cons=["复杂性增加", "网络延迟", "数据一致性挑战"]
            ),
            ArchitecturalPattern(
                name="分层架构",
                description="将系统组织成水平层次的架构模式",
                use_cases=["企业应用", "传统系统", "清晰的关注点分离"],
                pros=["简单易懂", "关注点分离", "可测试性"],
                cons=["性能开销", "紧耦合风险", "变更影响多层"]
            ),
            ArchitecturalPattern(
                name="事件驱动架构",
                description="基于事件的生产、检测和消费的架构模式",
                use_cases=["实时系统", "松耦合系统", "异步处理"],
                pros=["松耦合", "可扩展性", "实时响应"],
                cons=["复杂性", "事件顺序", "调试困难"]
            ),
            ArchitecturalPattern(
                name="CQRS",
                description="命令查询职责分离模式",
                use_cases=["复杂业务逻辑", "读写分离", "性能优化"],
                pros=["读写优化", "复杂查询", "可扩展性"],
                cons=["复杂性", "数据一致性", "学习曲线"]
            )
        ]
    
    def _load_technology_catalog(self) -> Dict[str, List[Dict[str, Any]]]:
        """加载技术目录"""
        return {
            "frontend": [
                {"name": "React", "type": "framework", "maturity": "high", "learning_curve": "medium"},
                {"name": "Vue.js", "type": "framework", "maturity": "high", "learning_curve": "low"},
                {"name": "Angular", "type": "framework", "maturity": "high", "learning_curve": "high"}
            ],
            "backend": [
                {"name": "Node.js", "type": "runtime", "maturity": "high", "performance": "high"},
                {"name": "Python/Django", "type": "framework", "maturity": "high", "productivity": "high"},
                {"name": "Java/Spring", "type": "framework", "maturity": "high", "enterprise": "high"}
            ],
            "database": [
                {"name": "PostgreSQL", "type": "relational", "maturity": "high", "features": "rich"},
                {"name": "MongoDB", "type": "document", "maturity": "high", "flexibility": "high"},
                {"name": "Redis", "type": "cache", "maturity": "high", "performance": "very_high"}
            ],
            "infrastructure": [
                {"name": "Docker", "type": "containerization", "maturity": "high", "portability": "high"},
                {"name": "Kubernetes", "type": "orchestration", "maturity": "high", "scalability": "high"},
                {"name": "AWS", "type": "cloud", "maturity": "high", "services": "comprehensive"}
            ]
        }
    
    def _load_design_principles(self) -> List[str]:
        """加载设计原则"""
        return [
            "单一职责原则", "开放封闭原则", "里氏替换原则", "接口隔离原则",
            "依赖倒置原则", "DRY", "KISS", "YAGNI"
        ]
    
    def _load_quality_attributes(self) -> Dict[str, str]:
        """加载质量属性"""
        return {
            "performance": "系统响应时间和吞吐量",
            "scalability": "系统处理负载增长的能力",
            "reliability": "系统在指定条件下正确执行的能力",
            "availability": "系统可用时间的百分比",
            "security": "系统保护数据和抵御攻击的能力",
            "maintainability": "系统易于修改和维护的程度",
            "usability": "系统易于使用的程度",
            "portability": "系统在不同环境中运行的能力"
        }
    
    async def _recommend_architectural_patterns(self, requirements: Dict[str, Any]) -> List[ArchitecturalPattern]:
        """推荐架构模式 (内部实现)"""
        recommended = []
        req_text = str(requirements).lower()
        for pattern in self.architectural_patterns:
            if any(use_case.lower() in req_text for use_case in pattern.use_cases):
                recommended.append(pattern)
        return recommended[:3]

    async def _design_system_components(self, requirements: Dict[str, Any], patterns: List[ArchitecturalPattern]) -> List[ArchitecturalComponent]:
        """设计系统组件 (内部实现)"""
        components = []
        if any(p.name == "微服务架构" for p in patterns):
            components.extend([
                ArchitecturalComponent(name="API Gateway", type="gateway", description="统一API入口和路由", interfaces=["HTTP", "WebSocket"]),
                ArchitecturalComponent(name="User Service", type="microservice", description="用户管理服务", dependencies=["Database"]),
                ArchitecturalComponent(name="Service Registry", type="infrastructure", description="服务注册和发现", interfaces=["REST API"])
            ])
        components.extend([
            ArchitecturalComponent(name="Database", type="database", description="数据存储层", interfaces=["SQL", "Connection Pool"]),
            ArchitecturalComponent(name="Cache", type="cache", description="缓存层", interfaces=["Redis Protocol"])
        ])
        return components
    
    async def _recommend_technology_stack(self, requirements: Dict[str, Any], quality_attributes: Dict[str, str]) -> List[TechnologyStack]:
        """推荐技术栈 (内部实现)"""
        tech_stack = []
        if "web" in str(requirements).lower():
            tech_stack.append(TechnologyStack(category="frontend", technologies=["React", "TypeScript", "Webpack"], rationale="现代Web应用开发的成熟技术栈"))
        tech_stack.append(TechnologyStack(category="backend", technologies=["Node.js", "Express", "TypeScript"], rationale="高性能异步处理和JavaScript生态"))
        tech_stack.append(TechnologyStack(category="database", technologies=["PostgreSQL", "Redis"], rationale="可靠的关系型数据库和高性能缓存"))
        return tech_stack

    def _generate_design_summary(self, design: SystemDesign) -> str:
        """生成设计摘要 (内部实现)"""
        return f"系统设计摘要: 系统名称: {design.name}, 组件数量: {len(design.components)}, 架构模式: {', '.join(p.name for p in design.patterns)}"
    
    async def _evaluate_quality_attribute(self, architecture: Dict[str, Any], attribute: str) -> tuple[float, str]:
        """评估质量属性 (内部实现)"""
        base_score, feedback = 7.0, f"{attribute} 评估: 基础实现满足要求"
        if attribute == "scalability" and "microservice" in str(architecture).lower():
            base_score += 1.0; feedback += ", 微服务架构提供良好的可扩展性"
        if attribute == "performance" and "cache" in str(architecture).lower():
            base_score += 0.5; feedback += ", 缓存层提升性能"
        return min(base_score, 10.0), feedback
    
    async def _get_improvement_recommendations(self, attribute: str, score: float) -> List[str]:
        """获取改进建议 (内部实现)"""
        if score < 6.0: return [f"需要重点改进 {attribute}", "考虑添加缓存层", "优化数据库查询"]
        return []

    async def _generate_architecture_recommendations(self, evaluation_results: Dict[str, Any]) -> List[str]:
        """生成架构建议 (内部实现)"""
        return ["定期进行架构评审", "建立架构决策记录"]

    async def _evaluate_technology(self, tech: Dict[str, Any], requirements: Dict[str, Any], constraints: Dict[str, Any]) -> Dict[str, Any]:
        """评估技术选项 (内部实现)"""
        base_score = 7.0
        if tech.get("maturity") == "high": base_score += 1.0
        if tech.get("performance") == "high": base_score += 0.5
        return {"technology": tech["name"], "total_score": min(base_score, 10.0), "rationale": "基于成熟度和性能的评估"}

    async def _analyze_debt_in_scope(self, codebase: Dict[str, Any], scope: str) -> Dict[str, Any]:
        """分析特定范围的技术债务 (内部实现)"""
        issues, severity_score = [], 0
        if scope == "code" and codebase.get("cyclomatic_complexity", 0) > 10:
            issues.append({"type": "complexity", "severity": "high", "description": "代码复杂度过高"})
            severity_score += 5
        return {"scope": scope, "issues": issues, "severity_score": severity_score}

    async def _generate_debt_reduction_plan(self, debt_analysis: Dict[str, Any]) -> List[str]:
        """生成债务减少计划 (内部实现)"""
        return ["建立技术债务跟踪机制", "定期进行代码审查"]

    def _estimate_debt_reduction_effort(self, debt_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """估算债务减少工作量 (内部实现)"""
        total_issues = sum(len(analysis["issues"]) for analysis in debt_analysis.values())
        return {"total_issues": total_issues, "estimated_days": total_issues * 2}

    async def _calculate_pattern_relevance(self, pattern: ArchitecturalPattern, problem: str, context: Dict[str, Any], constraints: List[str]) -> float:
        """计算模式相关性 (内部实现)"""
        return 0.7 if any(use_case.lower() in problem.lower() for use_case in pattern.use_cases) else 0.3

    async def _generate_application_notes(self, pattern: ArchitecturalPattern, context: Dict[str, Any]) -> str:
        """生成应用说明 (内部实现)"""
        return f"应用 {pattern.name} 模式时需要考虑..."

    async def _suggest_pattern_combinations(self, patterns: List[Dict[str, Any]]) -> List[str]:
        """建议模式组合 (内部实现)"""
        return ["微服务 + 事件驱动: 适合复杂分布式系统"] if len(patterns) > 1 else []

    async def _review_design_criterion(self, design: SystemDesign, criterion: str) -> tuple[float, str]:
        """评审设计标准 (内部实现)"""
        return (7.5, "设计完整")

    async def _generate_design_improvements(self, design: SystemDesign, review_results: Dict[str, Any]) -> List[str]:
        """生成设计改进建议 (内部实现)"""
        return ["添加详细的组件交互图", "完善非功能性需求规范"]


# ============================================================================
# 导出
# ============================================================================

__all__ = ['ArchitectAgent', 'SystemDesign', 'ArchitecturalComponent', 'ArchitecturalPattern', 'TechnologyStack', 'ArchitecturalDecision']
