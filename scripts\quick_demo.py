#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速演示脚本 - 测试LangGraph多智能体协作平台
避免长时间API调用，专注于核心功能验证
"""

import sys
import os
import subprocess
import time
import requests
from pathlib import Path

# 设置编码
os.environ['PYTHONIOENCODING'] = 'utf-8'

def safe_print(message):
    """安全打印函数"""
    try:
        print(message)
    except UnicodeEncodeError:
        # 替换特殊字符
        safe_msg = (message
                   .replace('✅', '[OK]')
                   .replace('❌', '[ERROR]')
                   .replace('🚀', '[START]')
                   .replace('🔧', '[TOOL]')
                   .replace('📊', '[STATS]')
                   .replace('🤖', '[AGENT]')
                   .replace('⚡', '[POWER]')
                   .replace('💻', '[CODE]'))
        print(safe_msg)

def run_command(cmd, timeout=15):
    """运行命令并返回结果"""
    try:
        env = os.environ.copy()
        env.update({
            'PYTHONIOENCODING': 'utf-8',
            'PYTHONLEGACYWINDOWSSTDIO': '0'
        })
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout,
            env=env,
            encoding='utf-8',
            errors='replace'
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令超时"
    except Exception as e:
        return False, "", str(e)

def test_system_status():
    """测试系统状态"""
    safe_print("🔧 测试1: 系统状态检查")
    
    cmd = [sys.executable, "scripts/run_cli.py", "--llm-provider", "moonshot", "status"]
    success, stdout, stderr = run_command(cmd)
    
    if success:
        safe_print("✅ 系统状态正常")
        # 提取关键信息
        if "专业智能体: 7 个" in stdout:
            safe_print("   - 7个专业智能体已加载")
        if "协作模式: 7 种" in stdout:
            safe_print("   - 7种协作模式可用")
        if "状态: ready" in stdout:
            safe_print("   - 系统状态: 就绪")
        return True
    else:
        safe_print(f"❌ 系统状态检查失败: {stderr}")
        return False

def test_agents_list():
    """测试智能体列表"""
    safe_print("\n🤖 测试2: 智能体列表")
    
    cmd = [sys.executable, "scripts/run_cli.py", "--llm-provider", "moonshot", "agents", "list"]
    success, stdout, stderr = run_command(cmd)
    
    if success:
        safe_print("✅ 智能体列表获取成功")
        
        # 检查关键智能体
        agents = ["architect", "product_manager", "coder", "qa_engineer", "devops", "documentation", "security"]
        found_agents = []
        
        for agent in agents:
            if agent in stdout:
                found_agents.append(agent)
        
        safe_print(f"   - 找到 {len(found_agents)}/{len(agents)} 个智能体")
        for agent in found_agents:
            safe_print(f"     • {agent}")
        
        return len(found_agents) >= 5
    else:
        safe_print(f"❌ 智能体列表获取失败: {stderr}")
        return False

def test_project_structure():
    """测试项目结构"""
    safe_print("\n📁 测试3: 项目结构验证")
    
    project_dir = Path("workspace/SmartTodoManager")
    if not project_dir.exists():
        safe_print("❌ 项目目录不存在")
        return False
    
    expected_files = [
        "README.md",
        "requirements.txt",
        "backend/main.py",
        "frontend/index.html",
        "tests/test_main.py",
        "docs/README.md"
    ]
    
    existing_files = []
    for file_path in expected_files:
        full_path = project_dir / file_path
        if full_path.exists():
            existing_files.append(file_path)
            safe_print(f"   ✅ {file_path}")
        else:
            safe_print(f"   ❌ {file_path}")
    
    success_rate = len(existing_files) / len(expected_files)
    safe_print(f"   📊 文件完整性: {success_rate:.1%} ({len(existing_files)}/{len(expected_files)})")
    
    return success_rate >= 0.8

def test_backend_code():
    """测试后端代码"""
    safe_print("\n💻 测试4: 后端代码验证")
    
    backend_file = Path("workspace/SmartTodoManager/backend/main.py")
    if not backend_file.exists():
        safe_print("❌ 后端文件不存在")
        return False
    
    try:
        content = backend_file.read_text(encoding='utf-8')
        
        # 检查关键组件
        checks = [
            ("FastAPI应用", "FastAPI" in content),
            ("数据模型", "class TodoItem" in content),
            ("API端点", "@app.get" in content and "@app.post" in content),
            ("数据库配置", "SQLAlchemy" in content),
            ("Pydantic模型", "BaseModel" in content)
        ]
        
        passed = 0
        for check_name, result in checks:
            if result:
                safe_print(f"   ✅ {check_name}")
                passed += 1
            else:
                safe_print(f"   ❌ {check_name}")
        
        safe_print(f"   📊 代码质量: {passed}/{len(checks)} ({passed/len(checks):.1%})")
        return passed >= len(checks) * 0.8
        
    except Exception as e:
        safe_print(f"❌ 代码检查失败: {e}")
        return False

def test_api_functionality():
    """测试API功能（不启动服务器）"""
    safe_print("\n🧪 测试5: API功能验证")
    
    try:
        # 使用FastAPI TestClient进行测试
        sys.path.insert(0, str(Path("workspace/SmartTodoManager").absolute()))
        
        from fastapi.testclient import TestClient
        from backend.main import app
        
        client = TestClient(app)
        
        # 测试根端点
        response = client.get("/")
        if response.status_code == 200:
            safe_print("   ✅ 根端点正常")
        else:
            safe_print(f"   ❌ 根端点失败: {response.status_code}")
            return False
        
        # 测试获取待办事项
        response = client.get("/todos")
        if response.status_code == 200:
            todos = response.json()
            safe_print(f"   ✅ 获取待办事项: {len(todos)} 个")
        else:
            safe_print(f"   ❌ 获取待办事项失败: {response.status_code}")
            return False
        
        # 测试创建待办事项
        todo_data = {
            "title": "演示任务",
            "description": "这是一个演示任务",
            "priority": 2
        }
        response = client.post("/todos", json=todo_data)
        if response.status_code == 200:
            todo = response.json()
            safe_print(f"   ✅ 创建待办事项: {todo['title']}")
        else:
            safe_print(f"   ❌ 创建待办事项失败: {response.status_code}")
            return False
        
        # 验证创建成功
        response = client.get("/todos")
        if response.status_code == 200:
            todos = response.json()
            safe_print(f"   ✅ 验证创建: 现在有 {len(todos)} 个待办事项")
        
        return True
        
    except Exception as e:
        safe_print(f"❌ API测试失败: {e}")
        return False

def main():
    """主函数"""
    safe_print("🚀 LangGraph多智能体协作平台 - 快速演示")
    safe_print("=" * 60)
    
    # 切换到项目目录
    os.chdir(Path(__file__).parent.parent)
    
    tests = [
        ("系统状态", test_system_status),
        ("智能体列表", test_agents_list),
        ("项目结构", test_project_structure),
        ("后端代码", test_backend_code),
        ("API功能", test_api_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            safe_print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 生成总结
    safe_print("\n" + "=" * 60)
    safe_print("📊 测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        safe_print(f"   {status} {test_name}")
    
    success_rate = passed / total * 100
    safe_print(f"\n🎯 总体结果: {passed}/{total} 通过 ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        safe_print("🎉 演示成功！LangGraph多智能体协作平台功能正常")
    elif success_rate >= 60:
        safe_print("👍 演示基本成功，部分功能需要优化")
    else:
        safe_print("⚠️ 演示发现问题，需要进一步检查")
    
    safe_print("\n💡 下一步建议:")
    safe_print("   1. 查看生成的项目: workspace/SmartTodoManager/")
    safe_print("   2. 运行后端服务: cd workspace/SmartTodoManager && python backend/main.py")
    safe_print("   3. 访问API文档: http://localhost:8000/docs")
    safe_print("   4. 查看前端界面: workspace/SmartTodoManager/frontend/index.html")
    
    return 0 if success_rate >= 80 else 1

if __name__ == "__main__":
    sys.exit(main())
