"""项目状态管理系统"""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime
import uuid

class TaskType(str, Enum):
    """任务类型枚举"""
    ARCHITECTURE = "architecture"
    DEVELOPMENT = "development"
    REVIEW = "review"
    TESTING = "testing"
    DEBUGGING = "debugging"
    DOCUMENTATION = "documentation"
    DEPLOYMENT = "deployment"
    RESEARCH = "research"
    CODING = "coding"

class AgentStatus(str, Enum):
    """智能体状态枚举"""
    IDLE = "idle"
    WORKING = "working"
    COMPLETED = "completed"
    FAILED = "failed"
    WAITING = "waiting"
    WAITING_FOR_USER = "waiting_for_user"

class MessageType(str, Enum):
    """消息类型枚举"""
    TASK = "task"
    RESULT = "result"
    ERROR = "error"
    QUERY = "query"
    STATUS = "status"

class AgentMessage(BaseModel):
    """智能体间消息"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    sender: str
    recipient: str
    content: Any
    message_type: MessageType
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict)

class ProjectState(BaseModel):
    """全局项目状态"""
    project_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    project_name: str = "Untitled Project"
    current_task: Optional[TaskType] = None
    current_agent: Optional[str] = None
    
    # 消息和通信
    messages: List[AgentMessage] = Field(default_factory=list)
    pending_tasks: List[Dict[str, Any]] = Field(default_factory=list)
    completed_tasks: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 智能体状态
    agent_status: Dict[str, AgentStatus] = Field(default_factory=dict)
    agent_assignments: Dict[str, str] = Field(default_factory=dict)
    
    # 项目工件
    artifacts: Dict[str, Any] = Field(default_factory=dict)
    files: Dict[str, str] = Field(default_factory=dict)
    code_reviews: List[Dict[str, Any]] = Field(default_factory=list)
    test_results: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 工具调用
    tool_calls: List[Dict[str, Any]] = Field(default_factory=list)
    tool_results: List[Dict[str, Any]] = Field(default_factory=list)

    # 上下文信息
    context: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # 项目需求
    requirements: Dict[str, Any] = Field(default_factory=dict)
    description: str = ""
    
    # 执行状态
    execution_status: str = "initialized"
    next_action: Optional[Dict[str, Any]] = Field(default=None, description="Supervisor的下一步行动决策")
    start_time: datetime = Field(default_factory=datetime.now)
    last_update: datetime = Field(default_factory=datetime.now)
    refactor_count: int = 0
    
    def add_message(self, sender: str, recipient: str, content: Any, 
                   message_type: MessageType = MessageType.TASK,
                   metadata: Optional[Dict[str, Any]] = None) -> AgentMessage:
        """添加消息到状态"""
        message = AgentMessage(
            sender=sender,
            recipient=recipient,
            content=content,
            message_type=message_type,
            metadata=metadata or {}
        )
        self.messages.append(message)
        self.last_update = datetime.now()
        return message
    
    def update_agent_status(self, agent_name: str, status: AgentStatus):
        """更新智能体状态"""
        self.agent_status[agent_name] = status
        self.last_update = datetime.now()
    
    def add_artifact(self, artifact_type: str, content: Any, 
                    metadata: Optional[Dict[str, Any]] = None):
        """添加项目工件"""
        artifact_id = str(uuid.uuid4())
        self.artifacts[artifact_id] = {
            "type": artifact_type,
            "content": content,
            "metadata": metadata or {},
            "created_at": datetime.now()
        }
        self.last_update = datetime.now()
        return artifact_id
    
    def get_agent_messages(self, agent_name: str) -> List[AgentMessage]:
        """获取特定智能体的消息"""
        return [msg for msg in self.messages 
                if msg.recipient == agent_name or msg.sender == agent_name]
    
    def get_latest_message(self) -> Optional[AgentMessage]:
        """获取最新消息"""
        return self.messages[-1] if self.messages else None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ProjectState":
        """从字典创建实例"""
        return cls(**data)
