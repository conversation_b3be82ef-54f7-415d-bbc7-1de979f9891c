"""
核心异常定义
"""

class LangGraphError(Exception):
    """LangGraph系统基础异常"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class AgentError(LangGraphError):
    """智能体相关异常"""
    
    def __init__(self, agent_name: str, message: str, error_code: str = None, details: dict = None):
        super().__init__(message, error_code, details)
        self.agent_name = agent_name
        self.details.update({"agent_name": agent_name})


class ToolError(LangGraphError):
    """工具执行异常"""
    
    def __init__(self, tool_name: str, message: str, error_code: str = None, details: dict = None):
        super().__init__(message, error_code, details)
        self.tool_name = tool_name
        self.details.update({"tool_name": tool_name})


class ConfigurationError(LangGraphError):
    """配置异常"""
    pass


class WorkflowError(LangGraphError):
    """工作流异常"""
    pass


class LLMError(LangGraphError):
    """LLM相关异常"""
    
    def __init__(self, provider: str, message: str, error_code: str = None, details: dict = None):
        super().__init__(message, error_code, details)
        self.provider = provider
        self.details.update({"provider": provider})


class StateManagerError(LangGraphError):
    """状态管理器异常"""
    pass


class StateLockError(StateManagerError):
    """状态锁异常"""
    pass


class StateVersionError(StateManagerError):
    """状态版本异常"""
    pass


class TaskSchedulerError(LangGraphError):
    """任务调度器异常"""
    pass


class TaskNotFoundError(TaskSchedulerError):
    """任务未找到异常"""
    pass


class TaskDependencyError(TaskSchedulerError):
    """任务依赖异常"""
    pass


class AgentPoolError(LangGraphError):
    """智能体池相关异常"""
    pass


class AgentNotAvailableError(AgentPoolError):
    """智能体不可用异常"""
    pass