"""
文件系统工具集，为Agent提供与文件系统交互的能力。
所有操作都将被限制在项目根目录下的 'workspace' 文件夹内，以确保安全。
"""
import os
from typing import List

# 获取项目根目录
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", ".."))
# 定义并确保工作区目录存在
WORKSPACE_DIR = os.path.join(PROJECT_ROOT, "workspace")
os.makedirs(WORKSPACE_DIR, exist_ok=True)

def _get_safe_path(file_path: str) -> str:
    """
    将相对路径转换为工作区内的安全绝对路径。
    防止路径遍历攻击 (e.g., '../../etc/passwd').
    """
    # 将输入路径转换为绝对路径
    abs_path = os.path.abspath(os.path.join(WORKSPACE_DIR, file_path))
    
    # 检查解析后的路径是否仍然在 WORKSPACE_DIR 内
    if not abs_path.startswith(WORKSPACE_DIR):
        raise ValueError(f"路径 '{file_path}' 超出了允许的工作区范围。")
        
    return abs_path

def read_file(file_path: str) -> str:
    """
    读取指定路径的文件内容。
    路径是相对于 'workspace' 目录的。
    """
    try:
        safe_path = _get_safe_path(file_path)
        with open(safe_path, "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return f"错误: 文件 '{file_path}' 未找到。"
    except Exception as e:
        return f"读取文件 '{file_path}' 时发生错误: {e}"

def write_file(file_path: str, content: str) -> str:
    """
    将内容写入指定路径的文件。
    路径是相对于 'workspace' 目录的。
    如果目录不存在，会自动创建。
    """
    try:
        safe_path = _get_safe_path(file_path)
        # 确保目标文件的父目录存在
        os.makedirs(os.path.dirname(safe_path), exist_ok=True)
        
        with open(safe_path, "w", encoding="utf-8") as f:
            f.write(content)
        return f"文件 '{file_path}' 已成功写入。"
    except Exception as e:
        return f"写入文件 '{file_path}' 时发生错误: {e}"

def list_files(directory_path: str = ".") -> List[str]:
    """
    列出指定目录下的文件和子目录。
    路径是相对于 'workspace' 目录的。
    """
    try:
        safe_path = _get_safe_path(directory_path)
        return os.listdir(safe_path)
    except FileNotFoundError:
        return [f"错误: 目录 '{directory_path}' 未找到。"]
    except Exception as e:
        return [f"列出目录 '{directory_path}' 内容时发生错误: {e}"]
