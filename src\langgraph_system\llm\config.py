"""LLM配置"""

import os
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class LLMConfig(BaseModel):
    """LLM配置类"""
    
    # 基础配置
    provider: str = Field(default_factory=lambda: os.getenv("LLM_PROVIDER"), description="LLM提供商")
    model: str = Field(default="gpt-4", description="模型名称")
    temperature: float = Field(default=0.7, description="温度参数")
    max_tokens: Optional[int] = Field(default=None, description="最大token数")
    timeout: int = Field(default=120, description="超时时间")
    
    # OpenAI配置
    openai_api_key: Optional[str] = Field(default=os.getenv("OPENAI_API_KEY"), description="OpenAI API密钥")
    openai_base_url: Optional[str] = Field(default=os.getenv("OPENAI_API_BASE"), description="OpenAI基础URL")
    
    # Anthropic配置
    anthropic_api_key: Optional[str] = Field(default=os.getenv("ANTHROPIC_API_KEY"), description="Anthropic API密钥")
    
    # Moonshot配置
    moonshot_api_key: Optional[str] = Field(default=os.getenv("MOONSHOT_API_KEY"), description="Moonshot API密钥")
    moonshot_base_url: str = Field(
        default=os.getenv("MOONSHOT_API_BASE", "https://api.moonshot.cn/v1"),
        description="Moonshot API基础URL"
    )
    
    class Config:
        env_file = ".env"
        # 移除 env_prefix，直接使用环境变量名
        extra = "ignore"

    def __init__(self, **data):
        # 强制从 .env 文件加载环境变量
        from dotenv import load_dotenv
        load_dotenv(override=True)
        
        # 如果 provider 不是在代码中直接指定的，就从环境变量加载
        if 'provider' not in data:
            data['provider'] = os.getenv("LLM_PROVIDER")

        super().__init__(**data)
        
        # 验证 provider
        if not self.provider:
            raise ValueError("LLM_PROVIDER environment variable must be set in .env file (e.g., 'openai', 'moonshot').")
        
        # 验证 API key
        if self.provider == "moonshot" and not self.moonshot_api_key:
             self.moonshot_api_key = os.getenv("MOONSHOT_API_KEY")
             if not self.moonshot_api_key:
                raise ValueError("MOONSHOT_API_KEY is not set in .env file for the moonshot provider.")
        elif self.provider == "openai" and not self.openai_api_key:
            self.openai_api_key = os.getenv("OPENAI_API_KEY")
            if not self.openai_api_key:
                raise ValueError("OPENAI_API_KEY is not set in .env file for the openai provider.")
        elif self.provider == "anthropic" and not self.anthropic_api_key:
            self.anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
            if not self.anthropic_api_key:
                raise ValueError("ANTHROPIC_API_KEY is not set in .env file for the anthropic provider.")

    def get_client_config(self) -> Dict[str, Any]:
        """获取客户端配置"""
        config = {
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "timeout": self.timeout,
        }
        
        if self.provider == "openai":
            if not self.openai_api_key:
                raise ValueError("OpenAI API key is required")
            config.update({
                "api_key": self.openai_api_key,
                "base_url": self.openai_base_url,
            })
        elif self.provider == "anthropic":
            if not self.anthropic_api_key:
                raise ValueError("Anthropic API key is required")
            config.update({
                "api_key": self.anthropic_api_key,
            })
        elif self.provider == "moonshot":
            if not self.moonshot_api_key:
                raise ValueError("Moonshot API key is required")
            config.update({
                "api_key": self.moonshot_api_key,
                "base_url": self.moonshot_base_url,
            })
        
        return config
    
    @classmethod
    def for_kimi_k2_0711_preview(cls, **kwargs) -> "LLMConfig":
        """创建kimi-k2-0711-preview配置"""
        return cls(
            provider="moonshot",
            model="kimi-k2-0711-preview",
            **kwargs
        )
