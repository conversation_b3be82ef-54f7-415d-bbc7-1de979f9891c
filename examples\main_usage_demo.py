#!/usr/bin/env python3
"""
main.py 使用演示 - 展示重构后的主入口功能

这个示例展示了如何：
1. 程序化使用系统管理器
2. 异步初始化和健康检查
3. 与CLI集成的最佳实践
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from main import (
    SystemManager, 
    create_system_manager, 
    main_async,
    LangGraphSystem,
    TaskType,
    LLMConfig
)


async def demo_system_manager():
    """演示系统管理器的使用"""
    print("🔧 系统管理器演示")
    print("=" * 30)
    
    # 创建系统管理器
    manager = create_system_manager()
    print(f"✅ 系统管理器创建: {type(manager).__name__}")
    
    # 检查初始状态
    print(f"🔍 初始化状态: {manager.is_initialized()}")
    
    # 初始化系统
    print("🚀 正在初始化系统...")
    success = await manager.initialize()
    print(f"✅ 初始化结果: {'成功' if success else '失败'}")
    
    if success:
        # 健康检查
        print("🏥 执行健康检查...")
        health = await manager.health_check()
        print(f"📊 系统状态: {health['status']}")
        print(f"🤖 智能体数量: {health.get('agents', 0)}")
        print(f"⚡ 系统能力: {health.get('capabilities', 0)}")
        
        # 优雅关闭
        print("🔄 关闭系统...")
        await manager.shutdown()
        print(f"🔍 关闭后状态: {manager.is_initialized()}")
    
    print()


async def demo_core_system():
    """演示核心系统的直接使用"""
    print("⚡ 核心系统演示")
    print("=" * 30)
    
    try:
        # 创建LLM配置
        llm_config = LLMConfig(provider="moonshot")
        print(f"🔧 LLM配置: {llm_config.provider}")
        
        # 创建核心系统
        system = LangGraphSystem(llm_config)
        print("✅ 核心系统创建成功")
        
        # 获取系统信息
        info = system.get_system_info()
        print(f"📋 系统名称: {info['name']}")
        print(f"📦 版本: {info['version']}")
        print(f"🤖 可用智能体: {len(info['available_agents'])}")
        print(f"⚡ 系统能力: {len(info['capabilities'])}")
        
        # 列出智能体
        print("\n🤖 智能体列表:")
        for agent_id in info['available_agents']:
            print(f"  • {agent_id}")
        
        # 列出能力
        print("\n⚡ 系统能力:")
        for capability in info['capabilities']:
            print(f"  • {capability}")
            
    except Exception as e:
        print(f"❌ 核心系统演示失败: {e}")
    
    print()


async def demo_project_creation():
    """演示项目创建功能"""
    print("🚀 项目创建演示")
    print("=" * 30)
    
    try:
        # 使用main_async获取系统管理器
        manager = await main_async()
        
        if manager and manager.is_initialized():
            # 创建测试项目
            project_result = await manager.core_system.create_project(
                project_name="DemoProject",
                task_type=TaskType.DEVELOPMENT,
                description="这是一个演示项目，用于测试系统功能",
                requirements={
                    "language": "Python",
                    "framework": "FastAPI",
                    "features": ["REST API", "数据库集成", "用户认证"]
                }
            )
            
            print(f"✅ 项目创建结果: {project_result.get('status', 'unknown')}")
            if 'project_id' in project_result:
                print(f"🆔 项目ID: {project_result['project_id']}")
            
            # 关闭系统
            await manager.shutdown()
        else:
            print("❌ 系统初始化失败，无法创建项目")
            
    except Exception as e:
        print(f"❌ 项目创建演示失败: {e}")
    
    print()


def demo_cli_integration():
    """演示CLI集成"""
    print("⌨️  CLI集成演示")
    print("=" * 30)
    
    print("📖 CLI使用示例:")
    print("  python src/main.py status                    # 系统状态")
    print("  python src/main.py agents list              # 智能体列表")
    print("  python src/main.py project create           # 创建项目")
    print("  python src/main.py interactive              # 交互模式")
    print("  python src/main.py workflow templates       # 工作流模板")
    print()
    
    print("🌐 Web界面启动:")
    print("  python run_web_ui.py                        # 启动Web界面")
    print()
    
    print("📚 文档和示例:")
    print("  docs/CLI_QUICK_START.md                     # 快速开始")
    print("  docs/CLI_USAGE.md                           # 详细使用")
    print("  examples/                                    # 使用示例")
    print()


async def main():
    """主演示函数"""
    print("🤖 LangGraph多智能体协作平台 v0.3.0")
    print("main.py 重构功能演示")
    print("=" * 60)
    print()
    
    try:
        # 演示各种功能
        await demo_system_manager()
        await demo_core_system()
        await demo_project_creation()
        demo_cli_integration()
        
        print("=" * 60)
        print("✅ 所有演示完成!")
        print()
        print("💡 提示:")
        print("  • 使用 python src/main.py 查看CLI帮助")
        print("  • 使用 python src/main.py interactive 启动交互模式")
        print("  • 查看 docs/ 目录获取详细文档")
        
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
