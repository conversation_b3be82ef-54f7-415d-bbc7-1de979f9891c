#!/usr/bin/env python3
"""
LangGraph多智能体系统 v0.3 - 智能体模块
包含所有智能体相关的类和接口 (已重构)
"""

from .base_v3 import SpecialistAgent, AgentCapability, CollaborationMode
from .architect_agent import ArchitectAgent
from .coder_agent import CoderAgent
from .devops_agent import DevOpsAgent
from .documentation_agent import DocumentationAgent
from .product_manager_agent import ProductManagerAgent
from .qa_agent import QAAgent
from .researcher_agent import ResearcherAgent


__all__ = [
    # v3 Base Classes
    "SpecialistAgent",
    "AgentCapability",
    "CollaborationMode",
    
    # Specialist Agents
    "ArchitectAgent",
    "ProductManagerAgent",
    "DevOpsAgent",
    "QAAgent",
    "DocumentationAgent",
    "CoderAgent",
    "ResearcherAgent",
]

__version__ = "0.3.1"
