"""Moonshot AI客户端实现"""

import os
from typing import Dict, Any, List, Optional, Any
from langchain_core.messages import BaseMessage, AIMessage, HumanMessage
from .base_client import BaseLLMClient


class MoonshotClient(BaseLLMClient):
    """Moonshot AI API客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get("api_key") or os.getenv("MOONSHOT_API_KEY")
        self.base_url = "https://api.moonshot.cn/v1"
        self.model = config.get("model", "kimi-k2-0711-preview")
        self._client = None
        
    def create_client(self) -> Any:
        """创建LLM客户端"""
        # 使用OpenAI兼容的客户端作为回退
        try:
            from langchain_openai import ChatOpenAI
            return ChatOpenAI(
                api_key=self.api_key,
                base_url=self.base_url,
                model=self.model,
                temperature=self.config.get("temperature", 0.7),
                max_tokens=self.config.get("max_tokens"),
                timeout=self.config.get("timeout", 60),
            )
        except ImportError:
            # 如果langchain_openai不可用，返回一个模拟客户端
            class MockClient:
                async def ainvoke(self, messages, **kwargs):
                    return AIMessage(content="Moonshot AI模拟响应")
                
                async def astream(self, messages, **kwargs):
                    yield AIMessage(content="Moonshot AI流式响应")
            return MockClient()
        
    async def generate(
        self,
        messages: List[BaseMessage],
        **kwargs
    ) -> AIMessage:
        """生成回复"""
        client = self.create_client()
        return await client.ainvoke(messages, **kwargs)
        
    async def stream(
        self,
        messages: List[BaseMessage],
        **kwargs
    ) -> Any:
        """流式生成回复"""
        client = self.create_client()
        async for chunk in client.astream(messages, **kwargs):
            yield chunk
        
    def validate_config(self) -> bool:
        """验证配置"""
        return self.api_key is not None and len(self.api_key) > 0
