# CLI 快速开始指南

## 5分钟快速上手

### 1. 安装和配置

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量（可选）
export OPENAI_API_KEY="your-api-key-here"
```

### 2. 启动交互式界面

```bash
python -m src.langgraph_system.cli.cli_main interactive
```

### 3. 创建第一个项目

```
langgraph> create
项目名称: HelloWorld
任务类型 [development]: development  
项目描述: 创建一个简单的Hello World应用
✅ 项目 'HelloWorld' 创建成功!
```

### 4. 运行项目

```
langgraph> run
🚀 开始执行项目...
✅ 项目执行完成!
```

### 5. 查看结果

```
langgraph> status
langgraph> messages
```

## 常用命令速查

| 命令 | 功能 | 示例 |
|------|------|------|
| `create` | 创建项目 | `create MyApp development` |
| `run` | 执行项目 | `run` |
| `status` | 查看状态 | `status` |
| `agents` | 智能体信息 | `agents` |
| `monitor` | 实时监控 | `monitor` |
| `save` | 保存项目 | `save project.json` |
| `help` | 帮助信息 | `help` |

## 项目类型说明

- **development** - 开发新功能
- **research** - 技术调研
- **testing** - 测试验证
- **debugging** - 问题调试
- **documentation** - 文档编写

## 下一步

- 阅读完整的 [CLI使用指南](../CLI_USAGE.md)
- 了解 [智能体系统架构](SUPERVISOR_GUIDE.md)
- 查看 [示例项目](../examples/)