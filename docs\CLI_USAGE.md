# LangGraph多智能体系统 - 命令行使用指南

## 概述

LangGraph多智能体系统提供了强大的命令行界面，支持项目管理、智能体协作和实时监控。本指南将详细介绍如何使用CLI工具来管理和执行多智能体项目。

## 安装和设置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 环境配置

设置环境变量（可选）：

```bash
# LLM配置
export LANGGRAPH_LLM_PROVIDER=openai
export LANGGRAPH_LLM_MODEL=gpt-4o-mini
export LANGGRAPH_LLM_TEMPERATURE=0.7

# 基本配置
export LANGGRAPH_VERBOSE=true
export LANGGRAPH_LOG_LEVEL=INFO
export LANGGRAPH_PROJECT_DIR=./projects
```

### 3. 配置文件

创建配置文件 `~/.langgraph/config.json`：

```json
{
  "verbose": false,
  "log_level": "INFO",
  "output_format": "table",
  "default_project_dir": "./projects",
  "auto_save": true,
  "backup_enabled": true,
  "max_messages": 50,
  "show_timestamps": true,
  "color_output": true,
  "confirm_actions": true,
  "auto_continue": false,
  "timeout": 300,
  "llm_provider": "openai",
  "llm_model": "gpt-4o-mini",
  "llm_temperature": 0.7
}
```

## 基本使用

### 启动CLI

```bash
# 直接启动
python -m src.main

# 或者使用交互模式
python -m src.langgraph_system.cli.cli_main interactive
```

### 查看帮助

```bash
python -m src.langgraph_system.cli.cli_main --help
```

## 命令详解

### 1. 项目管理

#### 创建项目

```bash
# 基本创建
python -m src.langgraph_system.cli.cli_main create \
  --project-name "MyWebApp" \
  --task-type development \
  --description "创建一个Flask Web应用"

# 交互式创建
python -m src.langgraph_system.cli.cli_main create --interactive
```

支持的任务类型：
- `architecture` - 架构设计
- `development` - 开发任务
- `review` - 代码审查
- `testing` - 测试任务
- `debugging` - 调试任务
- `documentation` - 文档编写
- `deployment` - 部署任务
- `research` - 研究分析
- `coding` - 编码实现

#### 查看项目状态

```bash
python -m src.langgraph_system.cli.cli_main status
```

#### 保存和加载项目

```bash
# 保存项目状态
python -m src.langgraph_system.cli.cli_main export --output my_project.json

# 加载项目状态
python -m src.langgraph_system.cli.cli_main resume --project-file my_project.json
```

### 2. 智能体管理

#### 查看智能体信息

```bash
python -m src.langgraph_system.cli.cli_main agents
```

#### 查看任务执行计划

```bash
python -m src.langgraph_system.cli.cli_main plan --task-type development
```

### 3. 系统信息

#### 查看系统状态

```bash
python -m src.langgraph_system.cli.cli_main status
```

## 交互式模式

### 启动交互式界面

```bash
python -m src.langgraph_system.cli.cli_main interactive
```

### 交互式命令

在交互模式下，可以使用以下命令：

| 命令 | 描述 | 示例 |
|------|------|------|
| `help` | 显示帮助信息 | `help` |
| `create` | 创建新项目 | `create MyProject development` |
| `status` | 显示当前项目状态 | `status` |
| `agents` | 显示智能体信息 | `agents` |
| `run` | 运行当前项目 | `run` |
| `monitor` | 实时监控项目执行 | `monitor` |
| `messages` | 显示消息历史 | `messages` |
| `plan` | 显示任务执行计划 | `plan development` |
| `save` | 保存项目状态 | `save project.json` |
| `load` | 加载项目状态 | `load project.json` |
| `clear` | 清屏 | `clear` |
| `exit/quit` | 退出程序 | `exit` |

### 交互式项目创建示例

```
langgraph> create
项目名称: WebCrawler
任务类型 [development]: development
项目描述: 创建一个网页爬虫系统
✅ 项目 'WebCrawler' 创建成功!

langgraph> run
🚀 开始执行项目...
✅ 项目执行完成!
```

## 实时监控

### 启动监控模式

```bash
langgraph> monitor
```

监控模式提供：
- 实时项目状态更新
- 智能体状态显示
- 消息流监控
- 执行进度跟踪

按 `Ctrl+C` 退出监控模式。

## 配置管理

### 查看当前配置

```bash
python -c "
from src.langgraph_system.cli.config import get_config_manager
config = get_config_manager()
print(config.config.to_dict())
"
```

### 配置文件位置

系统会按以下顺序查找配置文件：
1. `~/.langgraph/config.json`
2. `~/.langgraph/config.yaml`
3. `./langgraph.config.json`
4. `./langgraph.config.yaml`
5. `./.langgraph.json`
6. `./.langgraph.yaml`

### 配置文件管理

```python
# 在交互模式下
from src.langgraph_system.cli.config import ProfileManager, get_config_manager

config_manager = get_config_manager()
profile_manager = ProfileManager(config_manager)

# 保存配置文件
profile_manager.save_profile("development")

# 加载配置文件
profile_manager.load_profile("development")

# 列出所有配置文件
profiles = profile_manager.list_profiles()
```

## 高级功能

### 1. 批处理模式

创建批处理脚本：

```bash
#!/bin/bash
# batch_process.sh

# 创建项目
python -m src.langgraph_system.cli.cli_main create \
  --project-name "BatchProject" \
  --task-type development \
  --description "批处理项目"

# 执行项目
python -m src.langgraph_system.cli.cli_main run

# 保存结果
python -m src.langgraph_system.cli.cli_main export --output batch_result.json
```

### 2. 自定义智能体

系统会自动加载 `src/langgraph_system/agents/` 目录下的自定义智能体。

创建自定义智能体：

```python
# src/langgraph_system/agents/custom_agent.py
from .base_agent import LangGraphAgentAdapter

class CustomAgent(LangGraphAgentAdapter):
    def __init__(self):
        super().__init__()
        self.agent_name = "custom"
        self.capabilities = {
            "description": "自定义智能体功能"
        }
    
    async def process(self, state, context):
        # 实现自定义逻辑
        return {"status": "completed", "message": "自定义任务完成"}
```

### 3. 工作流自定义

可以通过修改 [`ProjectWorkflow`](src/langgraph_system/graphs/project_workflow.py:17) 来自定义工作流程。

## 故障排除

### 常见问题

1. **模块导入错误**
   ```bash
   # 确保在项目根目录运行
   cd /path/to/langgraph-multi-agent
   python -m src.main
   ```

2. **LLM配置错误**
   ```bash
   # 检查API密钥
   echo $OPENAI_API_KEY
   
   # 或在配置文件中设置
   ```

3. **依赖缺失**
   ```bash
   pip install -r requirements.txt
   ```

4. **权限问题**
   ```bash
   # 确保有写入权限
   chmod +w ~/.langgraph/
   ```

### 调试模式

启用详细输出：

```bash
python -m src.langgraph_system.cli.cli_main --verbose create --interactive
```

或设置环境变量：

```bash
export LANGGRAPH_VERBOSE=true
export LANGGRAPH_LOG_LEVEL=DEBUG
```

### 日志查看

系统日志位置：
- 应用日志：控制台输出
- 项目状态：保存在项目文件中
- 配置日志：`~/.langgraph/logs/`

## 示例工作流

### 完整的项目开发流程

```bash
# 1. 启动交互模式
python -m src.langgraph_system.cli.cli_main interactive

# 2. 创建项目
langgraph> create
项目名称: TodoApp
任务类型: development
项目描述: 创建一个待办事项应用

# 3. 查看执行计划
langgraph> plan development

# 4. 运行项目
langgraph> run

# 5. 监控执行
langgraph> monitor

# 6. 查看消息历史
langgraph> messages

# 7. 保存项目
langgraph> save todo_app_final.json

# 8. 退出
langgraph> exit
```

### Web应用开发示例

```bash
# 创建Web应用项目
python -m src.langgraph_system.cli.cli_main create \
  --project-name "FlaskBlog" \
  --task-type development \
  --description "创建一个Flask博客系统，包含用户认证、文章管理和评论功能" \
  --interactive

# 在交互模式中监控执行
langgraph> monitor
```

## API参考

### 主要类和方法

- [`InteractiveCLI`](src/langgraph_system/cli/interactive.py:25) - 交互式界面
- [`ProjectCommands`](src/langgraph_system/cli/commands.py:13) - 项目命令
- [`AgentCommands`](src/langgraph_system/cli/commands.py:42) - 智能体命令
- [`ConfigManager`](src/langgraph_system/cli/config.py:35) - 配置管理

### 配置选项

详见 [`CLIConfig`](src/langgraph_system/cli/config.py:12) 类定义。

## 贡献指南

欢迎贡献代码和改进建议！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 支持

如有问题，请：
1. 查看本文档
2. 检查 GitHub Issues
3. 提交新的 Issue

---

*最后更新: 2025-01-23*
