#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为hlowapp项目创建实际文件
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def create_hlowapp_project():
    """创建hlowapp项目文件"""
    from langgraph_system.tools.project_generator import project_generator
    
    # 创建项目目录结构
    project_name = "hlowapp"
    workspace_dir = Path("workspace")
    project_dir = workspace_dir / project_name
    project_dir.mkdir(exist_ok=True)
    
    # 创建Python Hello World项目文件
    files = {
        "README.md": f"""# {project_name}

一个简单的Python Hello World程序

## 描述

这是一个基础的Python程序，演示了基本的Python语法和功能。

## 文件说明

- `main.py` - 主程序文件
- `hello.py` - Hello World模块
- `utils.py` - 工具函数
- `tests/` - 测试文件

## 运行方法

```bash
# 运行主程序
python main.py

# 运行Hello World模块
python hello.py

# 运行测试
python -m pytest tests/
```

## 项目结构

```
{project_name}/
├── main.py          # 主程序
├── hello.py         # Hello World模块
├── utils.py         # 工具函数
├── requirements.txt # 依赖文件
├── tests/          # 测试目录
│   └── test_hello.py
└── README.md       # 项目说明
```

---

*项目创建时间: 2025-07-26*
""",
        
        "main.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
hlowapp - Python Hello World 主程序
"""

from hello import say_hello, say_hello_to
from utils import get_current_time, format_message

def main():
    """主函数"""
    print("=" * 50)
    print("🐍 Python Hello World 程序")
    print("=" * 50)
    
    # 基本的Hello World
    say_hello()
    
    # 个性化问候
    name = input("\\n请输入您的姓名: ")
    if name.strip():
        say_hello_to(name)
    
    # 显示当前时间
    current_time = get_current_time()
    time_message = format_message(f"当前时间: {current_time}")
    print(f"\\n{time_message}")
    
    # 简单的计算演示
    print("\\n🧮 简单计算演示:")
    try:
        num1 = float(input("请输入第一个数字: "))
        num2 = float(input("请输入第二个数字: "))
        
        result = num1 + num2
        print(f"结果: {num1} + {num2} = {result}")
        
    except ValueError:
        print("❌ 请输入有效的数字!")
    
    print("\\n👋 程序结束，谢谢使用!")

if __name__ == "__main__":
    main()
''',
        
        "hello.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hello World 模块
"""

def say_hello():
    """基本的Hello World函数"""
    print("🌍 Hello, World!")
    print("你好，世界！")

def say_hello_to(name: str):
    """个性化问候函数"""
    if not name or not name.strip():
        print("❌ 姓名不能为空!")
        return
    
    name = name.strip()
    print(f"\\n👋 Hello, {name}!")
    print(f"你好，{name}！")
    print(f"欢迎使用Python程序！")

def say_goodbye(name: str = None):
    """告别函数"""
    if name:
        print(f"👋 Goodbye, {name}!")
        print(f"再见，{name}！")
    else:
        print("👋 Goodbye!")
        print("再见！")

if __name__ == "__main__":
    # 直接运行此模块时的测试
    print("🧪 测试Hello模块:")
    say_hello()
    say_hello_to("Python开发者")
    say_goodbye("朋友")
''',
        
        "utils.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
"""

import datetime
from typing import Union

def get_current_time() -> str:
    """获取当前时间"""
    now = datetime.datetime.now()
    return now.strftime("%Y-%m-%d %H:%M:%S")

def format_message(message: str, width: int = 50) -> str:
    """格式化消息"""
    if len(message) >= width:
        return message
    
    padding = (width - len(message)) // 2
    return " " * padding + message

def calculate_sum(numbers: list) -> Union[int, float]:
    """计算数字列表的和"""
    if not numbers:
        return 0
    
    try:
        return sum(numbers)
    except (TypeError, ValueError):
        raise ValueError("列表中包含非数字元素")

def is_even(number: int) -> bool:
    """检查数字是否为偶数"""
    return number % 2 == 0

def fibonacci(n: int) -> list:
    """生成斐波那契数列"""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    fib = [0, 1]
    for i in range(2, n):
        fib.append(fib[i-1] + fib[i-2])
    
    return fib

if __name__ == "__main__":
    # 测试工具函数
    print("🧪 测试工具函数:")
    print(f"当前时间: {get_current_time()}")
    print(f"格式化消息: '{format_message('测试消息')}'")
    print(f"数字和: {calculate_sum([1, 2, 3, 4, 5])}")
    print(f"6是偶数: {is_even(6)}")
    print(f"斐波那契数列(10): {fibonacci(10)}")
''',
        
        "requirements.txt": '''# hlowapp Python Hello World 项目依赖

# 测试框架
pytest>=7.0.0

# 代码格式化
black>=22.0.0

# 代码检查
flake8>=5.0.0

# 类型检查
mypy>=1.0.0
''',
        
        "tests/test_hello.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hello模块测试用例
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from hello import say_hello, say_hello_to, say_goodbye
from utils import get_current_time, format_message, calculate_sum, is_even, fibonacci

class TestHello:
    """Hello模块测试类"""
    
    def test_say_hello(self, capsys):
        """测试基本问候函数"""
        say_hello()
        captured = capsys.readouterr()
        assert "Hello, World!" in captured.out
        assert "你好，世界！" in captured.out
    
    def test_say_hello_to(self, capsys):
        """测试个性化问候函数"""
        say_hello_to("测试用户")
        captured = capsys.readouterr()
        assert "Hello, 测试用户!" in captured.out
        assert "你好，测试用户！" in captured.out
    
    def test_say_hello_to_empty_name(self, capsys):
        """测试空姓名处理"""
        say_hello_to("")
        captured = capsys.readouterr()
        assert "姓名不能为空" in captured.out

class TestUtils:
    """工具函数测试类"""
    
    def test_get_current_time(self):
        """测试获取当前时间"""
        time_str = get_current_time()
        assert isinstance(time_str, str)
        assert len(time_str) > 0
    
    def test_format_message(self):
        """测试消息格式化"""
        result = format_message("测试", 10)
        assert len(result) <= 10
        assert "测试" in result
    
    def test_calculate_sum(self):
        """测试数字求和"""
        assert calculate_sum([1, 2, 3]) == 6
        assert calculate_sum([]) == 0
        assert calculate_sum([1.5, 2.5]) == 4.0
    
    def test_calculate_sum_invalid(self):
        """测试无效输入"""
        with pytest.raises(ValueError):
            calculate_sum([1, "invalid", 3])
    
    def test_is_even(self):
        """测试偶数判断"""
        assert is_even(2) == True
        assert is_even(3) == False
        assert is_even(0) == True
    
    def test_fibonacci(self):
        """测试斐波那契数列"""
        assert fibonacci(0) == []
        assert fibonacci(1) == [0]
        assert fibonacci(2) == [0, 1]
        assert fibonacci(5) == [0, 1, 1, 2, 3]

if __name__ == "__main__":
    pytest.main([__file__])
''',
        
        ".gitignore": '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Testing
.coverage
.pytest_cache/
htmlcov/

# Logs
*.log
''',
    }
    
    # 创建文件
    result = project_generator.create_multiple_files(project_name, files)
    
    return result

def main():
    """主函数"""
    print("🚀 为hlowapp项目创建文件...")
    
    # 切换到项目根目录
    os.chdir(Path(__file__).parent.parent)
    
    try:
        result = create_hlowapp_project()
        
        print(f"✅ hlowapp项目文件创建完成!")
        print(f"📊 文件统计: {result['success_count']}/{result['total_files']}")
        print(f"📁 项目位置: workspace/hlowapp/")
        
        print(f"\n🚀 使用方法:")
        print(f"1. cd workspace/hlowapp")
        print(f"2. python main.py")
        print(f"3. python -m pytest tests/")
        
        return 0
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
