{"project_name": "SmartTodoManager", "test_summary": {"total": 6, "passed": 4, "failed": 2, "success_rate": "66.7%"}, "duration": "0:00:05.639741", "timestamp": "2025-07-26T07:19:39.974968", "test_results": [{"test_name": "系统初始化", "success": true, "message": "系统初始化成功", "details": {"状态": "healthy", "模式": "lightweight", "版本": "0.2.0"}, "timestamp": "2025-07-26T07:19:34.339961"}, {"test_name": "项目创建", "success": true, "message": "项目创建成功，可使用CLI执行完整工作流", "details": {"项目ID": "395b78d5-6505-40b6-bd71-2eb71b5832b9", "任务类型": "development", "状态": "created"}, "timestamp": "2025-07-26T07:19:34.340442"}, {"test_name": "CLI状态命令", "success": false, "message": "CLI命令失败: \\u274c 系统初始化失败: 'gbk' codec can't encode character '\\u2705' in position 0: illegal multibyte sequence\n", "details": null, "timestamp": "2025-07-26T07:19:37.166790"}, {"test_name": "CLI智能体列表", "success": false, "message": "智能体列表命令失败: \\u274c 系统初始化失败: 'gbk' codec can't encode character '\\u2705' in position 0: illegal multibyte sequence\n", "details": null, "timestamp": "2025-07-26T07:19:39.964697"}, {"test_name": "工作空间创建", "success": true, "message": "工作空间创建成功", "details": {"目录": "7/7", "文件": "4/4", "路径": "E:\\langgraph-multi-agent\\workspace\\SmartTodoManager"}, "timestamp": "2025-07-26T07:19:39.973592"}, {"test_name": "代码生成", "success": true, "message": "代码生成完成", "details": {"后端文件": "main.py", "前端文件": "index.html", "测试文件": "test_main.py", "文档文件": "README.md", "总文件数": "4/4"}, "timestamp": "2025-07-26T07:19:39.974594"}]}