{"project_name": "SmartTodoManager", "test_summary": {"total": 6, "passed": 6, "failed": 0, "success_rate": "100.0%"}, "duration": "0:00:05.381441", "timestamp": "2025-07-26T07:35:30.890087", "test_results": [{"test_name": "系统初始化", "success": true, "message": "系统初始化成功", "details": {"状态": "healthy", "模式": "lightweight", "版本": "0.2.0"}, "timestamp": "2025-07-26T07:35:25.512520"}, {"test_name": "项目创建", "success": true, "message": "项目创建成功，可使用CLI执行完整工作流", "details": {"项目ID": "94b6f1a1-5dd3-4f22-ac20-44e7ca05f4d7", "任务类型": "development", "状态": "created"}, "timestamp": "2025-07-26T07:35:25.512698"}, {"test_name": "CLI状态命令", "success": true, "message": "CLI状态命令执行成功", "details": {"输出长度": 681}, "timestamp": "2025-07-26T07:35:28.202510"}, {"test_name": "CLI智能体列表", "success": true, "message": "找到 5/5 个智能体", "details": {"找到的智能体": ["architect", "product_manager", "coder", "qa_engineer", "devops"]}, "timestamp": "2025-07-26T07:35:30.888212"}, {"test_name": "工作空间创建", "success": true, "message": "工作空间创建成功", "details": {"目录": "7/7", "文件": "4/4", "路径": "e:\\langgraph-multi-agent\\workspace\\SmartTodoManager"}, "timestamp": "2025-07-26T07:35:30.889362"}, {"test_name": "代码生成", "success": true, "message": "代码生成完成", "details": {"后端文件": "main.py", "前端文件": "index.html", "测试文件": "test_main.py", "文档文件": "README.md", "总文件数": "4/4"}, "timestamp": "2025-07-26T07:35:30.890060"}]}