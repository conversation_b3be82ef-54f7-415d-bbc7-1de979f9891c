#!/usr/bin/env python3
"""
Redis安装和配置脚本
支持多种安装方式：Docker、本地安装、云服务配置
"""

import os
import sys
import subprocess
import platform
import json
from pathlib import Path
from typing import Dict, Any, Optional


class RedisSetup:
    """Redis安装配置器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.project_root = Path(__file__).parent.parent
        
    def check_docker(self) -> bool:
        """检查Docker是否可用"""
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def check_redis_local(self) -> bool:
        """检查本地Redis是否已安装"""
        try:
            result = subprocess.run(['redis-server', '--version'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def install_redis_docker(self) -> bool:
        """使用Docker安装Redis"""
        print("🐳 使用Docker安装Redis...")
        
        try:
            # 停止并删除现有容器
            subprocess.run(['docker', 'stop', 'langgraph-redis'], 
                         capture_output=True)
            subprocess.run(['docker', 'rm', 'langgraph-redis'], 
                         capture_output=True)
            
            # 启动新的Redis容器
            cmd = [
                'docker', 'run', '-d',
                '--name', 'langgraph-redis',
                '-p', '6379:6379',
                '--restart', 'unless-stopped',
                'redis:7-alpine',
                'redis-server', '--appendonly', 'yes'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Docker Redis容器启动成功")
                print("   容器名: langgraph-redis")
                print("   端口: 6379")
                return True
            else:
                print(f"❌ Docker Redis启动失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Docker Redis安装失败: {e}")
            return False
    
    def install_redis_local(self) -> bool:
        """本地安装Redis"""
        print(f"🔧 在{self.system}系统上安装Redis...")
        
        try:
            if self.system == "linux":
                # Ubuntu/Debian
                if self._command_exists("apt"):
                    subprocess.run(['sudo', 'apt', 'update'], check=True)
                    subprocess.run(['sudo', 'apt', 'install', '-y', 'redis-server'], check=True)
                # CentOS/RHEL
                elif self._command_exists("yum"):
                    subprocess.run(['sudo', 'yum', 'install', '-y', 'redis'], check=True)
                # Arch Linux
                elif self._command_exists("pacman"):
                    subprocess.run(['sudo', 'pacman', '-S', '--noconfirm', 'redis'], check=True)
                else:
                    print("❌ 不支持的Linux发行版")
                    return False
                    
            elif self.system == "darwin":  # macOS
                if self._command_exists("brew"):
                    subprocess.run(['brew', 'install', 'redis'], check=True)
                else:
                    print("❌ 请先安装Homebrew: https://brew.sh/")
                    return False
                    
            elif self.system == "windows":
                print("❌ Windows不支持原生Redis，请使用Docker或WSL")
                return False
            
            print("✅ Redis本地安装成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Redis安装失败: {e}")
            return False
    
    def _command_exists(self, command: str) -> bool:
        """检查命令是否存在"""
        try:
            subprocess.run(['which', command], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def start_redis_service(self) -> bool:
        """启动Redis服务"""
        print("🚀 启动Redis服务...")
        
        try:
            if self.system == "linux":
                subprocess.run(['sudo', 'systemctl', 'start', 'redis'], check=True)
                subprocess.run(['sudo', 'systemctl', 'enable', 'redis'], check=True)
            elif self.system == "darwin":
                subprocess.run(['brew', 'services', 'start', 'redis'], check=True)
            
            print("✅ Redis服务启动成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Redis服务启动失败: {e}")
            return False
    
    def test_redis_connection(self) -> bool:
        """测试Redis连接"""
        print("🔍 测试Redis连接...")
        
        try:
            result = subprocess.run(['redis-cli', 'ping'], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0 and 'PONG' in result.stdout:
                print("✅ Redis连接测试成功")
                return True
            else:
                print(f"❌ Redis连接测试失败: {result.stderr}")
                return False
                
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
            print(f"❌ Redis连接测试失败: {e}")
            return False
    
    def create_env_config(self, mode: str = "local") -> None:
        """创建环境配置"""
        env_file = self.project_root / ".env"
        
        config_lines = [
            "# Redis配置",
            f"REDIS_MODE={mode}",
            "REDIS_HOST=localhost",
            "REDIS_PORT=6379",
            "REDIS_DB=0",
            ""
        ]
        
        if env_file.exists():
            # 读取现有配置
            with open(env_file, 'r', encoding='utf-8') as f:
                existing_lines = f.readlines()
            
            # 更新Redis配置
            new_lines = []
            redis_section_found = False
            
            for line in existing_lines:
                if line.strip().startswith("REDIS_"):
                    if not redis_section_found:
                        new_lines.extend([l + "\n" for l in config_lines])
                        redis_section_found = True
                    # 跳过现有的Redis配置行
                    continue
                else:
                    new_lines.append(line)
            
            if not redis_section_found:
                new_lines.extend([l + "\n" for l in config_lines])
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
        else:
            # 创建新的配置文件
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write("\n".join(config_lines))
        
        print(f"✅ 环境配置已更新: {env_file}")
    
    def setup_interactive(self) -> None:
        """交互式安装"""
        print("🤖 LangGraph Redis配置向导")
        print("=" * 40)
        
        print("\n选择Redis安装方式:")
        print("1. 内存模拟 (推荐用于开发/测试)")
        print("2. Docker容器 (推荐用于本地开发)")
        print("3. 本地安装")
        print("4. 跳过Redis配置")
        
        while True:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == "1":
                print("✅ 选择内存模拟模式")
                self.create_env_config("memory")
                break
                
            elif choice == "2":
                if not self.check_docker():
                    print("❌ Docker未安装或不可用")
                    print("   请先安装Docker: https://docs.docker.com/get-docker/")
                    continue
                
                if self.install_redis_docker():
                    self.create_env_config("docker")
                break
                
            elif choice == "3":
                if self.install_redis_local():
                    if self.start_redis_service():
                        if self.test_redis_connection():
                            self.create_env_config("local")
                break
                
            elif choice == "4":
                print("⏭️  跳过Redis配置")
                self.create_env_config("disabled")
                break
                
            else:
                print("❌ 无效选择，请重新输入")
        
        print("\n🎉 Redis配置完成！")
        print("\n📖 使用说明:")
        print("   • 内存模式: 无需额外配置，适合开发测试")
        print("   • Docker模式: 使用 docker start langgraph-redis 启动")
        print("   • 本地模式: 使用系统服务管理Redis")
        print("   • 禁用模式: 系统将仅使用内存缓存")


def main():
    """主函数"""
    setup = RedisSetup()
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == "docker":
            if setup.check_docker():
                setup.install_redis_docker()
                setup.create_env_config("docker")
            else:
                print("❌ Docker不可用")
                sys.exit(1)
                
        elif mode == "local":
            if setup.install_redis_local():
                setup.start_redis_service()
                setup.test_redis_connection()
                setup.create_env_config("local")
                
        elif mode == "memory":
            setup.create_env_config("memory")
            print("✅ 配置为内存模拟模式")
            
        elif mode == "test":
            setup.test_redis_connection()
            
        else:
            print("❌ 无效参数")
            print("用法: python setup_redis.py [docker|local|memory|test]")
            sys.exit(1)
    else:
        setup.setup_interactive()


if __name__ == "__main__":
    main()
