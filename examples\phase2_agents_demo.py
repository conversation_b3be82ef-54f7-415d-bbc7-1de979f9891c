#!/usr/bin/env python3
"""
第二阶段智能体系统演示
展示专业智能体的协作和功能
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any

# 导入智能体
from src.langgraph_system.agents import (
    ArchitectAgent, ProductManagerAgent, DevOpsAgent, 
    QAAgent, DocumentationAgent, CoderAgent
)
from src.langgraph_system.agents.base_agent import TaskRequest, Priority
from src.langgraph_system.agents.collaboration import AgentCollaborationManager
from src.langgraph_system.agents.communication import AgentCommunicationProtocol
from src.langgraph_system.agents.capability_assessment import AgentCapabilityAssessment
from src.langgraph_system.agents.learning import AgentLearningSystem


class Phase2AgentsDemo:
    """第二阶段智能体系统演示类"""
    
    def __init__(self):
        """初始化演示系统"""
        self.agents = {}
        self.collaboration_manager = AgentCollaborationManager()
        self.communication_protocol = AgentCommunicationProtocol()
        self.capability_assessment = AgentCapabilityAssessment()
        self.learning_system = AgentLearningSystem()
        
        print("🚀 第二阶段智能体系统演示初始化完成")
    
    async def initialize_agents(self):
        """初始化所有智能体"""
        print("\n📋 正在初始化智能体...")
        
        # 创建智能体实例
        agent_classes = {
            'architect': ArchitectAgent,
            'product_manager': ProductManagerAgent,
            'devops': DevOpsAgent,
            'qa': QAAgent,
            'documentation': DocumentationAgent,
            'coder': CoderAgent
        }
        
        for name, agent_class in agent_classes.items():
            print(f"  ⚡ 初始化 {name} 智能体...")
            agent = agent_class()
            await agent.initialize()
            self.agents[name] = agent
            
            # 注册到协作管理器
            await self.collaboration_manager.register_agent(agent)
            
            print(f"    ✅ {agent.name} 初始化完成 (ID: {agent.agent_id})")
        
        print(f"\n✅ 所有 {len(self.agents)} 个智能体初始化完成")
    
    async def demo_individual_agents(self):
        """演示各个智能体的独立功能"""
        print("\n" + "="*60)
        print("🎯 演示各智能体独立功能")
        print("="*60)
        
        # 1. 架构师智能体演示
        await self._demo_architect_agent()
        
        # 2. 产品经理智能体演示
        await self._demo_product_manager_agent()
        
        # 3. DevOps智能体演示
        await self._demo_devops_agent()
        
        # 4. QA智能体演示
        await self._demo_qa_agent()
        
        # 5. 文档智能体演示
        await self._demo_documentation_agent()
        
        # 6. 编程智能体演示
        await self._demo_coder_agent()
    
    async def _demo_architect_agent(self):
        """演示架构师智能体"""
        print("\n🏗️ 架构师智能体演示")
        print("-" * 40)
        
        architect = self.agents['architect']
        
        # 系统设计任务
        print("📐 执行系统设计任务...")
        design_task = TaskRequest(
            type="design_system",
            description="设计电商平台系统架构",
            parameters={
                "requirements": {
                    "name": "电商平台",
                    "type": "web_application",
                    "expected_users": 10000,
                    "features": ["用户管理", "商品管理", "订单处理", "支付系统"]
                }
            },
            priority=Priority.HIGH
        )
        
        result = await architect.process_task(design_task)
        print(f"  ✅ 系统设计完成: {result.result['system_name']}")
        print(f"  📊 组件数量: {result.result['components_count']}")
        
        # 技术选型任务
        print("\n🔧 执行技术选型任务...")
        tech_task = TaskRequest(
            type="select_technology",
            description="为电商平台选择数据库技术",
            parameters={
                "category": "database",
                "requirements": {
                    "data_volume": "large",
                    "consistency": "strong",
                    "scalability": "high"
                }
            },
            priority=Priority.HIGH
        )
        
        result = await architect.process_task(tech_task)
        print(f"  ✅ 推荐技术: {result.result['recommended_technology']}")
        print(f"  📈 评分: {result.result['score']}/100")
    
    async def _demo_product_manager_agent(self):
        """演示产品经理智能体"""
        print("\n📋 产品经理智能体演示")
        print("-" * 40)
        
        pm = self.agents['product_manager']
        
        # 需求分析任务
        print("📊 执行需求分析任务...")
        requirements = [
            {
                "id": "req_001",
                "title": "用户注册登录",
                "description": "用户可以注册账号并登录系统",
                "type": "functional",
                "business_impact": "high"
            },
            {
                "id": "req_002",
                "title": "商品浏览",
                "description": "用户可以浏览商品列表和详情",
                "type": "functional",
                "business_impact": "high"
            },
            {
                "id": "req_003",
                "title": "系统性能",
                "description": "系统响应时间不超过2秒",
                "type": "non_functional",
                "business_impact": "medium"
            }
        ]
        
        analysis_task = TaskRequest(
            type="analyze_requirements",
            description="分析电商平台需求",
            parameters={"requirements": requirements},
            priority=Priority.HIGH
        )
        
        result = await pm.process_task(analysis_task)
        print(f"  ✅ 需求分析完成: {result.result['total_requirements']} 个需求")
        print(f"  📈 需求分布: {result.result['requirements_by_type']}")
        
        # 用户故事创建任务
        print("\n📝 执行用户故事创建任务...")
        story_task = TaskRequest(
            type="create_user_stories",
            description="创建用户故事",
            parameters={"requirements": requirements[:2]},  # 只处理前两个需求
            priority=Priority.HIGH
        )
        
        result = await pm.process_task(story_task)
        print(f"  ✅ 用户故事创建完成: {result.result['total_stories']} 个故事")
    
    async def _demo_devops_agent(self):
        """演示DevOps智能体"""
        print("\n⚙️ DevOps智能体演示")
        print("-" * 40)
        
        devops = self.agents['devops']
        
        # CI/CD设置任务
        print("🔄 执行CI/CD设置任务...")
        cicd_task = TaskRequest(
            type="setup_cicd",
            description="设置CI/CD管道",
            parameters={
                "project": {
                    "name": "ecommerce-platform",
                    "type": "web_application",
                    "language": "python",
                    "framework": "django"
                },
                "platform": "jenkins",
                "stages": ["build", "test", "deploy"]
            },
            priority=Priority.HIGH
        )
        
        result = await devops.process_task(cicd_task)
        print(f"  ✅ CI/CD管道创建完成: {result.result['pipeline_id']}")
        print(f"  🔧 阶段数量: {result.result['stages_count']}")
        
        # 基础设施部署任务
        print("\n☁️ 执行基础设施部署任务...")
        infra_task = TaskRequest(
            type="deploy_infrastructure",
            description="部署基础设施",
            parameters={
                "infrastructure": {
                    "compute": [
                        {"name": "web-server", "instance_type": "t3.medium", "count": 2},
                        {"name": "db-server", "instance_type": "t3.large", "count": 1}
                    ],
                    "storage": [
                        {"name": "app-storage", "type": "ebs", "size": "100GB"}
                    ]
                },
                "provider": "aws",
                "region": "us-west-2"
            },
            priority=Priority.HIGH
        )
        
        result = await devops.process_task(infra_task)
        print(f"  ✅ 基础设施部署完成")
        print(f"  📦 部署资源: {len(result.result['deployed_resources'])} 个")
    
    async def _demo_qa_agent(self):
        """演示QA智能体"""
        print("\n🧪 QA智能体演示")
        print("-" * 40)
        
        qa = self.agents['qa']
        
        # 测试策略设计任务
        print("📋 执行测试策略设计任务...")
        strategy_task = TaskRequest(
            type="design_test_strategy",
            description="设计测试策略",
            parameters={
                "project_name": "电商平台",
                "requirements": {
                    "type": "web_application",
                    "complexity": "high",
                    "critical_features": ["支付", "用户数据", "订单处理"]
                }
            },
            priority=Priority.HIGH
        )
        
        result = await qa.process_task(strategy_task)
        print(f"  ✅ 测试策略设计完成: {result.result['strategy_id']}")
        print(f"  🎯 覆盖测试类型: {result.result['test_types_covered']} 种")
        
        # 测试用例创建任务
        print("\n📝 执行测试用例创建任务...")
        test_requirements = [
            {
                "id": "req_001",
                "title": "用户登录",
                "description": "用户可以使用邮箱和密码登录系统",
                "type": "functional"
            },
            {
                "id": "req_002",
                "title": "商品搜索",
                "description": "用户可以搜索商品",
                "type": "functional"
            }
        ]
        
        testcase_task = TaskRequest(
            type="create_test_cases",
            description="创建测试用例",
            parameters={"requirements": test_requirements},
            priority=Priority.HIGH
        )
        
        result = await qa.process_task(testcase_task)
        print(f"  ✅ 测试用例创建完成: {result.result['total_test_cases']} 个")
    
    async def _demo_documentation_agent(self):
        """演示文档智能体"""
        print("\n📚 文档智能体演示")
        print("-" * 40)
        
        doc = self.agents['documentation']
        
        # 用户指南生成任务
        print("📖 执行用户指南生成任务...")
        guide_task = TaskRequest(
            type="generate_documentation",
            description="生成用户指南",
            parameters={
                "document_type": "user_guide",
                "content_source": {
                    "title": "电商平台用户指南",
                    "product_name": "电商平台",
                    "version": "1.0.0",
                    "features": [
                        {"name": "用户注册", "description": "创建新用户账号"},
                        {"name": "商品浏览", "description": "浏览和搜索商品"},
                        {"name": "购物车", "description": "管理购物车商品"},
                        {"name": "订单管理", "description": "查看和管理订单"}
                    ]
                }
            },
            priority=Priority.HIGH
        )
        
        result = await doc.process_task(guide_task)
        print(f"  ✅ 用户指南生成完成: {result.result['document_id']}")
        print(f"  📄 字数统计: {result.result['word_count']} 字")
        
        # API文档生成任务
        print("\n🔌 执行API文档生成任务...")
        api_task = TaskRequest(
            type="generate_api_docs",
            description="生成API文档",
            parameters={
                "api_spec": {
                    "name": "电商平台API",
                    "version": "1.0.0",
                    "base_url": "https://api.ecommerce.com",
                    "endpoints": [
                        {
                            "path": "/users",
                            "method": "GET",
                            "description": "获取用户列表",
                            "parameters": ["page", "limit"]
                        },
                        {
                            "path": "/products",
                            "method": "GET",
                            "description": "获取商品列表",
                            "parameters": ["category", "search"]
                        },
                        {
                            "path": "/orders",
                            "method": "POST",
                            "description": "创建新订单",
                            "parameters": ["user_id", "items"]
                        }
                    ]
                }
            },
            priority=Priority.HIGH
        )
        
        result = await doc.process_task(api_task)
        print(f"  ✅ API文档生成完成: {result.result['api_doc_id']}")
        print(f"  🔗 接口数量: {result.result['endpoints_count']} 个")
    
    async def _demo_coder_agent(self):
        """演示编程智能体"""
        print("\n💻 编程智能体演示")
        print("-" * 40)
        
        coder = self.agents['coder']
        
        # 代码生成任务
        print("⚡ 执行代码生成任务...")
        code_task = TaskRequest(
            type="generate_code",
            description="生成用户认证模块",
            parameters={
                "requirements": "创建一个用户认证类，包含登录、注册、密码验证功能",
                "language": "python",
                "code_type": "class",
                "framework": "django"
            },
            priority=Priority.HIGH
        )
        
        result = await coder.process_task(code_task)
        print(f"  ✅ 代码生成完成: {result.result['code_snippet_id']}")
        print(f"  📝 代码行数: {len(result.result['generated_code'].split('\\n'))} 行")
        
        # 代码审查任务
        print("\n🔍 执行代码审查任务...")
        sample_code = """
def authenticate_user(username, password):
    if not username or not password:
        return False
    
    user = User.objects.get(username=username)
    if user and user.check_password(password):
        return True
    return False
"""
        
        review_task = TaskRequest(
            type="review_code",
            description="审查用户认证代码",
            parameters={
                "code_content": sample_code,
                "language": "python",
                "focus_areas": ["security", "error_handling", "performance"]
            },
            priority=Priority.HIGH
        )
        
        result = await coder.process_task(review_task)
        print(f"  ✅ 代码审查完成: {result.result['review_id']}")
        print(f"  ⭐ 总体评分: {result.result['overall_rating']}/10")
        print(f"  ⚠️ 发现问题: {len(result.result['issues'])} 个")
    
    async def demo_agent_collaboration(self):
        """演示智能体协作功能"""
        print("\n" + "="*60)
        print("🤝 演示智能体协作功能")
        print("="*60)
        
        # 创建软件开发工作流
        print("\n🔄 创建软件开发协作工作流...")
        workflow_id = await self.collaboration_manager.create_workflow(
            "software_development",
            {
                "project_name": "电商平台开发",
                "analyze_requirements": {
                    "requirements": [
                        {
                            "id": "req_001",
                            "title": "用户管理系统",
                            "description": "实现用户注册、登录、个人信息管理",
                            "type": "functional",
                            "priority": "high"
                        },
                        {
                            "id": "req_002",
                            "title": "商品管理系统",
                            "description": "实现商品展示、搜索、分类管理",
                            "type": "functional",
                            "priority": "high"
                        }
                    ]
                }
            }
        )
        
        print(f"  ✅ 工作流创建完成: {workflow_id}")
        
        # 执行工作流
        print("\n▶️ 执行协作工作流...")
        execution_result = await self.collaboration_manager.execute_workflow(workflow_id)
        
        print(f"  ✅ 工作流执行完成")
        print(f"  📊 执行状态: {execution_result['status']}")
        print(f"  ⏱️ 执行时间: {execution_result['execution_time']:.2f}秒")
        print(f"  🎯 完成步骤: {execution_result['completed_steps']}")
        
        # 显示工作流结果
        if execution_result['results']:
            print("\n📋 工作流执行结果:")
            for step, result in execution_result['results'].items():
                print(f"  • {step}: {result.get('summary', '完成')}")
    
    async def demo_communication_protocol(self):
        """演示通信协议功能"""
        print("\n" + "="*60)
        print("📡 演示智能体通信协议")
        print("="*60)
        
        # 创建通信通道
        print("\n📢 创建通信通道...")
        channel_id = await self.communication_protocol.create_channel({
            "name": "项目协作通道",
            "pattern": "publish_subscribe",
            "participants": [agent.agent_id for agent in self.agents.values()]
        })
        
        print(f"  ✅ 通道创建完成: {channel_id}")
        
        # 发送协作消息
        print("\n💬 发送协作消息...")
        from src.langgraph_system.agents.base_agent import MessageType
        
        architect_id = self.agents['architect'].agent_id
        pm_id = self.agents['product_manager'].agent_id
        
        # 架构师向产品经理发送消息
        message_id = await self.communication_protocol.send_message(
            sender_id=architect_id,
            recipient_id=pm_id,
            message_type=MessageType.REQUEST,
            content={
                "subject": "系统架构设计完成",
                "message": "电商平台的系统架构设计已完成，请审查需求匹配度",
                "attachments": ["architecture_diagram.pdf", "component_specs.md"]
            },
            channel_id=channel_id
        )
        
        print(f"  ✅ 消息发送完成: {message_id}")
        
        # 检查消息状态
        status = await self.communication_protocol.get_message_status(message_id)
        print(f"  📊 消息状态: {status}")
        
        # 显示通信统计
        stats = self.communication_protocol.get_protocol_statistics()
        print(f"\n📈 通信协议统计:")
        print(f"  • 总通道数: {stats['total_channels']}")
        print(f"  • 总消息数: {stats['total_messages']}")
        print(f"  • 活跃连接: {stats['active_connections']}")
    
    async def demo_capability_assessment(self):
        """演示能力评估功能"""
        print("\n" + "="*60)
        print("📊 演示智能体能力评估")
        print("="*60)
        
        # 评估架构师智能体的系统设计能力
        print("\n🔍 评估架构师智能体能力...")
        architect = self.agents['architect']
        
        assessment_id = await self.capability_assessment.assess_agent_capability(
            architect,
            "system_design"
        )
        
        result = self.capability_assessment.get_assessment_result(assessment_id)
        print(f"  ✅ 评估完成: {assessment_id}")
        print(f"  ⭐ 总体评分: {result.overall_score:.2f}/100")
        print(f"  📈 能力等级: {result.capability_level}")
        
        # 批量评估所有智能体
        print("\n🔄 批量评估所有智能体...")
        batch_results = await self.capability_assessment.batch_assess_agents(
            list(self.agents.values())
        )
        
        print(f"  ✅ 批量评估完成，共评估 {len(batch_results)} 个智能体")
        
        # 显示评估结果排名
        print("\n🏆 智能体能力排名:")
        sorted_agents = sorted(
            batch_results.items(),
            key=lambda x: x[1].overall_score,
            reverse=True
        )
        
        for i, (agent_id, result) in enumerate(sorted_agents, 1):
            agent_name = next(
                agent.name for agent in self.agents.values() 
                if agent.agent_id == agent_id
            )
            print(f"  {i}. {agent_name}: {result.overall_score:.2f}分 ({result.capability_level})")
    
    async def demo_learning_system(self):
        """演示学习系统功能"""
        print("\n" + "="*60)
        print("🧠 演示智能体学习系统")
        print("="*60)
        
        # 记录学习经验
        print("\n📚 记录智能体学习经验...")
        architect = self.agents['architect']
        
        # 模拟多个任务执行经验
        for i in range(5):
            task = TaskRequest(
                type="design_system",
                description=f"设计系统架构 #{i+1}",
                parameters={"complexity": "medium"},
                priority=Priority.MEDIUM
            )
            
            from src.langgraph_system.agents.base_agent import TaskResult
            result = TaskResult(
                task_id=task.id,
                agent_id=architect.agent_id,
                status="completed",
                result={"success": True, "quality_score": 85 + i * 2},
                execution_time=2.0 + i * 0.5
            )
            
            experience_id = await self.learning_system.record_experience(
                architect.agent_id, task, result
            )
            print(f"  📝 记录经验 #{i+1}: {experience_id}")
        
        # 开始学习会话
        print("\n🎓 开始智能体学习会话...")
        from src.langgraph_system.agents.learning import LearningType
        
        session_id = await self.learning_system.start_learning_session(
            architect.agent_id,
            LearningType.INCREMENTAL,
            "系统设计能力提升"
        )
        
        print(f"  ✅ 学习会话开始: {session_id}")
        
        # 显示学习统计
        stats = self.learning_system.get_learning_statistics(architect.agent_id)
        print(f"\n📊 学习统计:")
        print(f"  • 总经验数: {stats['total_experiences']}")
        print(f"  • 学习会话: {stats['learning_sessions']}")
        print(f"  • 平均性能: {stats['average_performance']:.2f}")
        print(f"  • 改进趋势: {stats['improvement_trend']}")
    
    async def demo_system_integration(self):
        """演示系统集成功能"""
        print("\n" + "="*60)
        print("🔗 演示系统集成功能")
        print("="*60)
        
        # 显示系统整体状态
        print("\n📊 系统整体状态:")
        
        # 智能体状态
        print(f"  🤖 注册智能体: {len(self.agents)} 个")
        for name, agent in self.agents.items():
            print(f"    • {agent.name} (ID: {agent.agent_id[:8]}...)")
        
        # 协作管理器状态
        registered_agents = self.collaboration_manager.get_registered_agents()
        print(f"  🤝 协作管理器: {len(registered_agents)} 个智能体已注册")
        
        # 通信协议状态
        comm_stats = self.communication_protocol.get_protocol_statistics()
        print(f"  📡 通信协议: {comm_stats['total_channels']} 个通道, {comm_stats['total_messages']} 条消息")
        
        # 能力评估状态
        assessment_stats = self.capability_assessment.get_assessment_statistics()
        print(f"  📊 能力评估: {assessment_stats['total_assessments']} 次评估")
        
        # 学习系统状态
        learning_stats = self.learning_system.get_system_statistics()
        print(f"  🧠 学习系统: {learning_stats['total_experiences']} 个经验记录")
        
        print("\n✅ 系统集成状态良好，所有组件正常运行")
    
    async def run_complete_demo(self):
        """运行完整演示"""
        print("🎬 开始第二阶段智能体系统完整演示")
        print("=" * 80)
        
        try:
            # 1. 初始化智能体
            await self.initialize_agents()
            
            # 2. 演示各智能体独立功能
            await self.demo_individual_agents()
            
            # 3. 演示智能体协作
            await self.demo_agent_collaboration()
            
            # 4. 演示通信协议
            await self.demo_communication_protocol()
            
            # 5. 演示能力评估
            await self.demo_capability_assessment()
            
            # 6. 演示学习系统
            await self.demo_learning_system()
            
            # 7. 演示系统集成
            await self.demo_system_integration()
            
            print("\n" + "="*80)
            print("🎉 第二阶段智能体系统演示完成！")
            print("✅ 所有功能模块运行正常")
            print("🚀 系统已准备好投入生产使用")
            print("="*80)
            
        except Exception as e:
            print(f"\n❌ 演示过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主函数"""
    demo = Phase2AgentsDemo()
    await demo.run_complete_demo()


if __name__ == "__main__":
    asyncio.run(main())