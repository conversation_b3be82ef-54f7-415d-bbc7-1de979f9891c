#!/usr/bin/env python3
"""
调试脚本：测试系统为什么没有创建文件
"""
import asyncio
import logging
from src.langgraph_system.states.project_state import ProjectState, TaskType
from src.langgraph_system.graphs.project_workflow import ProjectWorkflow

# 设置日志级别为DEBUG
logging.basicConfig(level=logging.DEBUG)

async def debug_system():
    """调试系统行为"""
    print("=== 系统调试开始 ===")
    
    workflow = ProjectWorkflow()
    
    # 测试1：使用CLI相同的参数
    print("\n1. 测试CLI参数...")
    state1 = ProjectState(
        project_name='DebugTest',
        current_task=TaskType.DEVELOPMENT,
        context={"description": "创建一个python的猜数字游戏"},
        execution_status="running"
    )
    
    result1 = await workflow.execute(state1)
    print(f"CLI参数测试结果: {result1['status']}")
    
    # 测试2：添加文件路径
    print("\n2. 测试添加文件路径...")
    state2 = ProjectState(
        project_name='DebugTest2',
        current_task=TaskType.DEVELOPMENT,
        context={
            "description": "创建一个python的猜数字游戏",
            "file_path": "DebugTest2/guess_game.py"
        },
        execution_status="running"
    )
    
    result2 = await workflow.execute(state2)
    print(f"添加文件路径测试结果: {result2['status']}")
    
    print("\n=== 调试完成 ===")

if __name__ == "__main__":
    asyncio.run(debug_system())
