#!/usr/bin/env python3
"""
系统功能验证脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def validate_system():
    """验证系统功能"""
    print("🚀 开始系统功能验证...")
    
    try:
        from src.langgraph_system.cli.interactive import InteractiveCLI
        from src.langgraph_system.agents.supervisor_agent import SupervisorAgent
        from src.langgraph_system.config.config_manager import get_config_manager
        
        # 初始化组件
        config_manager = get_config_manager()
        supervisor = SupervisorAgent(config_manager.get_llm_config())
        
        # 创建交互式CLI实例
        interactive_cli = InteractiveCLI(supervisor)
        
        # 创建示范项目配置
        project_config = {
            "name": "DemoWebApp",
            "task_type": "development",
            "description": "创建一个简单的Flask Web应用，包含用户注册和登录功能",
            "collaboration_mode": None,
            "specified_agents": None
        }
        
        print("✅ 组件初始化成功")
        print("✅ 项目配置创建成功")
        print("🎯 建议使用交互模式进行完整验证")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统验证失败: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(validate_system())
    sys.exit(0 if success else 1)