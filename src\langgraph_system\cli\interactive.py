"""
交互式命令行界面 v0.3.0
支持专业智能体生态、高级工作流引擎和智能协作机制
"""

import asyncio
import json
import yaml
from typing import Dict, Any, List, Optional
from datetime import datetime
import click

from ..states.project_state import ProjectState, TaskType
from ..agents.supervisor_agent import SupervisorAgent, CollaborationMode, AgentCapability
from .commands import (
    ProjectCommands, AgentCommands, SystemCommands, 
    WorkflowCommands, MonitoringCommands, UtilityCommands
)


class InteractiveCLI:
    """交互式命令行界面 v0.3.0"""
    
    def __init__(self, supervisor: SupervisorAgent):
        self.supervisor = supervisor
        self.project_commands = ProjectCommands(supervisor)
        self.agent_commands = AgentCommands(supervisor)
        self.system_commands = SystemCommands(supervisor)
        self.workflow_commands = WorkflowCommands(supervisor)
        self.monitoring_commands = MonitoringCommands(supervisor)
        
        self.current_project: Optional[ProjectState] = None
        self.session_history: List[Dict[str, Any]] = []
        self.running = False
    
    async def start(self):
        """启动交互式界面"""
        self.running = True
        
        click.echo("🎯 欢迎使用 LangGraph多智能体系统 v0.3.0 交互式界面")
        click.echo("=" * 60)
        
        # 显示系统状态
        await self._show_system_overview()
        
        click.echo("\n💡 输入 'help' 查看可用命令，输入 'quit' 退出")
        
        while self.running:
            try:
                # 显示提示符
                prompt = self._get_prompt()
                user_input = click.prompt(prompt, default="", show_default=False)
                
                if not user_input.strip():
                    continue
                
                # 处理命令
                await self._process_command(user_input.strip())
                
            except KeyboardInterrupt:
                click.echo("\n👋 再见！")
                break
            except EOFError:
                click.echo("\n👋 再见！")
                break
            except Exception as e:
                click.echo(f"❌ 发生错误: {e}")
    
    async def start_project_session(self, project_config: Dict[str, Any]):
        """启动项目会话"""
        self.running = True
        
        click.echo(f"🚀 启动项目会话: {project_config['name']}")
        click.echo("=" * 60)
        
        # 创建项目
        self.current_project = await self.project_commands.create_project(project_config)
        
        # 显示项目信息
        await self._show_project_overview()
        
        # 生成执行计划
        plan = await self.project_commands.create_execution_plan(self.current_project)
        click.echo("\n📋 执行计划:")
        for step in plan["execution_plan"]:
            agents_str = ", ".join(step["agents"]) if step.get("parallel") else " -> ".join(step["agents"])
            click.echo(f"  步骤 {step['step']}: {agents_str} ({step['estimated_duration']:.1f}s)")
        
        click.echo(f"\n⏱️  预计总时间: {plan['estimated_total_duration']:.1f}秒")
        
        # 询问是否执行
        if click.confirm("\n🤔 是否立即执行项目？"):
            await self._execute_current_project()
        
        click.echo("\n💡 输入 'help' 查看项目命令，输入 'quit' 退出")
        
        while self.running:
            try:
                prompt = self._get_prompt()
                user_input = click.prompt(prompt, default="", show_default=False)
                
                if not user_input.strip():
                    continue
                
                await self._process_command(user_input.strip())
                
            except KeyboardInterrupt:
                click.echo("\n👋 项目会话结束！")
                break
            except EOFError:
                click.echo("\n👋 项目会话结束！")
                break
            except Exception as e:
                click.echo(f"❌ 发生错误: {e}")
    
    def _get_prompt(self) -> str:
        """获取命令提示符"""
        if self.current_project:
            return f"[{self.current_project.project_name}] > "
        else:
            return "LangGraph v0.3.0 > "
    
    async def _process_command(self, command: str):
        """处理用户命令"""
        parts = command.split()
        if not parts:
            return
        
        cmd = parts[0].lower()
        args = parts[1:]
        
        # 记录命令历史
        self.session_history.append({
            "command": command,
            "timestamp": datetime.now().isoformat()
        })
        
        # 处理命令
        if cmd in ["help", "h"]:
            self._show_help()
        elif cmd in ["quit", "exit", "q"]:
            self.running = False
        elif cmd in ["status", "st"]:
            await self._show_status()
        elif cmd in ["agents", "ag"]:
            await self._handle_agents_command(args)
        elif cmd in ["project", "proj", "p"]:
            await self._handle_project_command(args)
        elif cmd in ["workflow", "wf", "w"]:
            await self._handle_workflow_command(args)
        elif cmd in ["monitor", "mon", "m"]:
            await self._handle_monitor_command(args)
        elif cmd in ["execute", "exec", "run"]:
            await self._handle_execute_command(args)
        elif cmd in ["plan"]:
            await self._handle_plan_command(args)
        elif cmd in ["save"]:
            await self._handle_save_command(args)
        elif cmd in ["load"]:
            await self._handle_load_command(args)
        elif cmd in ["history", "hist"]:
            self._show_history()
        elif cmd in ["clear", "cls"]:
            click.clear()
        else:
            click.echo(f"❌ 未知命令: {cmd}。输入 'help' 查看可用命令。")
    
    def _show_help(self):
        """显示帮助信息"""
        click.echo("📚 LangGraph多智能体系统 v0.3.0 命令帮助")
        click.echo("=" * 50)
        
        click.echo("\n🔧 基础命令:")
        click.echo("  help, h          - 显示此帮助信息")
        click.echo("  status, st       - 显示系统状态")
        click.echo("  quit, exit, q    - 退出交互界面")
        click.echo("  clear, cls       - 清屏")
        click.echo("  history, hist    - 显示命令历史")
        
        click.echo("\n🤖 智能体命令:")
        click.echo("  agents list      - 列出所有智能体")
        click.echo("  agents info <id> - 显示智能体详情")
        click.echo("  agents recommend <capability> - 推荐智能体")
        
        click.echo("\n📋 项目命令:")
        click.echo("  project create   - 创建新项目")
        click.echo("  project info     - 显示当前项目信息")
        click.echo("  project execute  - 执行当前项目")
        
        click.echo("\n🔄 工作流命令:")
        click.echo("  workflow templates - 列出工作流模板")
        click.echo("  workflow create    - 创建工作流")
        click.echo("  workflow execute   - 执行工作流")
        
        click.echo("\n📊 监控命令:")
        click.echo("  monitor dashboard  - 显示监控仪表板")
        click.echo("  monitor performance - 显示性能指标")
        click.echo("  monitor health     - 显示系统健康状态")
        
        click.echo("\n⚡ 执行命令:")
        click.echo("  execute          - 执行当前项目")
        click.echo("  plan             - 显示执行计划")
        click.echo("  save <file>      - 保存项目状态")
        click.echo("  load <file>      - 加载项目状态")
    
    async def _show_system_overview(self):
        """显示系统概览"""
        status = self.system_commands.get_system_status()
        
        click.echo("🖥️  系统概览:")
        click.echo(f"  版本: {status['supervisor_version']}")
        click.echo(f"  专业智能体: {status['total_agents']} 个")
        click.echo(f"  协作模式: {len(status['collaboration_modes'])} 种")
        click.echo(f"  系统能力: {len(status['capabilities'])} 项")
        click.echo(f"  状态: {status['status']}")
        
        # 性能指标
        perf = status['performance_metrics']
        if perf['total_tasks'] > 0:
            click.echo(f"\n📈 性能指标:")
            click.echo(f"  总任务数: {perf['total_tasks']}")
            click.echo(f"  成功率: {perf['success_rate']:.1%}")
            click.echo(f"  平均执行时间: {perf['average_execution_time']:.2f}s")
    
    async def _show_project_overview(self):
        """显示项目概览"""
        if not self.current_project:
            click.echo("❌ 当前没有活跃项目")
            return
        
        click.echo("📋 项目概览:")
        click.echo(f"  名称: {self.current_project.project_name}")
        click.echo(f"  ID: {self.current_project.project_id}")
        click.echo(f"  任务类型: {self.current_project.current_task.value if self.current_project.current_task else 'None'}")
        click.echo(f"  状态: {self.current_project.execution_status}")
        click.echo(f"  描述: {self.current_project.description}")
        
        # 上下文信息
        if self.current_project.context:
            click.echo(f"  协作模式: {self.current_project.context.get('collaboration_mode', 'auto')}")
            if self.current_project.context.get('specified_agents'):
                click.echo(f"  指定智能体: {', '.join(self.current_project.context['specified_agents'])}")
    
    async def _show_status(self):
        """显示状态信息"""
        await self._show_system_overview()
        
        if self.current_project:
            click.echo()
            await self._show_project_overview()
    
    async def _handle_agents_command(self, args: List[str]):
        """处理智能体命令"""
        if not args:
            click.echo("❌ 请指定智能体子命令。使用 'agents list' 查看所有智能体")
            return
        
        subcmd = args[0].lower()
        
        if subcmd == "list":
            agents_info = self.agent_commands.get_agents_info()
            click.echo("🤖 专业智能体列表:")
            for agent_id, info in agents_info.items():
                click.echo(f"\n  📋 {agent_id} ({info['name']})")
                click.echo(f"    能力: {', '.join(info['capabilities'])}")
                
                metrics = info['performance_metrics']
                if metrics.get('total_tasks', 0) > 0:
                    click.echo(f"    性能: 成功率 {metrics['success_rate']:.1%}, "
                              f"平均执行时间 {metrics['average_execution_time']:.1f}s")
        
        elif subcmd == "info" and len(args) > 1:
            agent_id = args[1]
            capabilities = self.agent_commands.get_agent_capabilities(agent_id)
            
            if not capabilities:
                click.echo(f"❌ 智能体 {agent_id} 不存在")
                return
            
            click.echo(f"🎯 智能体 {agent_id} 详情:")
            click.echo(f"  名称: {capabilities['name']}")
            click.echo(f"  专业能力: {', '.join(capabilities['capabilities'])}")
            
            click.echo("\n  📊 能力评分:")
            for cap, score in capabilities['capability_scores'].items():
                if score > 0:
                    stars = "⭐" * int(score * 5)
                    click.echo(f"    {cap}: {score:.2f} {stars}")
        
        elif subcmd == "recommend" and len(args) > 1:
            capability = args[1]
            recommendation = self.agent_commands.recommend_agent_for_capability(capability)
            
            if recommendation:
                click.echo(f"💡 推荐智能体: {recommendation['agent_id']}")
                click.echo(f"   能力评分: {recommendation['score']:.2f}")
                click.echo(f"   推荐理由: {recommendation['reason']}")
            else:
                click.echo(f"❌ 没有找到适合 {capability} 能力的智能体")
        
        else:
            click.echo("❌ 无效的智能体命令。使用 'help' 查看可用命令")
    
    async def _handle_project_command(self, args: List[str]):
        """处理项目命令"""
        if not args:
            if self.current_project:
                await self._show_project_overview()
            else:
                click.echo("❌ 当前没有活跃项目。使用 'project create' 创建新项目")
            return
        
        subcmd = args[0].lower()
        
        if subcmd == "create":
            await self._create_project_interactive()
        elif subcmd == "info":
            await self._show_project_overview()
        elif subcmd == "execute":
            await self._execute_current_project()
        else:
            click.echo("❌ 无效的项目命令。使用 'help' 查看可用命令")
    
    async def _handle_workflow_command(self, args: List[str]):
        """处理工作流命令"""
        if not args:
            click.echo("❌ 请指定工作流子命令")
            return
        
        subcmd = args[0].lower()
        
        if subcmd == "templates":
            templates = self.workflow_commands.list_templates()
            click.echo("📋 可用工作流模板:")
            for template in templates:
                click.echo(f"  • {template['name']}: {template['description']}")
        
        elif subcmd == "create":
            await self._create_workflow_interactive()
        
        elif subcmd == "execute":
            await self._execute_workflow_interactive()
        
        else:
            click.echo("❌ 无效的工作流命令。使用 'help' 查看可用命令")
    
    async def _handle_monitor_command(self, args: List[str]):
        """处理监控命令"""
        if not args:
            subcmd = "dashboard"
        else:
            subcmd = args[0].lower()
        
        if subcmd == "dashboard":
            dashboard_data = self.monitoring_commands.get_dashboard_data()
            
            click.echo("📊 系统监控仪表板")
            click.echo("=" * 30)
            click.echo(f"🟢 系统状态: {dashboard_data['system_status']}")
            click.echo(f"🤖 活跃智能体: {dashboard_data['active_agents']}")
            click.echo(f"⚡ 运行中任务: {dashboard_data['running_tasks']}")
            
            perf = dashboard_data['performance_metrics']
            click.echo(f"\n📈 性能指标:")
            click.echo(f"  成功率: {perf['success_rate']:.1%}")
            click.echo(f"  平均响应时间: {perf['avg_response_time']:.2f}s")
            click.echo(f"  吞吐量: {perf['throughput']:.1f} 任务/分钟")
        
        elif subcmd == "performance":
            all_metrics = self.monitoring_commands.get_all_performance_metrics()
            
            click.echo("📊 智能体性能指标:")
            for agent_id, metrics in all_metrics.items():
                click.echo(f"\n  {agent_id} ({metrics['agent_name']}):")
                click.echo(f"    任务数: {metrics['total_tasks']}")
                click.echo(f"    成功率: {metrics['success_rate']:.1%}")
                click.echo(f"    平均时间: {metrics['average_execution_time']:.2f}s")
        
        elif subcmd == "health":
            health = self.monitoring_commands.get_system_health()
            
            click.echo("🏥 系统健康状态:")
            click.echo(f"  健康评分: {health['health_score']:.2f}")
            click.echo(f"  健康状态: {health['health_status']}")
            click.echo(f"  成功率: {health['success_rate']:.1%}")
            click.echo(f"  平均响应时间: {health['average_response_time']:.2f}s")
            
            if health['recommendations']:
                click.echo("\n💡 改进建议:")
                for rec in health['recommendations']:
                    click.echo(f"  • {rec}")
        
        else:
            click.echo("❌ 无效的监控命令。使用 'help' 查看可用命令")
    
    async def _handle_execute_command(self, args: List[str]):
        """处理执行命令"""
        await self._execute_current_project()
    
    async def _handle_plan_command(self, args: List[str]):
        """处理计划命令"""
        if not self.current_project:
            click.echo("❌ 当前没有活跃项目")
            return
        
        plan = await self.project_commands.create_execution_plan(self.current_project)
        
        click.echo("📋 项目执行计划:")
        click.echo(f"  项目: {plan['project_name']}")
        click.echo(f"  任务类型: {plan['task_type']}")
        click.echo(f"  预计总时间: {plan['estimated_total_duration']:.1f}秒")
        
        click.echo("\n📝 执行步骤:")
        for step in plan["execution_plan"]:
            mode_str = " (并行)" if step.get("parallel") else ""
            agents_str = ", ".join(step["agents"]) if step.get("parallel") else " -> ".join(step["agents"])
            click.echo(f"  步骤 {step['step']}: {agents_str}{mode_str}")
            click.echo(f"    协作模式: {step['collaboration_mode']}")
            click.echo(f"    预计时间: {step['estimated_duration']:.1f}s")
            click.echo(f"    描述: {step['description']}")
    
    async def _handle_save_command(self, args: List[str]):
        """处理保存命令"""
        if not self.current_project:
            click.echo("❌ 当前没有活跃项目")
            return
        
        if not args:
            filename = f"{self.current_project.project_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        else:
            filename = args[0]
        
        success = self.project_commands.save_project(self.current_project, filename)
        
        if success:
            click.echo(f"✅ 项目状态已保存到: {filename}")
        else:
            click.echo(f"❌ 保存项目状态失败")
    
    async def _handle_load_command(self, args: List[str]):
        """处理加载命令"""
        if not args:
            click.echo("❌ 请指定要加载的文件")
            return
        
        filename = args[0]
        project_state = self.project_commands.load_project(filename)
        
        if project_state:
            self.current_project = project_state
            click.echo(f"✅ 项目状态已从 {filename} 加载")
            await self._show_project_overview()
        else:
            click.echo(f"❌ 加载项目状态失败")
    
    def _show_history(self):
        """显示命令历史"""
        click.echo("📜 命令历史:")
        for i, entry in enumerate(self.session_history[-10:], 1):  # 显示最近10条
            timestamp = datetime.fromisoformat(entry['timestamp']).strftime('%H:%M:%S')
            click.echo(f"  {i:2d}. [{timestamp}] {entry['command']}")
    
    async def _create_project_interactive(self):
        """交互式创建项目"""
        click.echo("🚀 创建新项目")
        click.echo("-" * 20)
        
        # 项目基本信息
        name = click.prompt("项目名称")
        description = click.prompt("项目描述")
        
        # 任务类型
        task_types = [t.value for t in TaskType]
        click.echo(f"可用任务类型: {', '.join(task_types)}")
        task_type = click.prompt("任务类型", type=click.Choice(task_types), default="development")
        
        # 协作模式
        collaboration_modes = [m.value for m in CollaborationMode]
        click.echo(f"可用协作模式: {', '.join(collaboration_modes)}")
        collaboration_mode = click.prompt("协作模式 (可选)", default="", show_default=False)
        
        # 指定智能体
        agents_info = self.agent_commands.get_agents_info()
        available_agents = list(agents_info.keys())
        click.echo(f"可用智能体: {', '.join(available_agents)}")
        specified_agents = click.prompt("指定智能体 (逗号分隔，可选)", default="", show_default=False)
        
        # 创建项目配置
        project_config = {
            "name": name,
            "description": description,
            "task_type": task_type,
            "collaboration_mode": collaboration_mode if collaboration_mode else None,
            "specified_agents": [a.strip() for a in specified_agents.split(",")] if specified_agents else None
        }
        
        # 创建项目
        self.current_project = await self.project_commands.create_project(project_config)
        
        click.echo(f"✅ 项目 '{name}' 创建成功！")
        await self._show_project_overview()
    
    async def _execute_current_project(self):
        """执行当前项目"""
        if not self.current_project:
            click.echo("❌ 当前没有活跃项目")
            return
        
        click.echo("⚡ 开始执行项目...")
        
        try:
            result = await self.project_commands.execute_project(self.current_project)
            
            if result['status'] == 'completed':
                click.echo("✅ 项目执行完成!")
                
                # 显示执行摘要
                if 'execution_summary' in result:
                    summary = result['execution_summary']
                    click.echo(f"\n📊 执行摘要:")
                    click.echo(f"  参与智能体: {summary['total_agents']}")
                    click.echo(f"  成功智能体: {summary['successful_agents']}")
                    click.echo(f"  协作模式: {summary['collaboration_mode']}")
                
                # 显示智能体贡献
                if 'agent_contributions' in result:
                    click.echo(f"\n🤖 智能体贡献:")
                    for agent_id, contribution in result['agent_contributions'].items():
                        click.echo(f"  {agent_id}:")
                        click.echo(f"    {contribution[:200]}...")
                
            elif result['status'] == 'partial_success':
                click.echo("⚠️  项目部分完成")
                if 'errors' in result:
                    click.echo("❌ 发生的错误:")
                    for error in result['errors']:
                        click.echo(f"  • {error}")
            else:
                click.echo(f"❌ 项目执行失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            click.echo(f"💥 执行过程中发生错误: {e}")
    
    async def _create_workflow_interactive(self):
        """交互式创建工作流"""
        click.echo("🔄 创建工作流")
        click.echo("-" * 15)
        
        # 选择创建方式
        creation_methods = ["template", "custom"]
        method = click.prompt("创建方式", type=click.Choice(creation_methods), default="template")
        
        if method == "template":
            templates = self.workflow_commands.list_templates()
            template_names = [t['name'] for t in templates]
            
            click.echo("可用模板:")
            for template in templates:
                click.echo(f"  • {template['name']}: {template['description']}")
            
            template_name = click.prompt("选择模板", type=click.Choice(template_names))
            workflow_def = self.workflow_commands.create_from_template(template_name)
        else:
            workflow_def = self.workflow_commands.create_interactive()
        
        # 保存工作流
        if click.confirm("是否保存工作流定义？"):
            filename = click.prompt("文件名", default="workflow.yaml")
            
            with open(filename, 'w', encoding='utf-8') as f:
                yaml.dump(workflow_def, f, default_flow_style=False, allow_unicode=True)
            
            click.echo(f"✅ 工作流定义已保存到: {filename}")
        
        # 询问是否执行
        if click.confirm("是否立即执行工作流？"):
            input_data = {}
            if click.confirm("是否提供输入数据？"):
                input_str = click.prompt("输入数据 (JSON格式)", default="{}")
                try:
                    input_data = json.loads(input_str)
                except json.JSONDecodeError:
                    click.echo("❌ 输入数据格式错误，使用空数据")
                    input_data = {}
            
            # 执行工作流
            await self._execute_workflow_definition(workflow_def, input_data)
    
    async def _execute_workflow_interactive(self):
        """交互式执行工作流"""
        click.echo("⚡ 执行工作流")
        click.echo("-" * 12)
        
        # 选择工作流文件
        workflow_file = click.prompt("工作流文件路径")
        
        # 输入数据
        input_data = {}
        if click.confirm("是否提供输入数据？"):
            input_str = click.prompt("输入数据 (JSON格式)", default="{}")
            try:
                input_data = json.loads(input_str)
            except json.JSONDecodeError:
                click.echo("❌ 输入数据格式错误，使用空数据")
                input_data = {}
        
        # 并行执行选项
        parallel = click.confirm("是否启用并行执行？", default=False)
        
        # 执行工作流
        try:
            result = await self.workflow_commands.execute_workflow_file(workflow_file, input_data, parallel)
            
            if result['status'] == 'completed':
                click.echo("✅ 工作流执行完成!")
                click.echo(f"📊 结果: {result['result'][:300]}...")
            else:
                click.echo(f"❌ 工作流执行失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            click.echo(f"💥 工作流执行错误: {e}")
    
    async def _execute_workflow_definition(self, workflow_def: Dict[str, Any], input_data: Dict[str, Any]):
        """执行工作流定义"""
        try:
            # 构建任务信息
            task_info = {
                "id": f"workflow_{datetime.now().timestamp()}",
                "type": "workflow_execution",
                "description": workflow_def.get("description", "工作流执行"),
                "requirements": input_data,
                "workflow_definition": workflow_def
            }
            
            # 执行工作流
            result = await self.supervisor.process_task(task_info)
            
            if result['status'] == 'completed':
                click.echo("✅ 工作流执行完成!")
                click.echo(f"📊 结果: {result['result'][:300]}...")
            else:
                click.echo(f"❌ 工作流执行失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            click.echo(f"💥 工作流执行错误: {e}")