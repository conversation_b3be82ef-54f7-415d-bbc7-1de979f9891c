#!/usr/bin/env python3
"""
v0.3.0 架构基础定义
包含被多个模块共享的核心枚举和类
"""

from enum import Enum
from typing import List, Dict, Any

from langchain_core.language_models.base import BaseLanguageModel
from langgraph.prebuilt import create_react_agent
from datetime import datetime

class CollaborationMode(Enum):
    """协作模式枚举"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    REVIEW = "review"
    CONSULTATION = "consultation"
    PAIR_PROGRAMMING = "pair_programming"
    BRAINSTORMING = "brainstorming"
    MENTORING = "mentoring"

class AgentCapability(Enum):
    """智能体能力枚举"""
    ARCHITECTURE_DESIGN = "architecture_design"
    REQUIREMENT_ANALYSIS = "requirement_analysis"
    CODE_DEVELOPMENT = "code_development"
    TESTING_QA = "testing_qa"
    DEVOPS_DEPLOYMENT = "devops_deployment"
    DOCUMENTATION = "documentation"
    SECURITY_AUDIT = "security_audit"
    PERFORMANCE_OPTIMIZATION = "performance_optimization"

class SpecialistAgent:
    """专业智能体基类"""
    
    def __init__(self, agent_id: str, name: str, capabilities: List[AgentCapability], 
                 model: BaseLanguageModel, tools: List = None):
        self.agent_id = agent_id
        self.name = name
        self.capabilities = capabilities
        self.model = model
        self.tools = tools or []
        self.performance_metrics = {}
        self.collaboration_history = []
        
        # 创建LangGraph智能体
        self.langgraph_agent = create_react_agent(
            model=self.model,
            tools=self.tools,
            name=self.agent_id,
            prompt=self._create_agent_prompt()
        )
    
    def _create_agent_prompt(self) -> str:
        """创建智能体提示词"""
        capabilities_desc = ", ".join([cap.value for cap in self.capabilities])
        return f"""你是一个专业的{self.name}智能体。

你的专业能力包括：{capabilities_desc}

请根据任务需求提供专业的服务，与其他智能体协作完成项目目标。
在协作过程中，请：
1. 明确表达你的专业观点
2. 主动提供建设性建议
3. 与其他智能体保持良好沟通
4. 确保输出质量符合专业标准
"""
    
    async def execute_task(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行任务"""
        try:
            start_time = datetime.now()
            
            # 构建消息
            messages = [{
                "role": "user",
                "content": self._format_task_message(task, context)
            }]
            
            # 执行智能体
            result = self.langgraph_agent.invoke({"messages": messages})
            
            # 记录性能指标
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_performance_metrics(execution_time, True)
            
            return {
                "status": "completed",
                "result": result["messages"][-1].content,
                "agent_id": self.agent_id,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            # logger.error(f"智能体 {self.agent_id} 执行任务失败: {e}") #需要添加logger
            self._update_performance_metrics(0, False)
            return {
                "status": "failed",
                "error": str(e),
                "agent_id": self.agent_id,
                "timestamp": datetime.now().isoformat()
            }
    
    def _format_task_message(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """格式化任务消息"""
        task_type = task.get('type', 'general')
        description = task.get('description', '')
        requirements = task.get('requirements', {})
        
        message = f"""
任务类型：{task_type}
任务描述：{description}

需求详情：
{requirements}

上下文信息：
{context or {}}

请根据你的专业能力处理这个任务。
"""
        return message
    
    def _update_performance_metrics(self, execution_time: float, success: bool):
        """更新性能指标"""
        if "total_tasks" not in self.performance_metrics:
            self.performance_metrics = {
                "total_tasks": 0,
                "successful_tasks": 0,
                "failed_tasks": 0,
                "total_execution_time": 0.0,
                "average_execution_time": 0.0,
                "success_rate": 0.0
            }
        
        self.performance_metrics["total_tasks"] += 1
        self.performance_metrics["total_execution_time"] += execution_time
        
        if success:
            self.performance_metrics["successful_tasks"] += 1
        else:
            self.performance_metrics["failed_tasks"] += 1
        
        # 计算平均值和成功率
        total_tasks = self.performance_metrics["total_tasks"]
        self.performance_metrics["average_execution_time"] = (
            self.performance_metrics["total_execution_time"] / total_tasks
        )
        self.performance_metrics["success_rate"] = (
            self.performance_metrics["successful_tasks"] / total_tasks
        )
    
    def get_capability_score(self, capability: "AgentCapability") -> float:
        """获取能力评分"""
        if capability in self.capabilities:
            # 基于性能指标计算能力评分
            base_score = 0.8
            success_rate = self.performance_metrics.get("success_rate", 0.5)
            return min(1.0, base_score + (success_rate - 0.5) * 0.4)
        return 0.0