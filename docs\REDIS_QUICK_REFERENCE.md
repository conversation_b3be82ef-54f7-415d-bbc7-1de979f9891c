# 🚀 Redis配置快速参考

## ⚡ 一键解决

```bash
# 最简单的方式 - 内存模拟模式
python scripts/setup_redis.py memory

# 然后测试系统
python src/main.py --llm-provider moonshot status
```

## 🔧 配置模式

| 命令 | 模式 | 说明 | 适用场景 |
|------|------|------|----------|
| `python scripts/setup_redis.py memory` | 内存模拟 | 无需Redis服务 | 开发测试 ⭐ |
| `python scripts/setup_redis.py docker` | Docker容器 | 需要Docker | 本地开发 |
| `python scripts/setup_redis.py local` | 本地安装 | 需要安装Redis | 生产环境 |
| `python scripts/setup_redis.py` | 交互式 | 引导配置 | 首次使用 |

## 🔍 故障排除

### 系统卡住不动？
```bash
# 检查Redis模式
grep REDIS_MODE .env

# 切换到内存模式
echo "REDIS_MODE=memory" >> .env
```

### 缺少依赖？
```bash
pip install aioredis>=2.0.0
```

### Docker Redis问题？
```bash
# 重启Redis容器
docker restart langgraph-redis

# 或重新创建
docker rm -f langgraph-redis
python scripts/setup_redis.py docker
```

## ✅ 验证配置

```bash
# 检查系统状态
python src/main.py status

# 查看智能体
python src/main.py agents list

# 测试项目创建
python src/main.py project create --name "Test"
```

## 📝 环境变量

```bash
# 最小配置
REDIS_MODE=memory

# 完整配置
REDIS_MODE=local
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

## 🆘 紧急修复

如果系统完全无法启动：

1. **删除现有配置**:
   ```bash
   rm .env
   ```

2. **重新配置**:
   ```bash
   python scripts/setup_redis.py memory
   ```

3. **测试系统**:
   ```bash
   python src/main.py status
   ```

## 📞 获取帮助

- 详细文档: [REDIS_CONFIGURATION_GUIDE.md](REDIS_CONFIGURATION_GUIDE.md)
- 系统问题: [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
- 快速开始: [CLI_QUICK_START.md](CLI_QUICK_START.md)
