#!/usr/bin/env python3
"""
QA智能体 (QAAgent) - v0.3.0 重构
负责测试策略制定、质量保证、缺陷管理和自动化测试设计
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
import logging

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.tools import tool

from .base_v3 import SpecialistAgent, AgentCapability

logger = logging.getLogger(__name__)

# ============================================================================
# QA领域数据模型
# ============================================================================

class TestType(str, Enum):
    UNIT = "unit"
    INTEGRATION = "integration"
    SYSTEM = "system"
    PERFORMANCE = "performance"
    SECURITY = "security"

class DefectSeverity(str, Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class TestCase:
    id: str
    title: str
    test_type: TestType
    steps: List[str]
    expected_result: str

@dataclass
class Defect:
    id: str
    title: str
    description: str
    severity: DefectSeverity
    steps_to_reproduce: List[str]

# ============================================================================
# QA智能体实现 (v3)
# ============================================================================

class QAAgent(SpecialistAgent):
    """
    QA智能体 v3
    
    核心职责：
    - 制定测试策略、设计测试用例、报告缺陷和评估软件质量
    """
    
    def __init__(self, model: BaseLanguageModel, custom_tools: List = None, **kwargs):
        self.test_cases: Dict[str, TestCase] = {}
        self.defects: Dict[str, Defect] = {}
        
        agent_tools = [
            self.design_test_strategy,
            self.create_test_cases,
            self.report_defect,
            self.assess_quality,
        ]
        if custom_tools:
            agent_tools.extend(custom_tools)

        super().__init__(
            agent_id="qa_agent_001",
            name="质量保证工程师",
            capabilities=[AgentCapability.TESTING_QA],
            model=model,
            tools=agent_tools,
            **kwargs,
        )

    # ========================================================================
    # 智能体工具定义
    # ========================================================================

    @tool
    async def design_test_strategy(self, requirements: str, project_context: str) -> str:
        """
        根据项目需求和上下文设计测试策略。
        Args:
            requirements (str): 项目需求描述.
            project_context (str): 项目上下文信息 (e.g.,技术栈、团队规模).
        Returns:
            str: JSON格式的测试策略摘要.
        """
        try:
            logger.info("开始设计测试策略")
            
            strategy = await self._invoke_llm_for_strategy(requirements, project_context)
            
            return json.dumps(strategy, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"测试策略设计失败: {e}", exc_info=True)
            return json.dumps({"error": f"测试策略设计失败: {str(e)}"})

    @tool
    async def create_test_cases(self, requirement: str, test_type: str) -> str:
        """
        为一个特定的需求创建测试用例。
        Args:
            requirement (str): 需要测试的需求或功能描述.
            test_type (str): 测试类型 (e.g., 'unit', 'integration', 'system').
        Returns:
            str: JSON格式的测试用例列表.
        """
        try:
            test_type_enum = TestType(test_type)
            logger.info(f"为需求创建 '{test_type}' 测试用例")
            
            cases_data = await self._invoke_llm_for_test_cases(requirement, test_type_enum)
            
            created_cases = []
            for case_data in cases_data:
                case_id = f"tc_{uuid.uuid4().hex[:8]}"
                test_case = TestCase(
                    id=case_id,
                    title=case_data['title'],
                    test_type=test_type_enum,
                    steps=case_data['steps'],
                    expected_result=case_data['expected_result'],
                )
                self.test_cases[case_id] = test_case
                created_cases.append({"id": case_id, "title": test_case.title})
            
            return json.dumps(created_cases, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"测试用例创建失败: {e}", exc_info=True)
            return json.dumps({"error": f"测试用例创建失败: {str(e)}"})

    @tool
    async def report_defect(self, title: str, description: str, severity: str, steps_to_reproduce: List[str]) -> str:
        """
        报告一个软件缺陷。
        Args:
            title (str): 缺陷标题.
            description (str): 缺陷的详细描述.
            severity (str): 缺陷严重程度 ('critical', 'high', 'medium', 'low').
            steps_to_reproduce (List[str]): 复现缺陷的步骤.
        Returns:
            str: JSON格式的缺陷报告确认信息.
        """
        try:
            severity_enum = DefectSeverity(severity)
            logger.info(f"报告新缺陷: {title}")
            
            defect_id = f"defect_{uuid.uuid4().hex[:8]}"
            defect = Defect(
                id=defect_id,
                title=title,
                description=description,
                severity=severity_enum,
                steps_to_reproduce=steps_to_reproduce,
            )
            self.defects[defect_id] = defect
            
            return json.dumps({
                "defect_id": defect.id,
                "title": defect.title,
                "severity": defect.severity.value,
                "status": "reported",
            }, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"缺陷报告失败: {e}", exc_info=True)
            return json.dumps({"error": f"缺陷报告失败: {str(e)}"})
            
    @tool
    async def assess_quality(self, test_results: str, code_coverage: str, defect_data: str) -> str:
        """
        基于测试结果、代码覆盖率和缺陷数据评估软件质量。
        Args:
            test_results (str): JSON格式的测试结果摘要. e.g., '{"total": 100, "passed": 95}'.
            code_coverage (str): JSON格式的代码覆盖率数据. e.g., '{"line_coverage": 85.5}'.
            defect_data (str): JSON格式的缺陷数据摘要. e.g., '{"open_critical": 1, "total_open": 10}'.
        Returns:
            str: JSON格式的质量评估报告。
        """
        try:
            logger.info("开始评估软件质量")
            
            quality_report = await self._invoke_llm_for_quality_assessment(test_results, code_coverage, defect_data)
            
            return json.dumps(quality_report, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"质量评估失败: {e}", exc_info=True)
            return json.dumps({"error": f"质量评估失败: {str(e)}"})

    # ========================================================================
    # 内部辅助方法 & LLM 调用
    # ========================================================================

    async def _invoke_llm_for_strategy(self, requirements: str, context: str) -> Dict[str, Any]:
        """使用LLM为项目设计测试策略"""
        prompt = f"""As a QA strategist, create a concise test strategy in JSON format.
Project Requirements: {requirements}
Project Context: {context}
Provide a JSON object with keys: 'test_levels' (list of strings), 'test_types' (list of strings), 'automation_focus' (string), and 'risk_assessment' (string)."""
        response = await self.model.ainvoke(prompt)
        return json.loads(response.content)

    async def _invoke_llm_for_test_cases(self, requirement: str, test_type: TestType) -> List[Dict[str, Any]]:
        """使用LLM生成测试用例"""
        prompt = f"""As a QA engineer, create test cases for the following requirement.
Requirement: {requirement}
Test Type: {test_type.value}
Provide a JSON list of test cases, each with 'title', 'steps' (list of strings), and 'expected_result' (string)."""
        response = await self.model.ainvoke(prompt)
        return json.loads(response.content)
        
    async def _invoke_llm_for_quality_assessment(self, test_results: str, coverage: str, defects: str) -> Dict[str, Any]:
        """使用LLM进行质量评估"""
        prompt = f"""As a QA manager, provide a software quality assessment based on the following data.
Test Results: {test_results}
Code Coverage: {coverage}
Defect Data: {defects}
Provide a JSON object with 'quality_score' (0-100), 'summary' (string), and 'recommendations' (list of strings)."""
        response = await self.model.ainvoke(prompt)
        return json.loads(response.content)

__all__ = ['QAAgent', 'TestCase', 'Defect', 'TestType', 'DefectSeverity']