"""LLM客户端工厂"""

from typing import Dict, Any
from .base_client import BaseLL<PERSON>lient
from .config import LLMConfig
from .moonshot_client import MoonshotClient


class LLMFactory:
    """LLM客户端工厂"""
    
    _clients: Dict[str, BaseLLMClient] = {}
    
    @classmethod
    def create_client(cls, config: LLMConfig) -> BaseLLMClient:
        """创建LLM客户端"""
        provider = config.provider
        
        if provider == "moonshot":
            return MoonshotClient(config.get_client_config())
        elif provider == "openai":
            from .openai_client import OpenAIClient
            return OpenAIClient(config.get_client_config())
        elif provider == "anthropic":
            from .anthropic_client import AnthropicClient
            return AnthropicClient(config.get_client_config())
        else:
            raise ValueError(f"Unsupported provider: {provider}")
    
    @classmethod
    def get_client(cls, config: LLMConfig) -> BaseLLMClient:
        """获取或创建LLM客户端"""
        key = f"{config.provider}:{config.model}"
        
        if key not in cls._clients:
            cls._clients[key] = cls.create_client(config)
        
        return cls._clients[key]
    
    @classmethod
    def clear_cache(cls):
        """清除客户端缓存"""
        cls._clients.clear()
