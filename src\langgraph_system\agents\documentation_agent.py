#!/usr/bin/env python3
"""
文档智能体 (DocumentationAgent) - v0.3.0 重构
负责技术文档生成、API文档自动化和知识库管理
"""

import asyncio
import json
import uuid
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
import logging

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.tools import tool

from .base_v3 import SpecialistAgent, AgentCapability

logger = logging.getLogger(__name__)

# ============================================================================
# 文档领域数据模型
# ============================================================================

class DocumentType(str, Enum):
    API_REFERENCE = "api_reference"
    USER_GUIDE = "user_guide"
    TECHNICAL_SPEC = "technical_spec"
    README = "readme"

class DocumentFormat(str, Enum):
    MARKDOWN = "markdown"
    HTML = "html"
    OPENAPI = "openapi"

class DocumentStatus(str, Enum):
    DRAFT = "draft"
    REVIEW = "review"
    PUBLISHED = "published"

@dataclass
class Document:
    id: str
    title: str
    content: str
    doc_type: DocumentType
    doc_format: DocumentFormat
    version: str = "1.0.0"
    status: DocumentStatus = DocumentStatus.DRAFT

# ============================================================================
# 文档智能体实现 (v3)
# ============================================================================

class DocumentationAgent(SpecialistAgent):
    """
    文档智能体 v3
    
    核心职责：
    - 技术文档和API文档的生成、验证和更新
    """
    
    def __init__(self, model: BaseLanguageModel, custom_tools: List = None, **kwargs):
        self.documents: Dict[str, Document] = {}
        
        agent_tools = [
            self.generate_documentation,
            self.generate_api_documentation,
            self.validate_documentation,
            self.update_documentation,
        ]
        if custom_tools:
            agent_tools.extend(custom_tools)

        super().__init__(
            agent_id="doc_agent_001",
            name="文档智能体",
            capabilities=[AgentCapability.DOCUMENTATION],
            model=model,
            tools=agent_tools,
            **kwargs,
        )

    # ========================================================================
    # 智能体工具定义
    # ========================================================================

    @tool
    async def generate_documentation(self, doc_type: str, title: str, source_content: str, doc_format: str = "markdown") -> str:
        """
        根据源内容生成技术文档。
        Args:
            doc_type (str): 文档类型 (e.g., 'user_guide', 'technical_spec').
            title (str): 文档标题.
            source_content (str): 用于生成文档的源材料或JSON格式的需求.
            doc_format (str, optional): 输出格式 (e.g., 'markdown'). Defaults to "markdown".
        Returns:
            str: JSON格式的文档生成结果, 包括文档ID和内容.
        """
        try:
            doc_type_enum = DocumentType(doc_type)
            doc_format_enum = DocumentFormat(doc_format)
            logger.info(f"开始生成文档: {title} ({doc_type})")

            content = await self._invoke_llm_for_doc_generation(doc_type_enum, title, source_content)
            
            doc_id = f"doc_{uuid.uuid4().hex[:8]}"
            document = Document(
                id=doc_id,
                title=title,
                content=content,
                doc_type=doc_type_enum,
                doc_format=doc_format_enum,
            )
            self.documents[doc_id] = document
            
            return json.dumps({
                "document_id": document.id,
                "title": document.title,
                "content_preview": document.content[:200] + "...",
            }, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"文档生成失败: {e}", exc_info=True)
            return json.dumps({"error": f"文档生成失败: {str(e)}"})

    @tool
    async def generate_api_documentation(self, api_spec: str, doc_format: str = "markdown") -> str:
        """
        从API规范 (如OpenAPI) 自动生成API文档。
        Args:
            api_spec (str): JSON或YAML格式的API规范.
            doc_format (str, optional): 输出格式 ('markdown' or 'openapi'). Defaults to "markdown".
        Returns:
            str: JSON格式的API文档.
        """
        try:
            spec_dict = json.loads(api_spec)
            api_title = spec_dict.get("info", {}).get("title", "API")
            doc_format_enum = DocumentFormat(doc_format)
            logger.info(f"开始生成API文档: {api_title}")
            
            content = await self._invoke_llm_for_api_doc_generation(spec_dict, doc_format_enum)
            
            doc_id = f"api_doc_{uuid.uuid4().hex[:8]}"
            document = Document(
                id=doc_id,
                title=f"{api_title} API Reference",
                content=content,
                doc_type=DocumentType.API_REFERENCE,
                doc_format=doc_format_enum,
            )
            self.documents[doc_id] = document
            
            return json.dumps({
                "document_id": document.id,
                "title": document.title,
                "format": doc_format_enum.value,
            }, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"API文档生成失败: {e}", exc_info=True)
            return json.dumps({"error": f"API文档生成失败: {str(e)}"})

    @tool
    async def validate_documentation(self, document_id: str) -> str:
        """
        验证文档的质量和一致性。
        Args:
            document_id (str): 要验证的文档ID.
        Returns:
            str: JSON格式的验证报告.
        """
        try:
            if document_id not in self.documents:
                raise ValueError(f"文档 '{document_id}' 不存在")
            document = self.documents[document_id]
            logger.info(f"开始验证文档: {document.title}")

            issues = await self._analyze_doc_issues(document.content)
            score = 100 - len(issues) * 10

            return json.dumps({
                "document_id": document_id,
                "validation_score": max(0, score),
                "issues_found": len(issues),
                "issues": issues,
            }, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"文档验证失败: {e}", exc_info=True)
            return json.dumps({"error": f"文档验证失败: {str(e)}"})

    @tool
    async def update_documentation(self, document_id: str, changes_description: str) -> str:
        """
        根据变更描述更新现有文档。
        Args:
            document_id (str): 要更新的文档ID.
            changes_description (str): 对文档需要进行的变更描述.
        Returns:
            str: JSON格式的更新确认信息.
        """
        try:
            if document_id not in self.documents:
                raise ValueError(f"文档 '{document_id}' 不存在")
            document = self.documents[document_id]
            logger.info(f"开始更新文档: {document.title}")

            updated_content = await self._invoke_llm_for_doc_update(document.content, changes_description)
            
            document.content = updated_content
            document.version = self._increment_version(document.version)
            document.status = DocumentStatus.DRAFT # 更新后需要重新审查
            
            return json.dumps({
                "document_id": document.id,
                "new_version": document.version,
                "status": "updated_successfully",
            }, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"文档更新失败: {e}", exc_info=True)
            return json.dumps({"error": f"文档更新失败: {str(e)}"})

    # ========================================================================
    # 内部辅助方法 & LLM 调用
    # ========================================================================

    async def _invoke_llm_for_doc_generation(self, doc_type: DocumentType, title: str, source: str) -> str:
        prompt = f"""As a technical writer, generate a '{doc_type.value}' document titled '{title}'.
Use the following source material:
{source}
The output should be in Markdown format."""
        response = await self.model.ainvoke(prompt)
        return response.content
    
    async def _invoke_llm_for_api_doc_generation(self, spec: Dict[str, Any], doc_format: DocumentFormat) -> str:
        prompt = f"""As a technical writer, create API documentation in {doc_format.value} format from the following OpenAPI spec:
{json.dumps(spec, indent=2)}
If the format is markdown, create a user-friendly guide. If OpenAPI, just return the spec."""
        if doc_format == DocumentFormat.OPENAPI:
            return json.dumps(spec, indent=2, ensure_ascii=False)
        
        response = await self.model.ainvoke(prompt)
        return response.content

    async def _invoke_llm_for_doc_update(self, old_content: str, changes: str) -> str:
        prompt = f"""As a technical writer, update the following document based on the requested changes.
Document Content:
{old_content}
Requested Changes:
{changes}
Provide the full, updated document in Markdown format."""
        response = await self.model.ainvoke(prompt)
        return response.content
        
    async def _analyze_doc_issues(self, content: str) -> List[Dict[str, Any]]:
        """分析文档中的常见问题 (简化版)"""
        issues = []
        if "TODO" in content or "FIXME" in content:
            issues.append({"type": "Unresolved Task", "detail": "Found 'TODO' or 'FIXME' placeholders."})
        if "..." in content:
            issues.append({"type": "Incomplete Content", "detail": "Found '...' suggesting placeholder content."})
        if not re.search(r'^# .+', content):
            issues.append({"type": "Missing Title", "detail": "Document appears to be missing a main title (H1)."})
        return issues
        
    def _increment_version(self, version: str) -> str:
        """简单增加文档版本号"""
        parts = version.split('.')
        try:
            patch = int(parts[-1]) + 1
            return ".".join(parts[:-1] + [str(patch)])
        except (ValueError, IndexError):
            return "1.0.1"


__all__ = ['DocumentationAgent', 'Document', 'DocumentType', 'DocumentFormat', 'DocumentStatus']