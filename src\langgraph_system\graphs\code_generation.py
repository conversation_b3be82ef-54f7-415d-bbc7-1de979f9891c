"""代码生成工作流"""

from typing import Dict, Any, List, Optional
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
import logging
from datetime import datetime

from ..states.project_state import ProjectState, TaskType, AgentStatus

logger = logging.getLogger(__name__)

class CodeGenerationWorkflow:
    """代码生成专用工作流"""
    
    def __init__(self):
        """初始化代码生成工作流"""
        self.graph = StateGraph(ProjectState)
        self.memory_saver = MemorySaver()
        
        self._setup_graph()
        
    def _setup_graph(self):
        """设置工作流图"""
        # 添加节点
        self.graph.add_node("analyze_requirements", self._analyze_requirements)
        self.graph.add_node("design_architecture", self._design_architecture)
        self.graph.add_node("generate_code", self._generate_code)
        self.graph.add_node("review_code", self._review_code)
        self.graph.add_node("generate_tests", self._generate_tests)
        self.graph.add_node("refactor_code", self._refactor_code)
        
        # 添加边
        self._setup_edges()
        
        # 设置检查点
        self.graph = self.graph.compile(checkpointer=self.memory_saver)
        
    def _setup_edges(self):
        """设置工作流边"""
        # 顺序执行
        self.graph.add_edge("analyze_requirements", "design_architecture")
        self.graph.add_edge("design_architecture", "generate_code")
        self.graph.add_edge("generate_code", "review_code")
        
        # 条件边：基于审查结果
        self.graph.add_conditional_edges(
            "review_code",
            self._review_decision,
            {
                "approve": "generate_tests",
                "needs_refactor": "refactor_code",
                "reject": "generate_code"
            }
        )
        
        # 重构后重新审查
        self.graph.add_edge("refactor_code", "review_code")
        
        # 测试完成后结束
        self.graph.add_edge("generate_tests", END)
        
        # 设置入口点
        self.graph.set_entry_point("analyze_requirements")
        
    def _analyze_requirements(self, state: ProjectState) -> Dict[str, Any]:
        """分析需求节点"""
        logger.info("Analyzing requirements")
        
        # 模拟需求分析
        requirements = {
            "functional": ["user authentication", "data storage", "API endpoints"],
            "non_functional": ["performance", "security", "scalability"],
            "constraints": ["budget", "timeline", "technology stack"]
        }
        
        state.add_artifact("requirements", requirements)
        state.update_agent_status("architect", AgentStatus.COMPLETED)
        
        return state
        
    def _design_architecture(self, state: ProjectState) -> Dict[str, Any]:
        """设计架构节点"""
        logger.info("Designing architecture")
        
        # 模拟架构设计
        architecture = {
            "pattern": "MVC",
            "database": "PostgreSQL",
            "backend": "FastAPI",
            "frontend": "React",
            "deployment": "Docker + Kubernetes"
        }
        
        state.add_artifact("architecture", architecture)
        state.update_agent_status("architect", AgentStatus.COMPLETED)
        
        return state
        
    def _generate_code(self, state: ProjectState) -> Dict[str, Any]:
        """生成代码节点"""
        logger.info("Generating code")
        
        # 模拟代码生成
        code_files = {
            "main.py": "from fastapi import FastAPI\n\napp = FastAPI()\n\<EMAIL>('/')\ndef read_root():\n    return {'message': 'Hello World'}",
            "models.py": "from sqlalchemy import Column, Integer, String\nfrom sqlalchemy.ext.declarative import declarative_base\n\nBase = declarative_base()\n\nclass User(Base):\n    __tablename__ = 'users'\n    \n    id = Column(Integer, primary_key=True)\n    username = Column(String(50), unique=True)\n    email = Column(String(100), unique=True)",
            "requirements.txt": "fastapi==0.104.1\nsqlalchemy==2.0.23\npsycopg2-binary==2.9.9"
        }
        
        for filename, content in code_files.items():
            state.files[filename] = content
            
        state.update_agent_status("developer", AgentStatus.COMPLETED)
        
        return state
        
    def _review_code(self, state: ProjectState) -> Dict[str, Any]:
        """代码审查节点"""
        logger.info("Reviewing code")
        
        # 模拟代码审查，在重构一次后批准
        if state.refactor_count > 0:
            review_status = "approved"
        else:
            review_status = "needs_improvement"

        review_result = {
            "status": review_status,
            "issues": [] if review_status == "approved" else [
                {"type": "style", "description": "Missing docstrings"},
                {"type": "security", "description": "No input validation"},
            ],
            "suggestions": [] if review_status == "approved" else [
                "Add comprehensive docstrings",
                "Implement input validation",
            ]
        }
        
        state.code_reviews.append(review_result)
        state.update_agent_status("reviewer", AgentStatus.COMPLETED)
        
        return state
        
    def _review_decision(self, state: ProjectState) -> str:
        """审查决策"""
        if state.code_reviews:
            last_review = state.code_reviews[-1]
            if last_review["status"] == "approved":
                return "approve"
            elif last_review["status"] == "needs_improvement":
                return "needs_refactor"
            else:
                return "reject"
        return "approve"
        
    def _refactor_code(self, state: ProjectState) -> Dict[str, Any]:
        """重构代码节点"""
        logger.info("Refactoring code")
        state.refactor_count += 1
        
        # 模拟代码重构
        refactored_files = {
            "main.py": "from fastapi import FastAPI, HTTPException\nfrom pydantic import BaseModel\n\napp = FastAPI()\n\nclass HealthCheck(BaseModel):\n    status: str\n\<EMAIL>('/')\nasync def read_root():\n    return {'message': 'Hello World'}\n\<EMAIL>('/health', response_model=HealthCheck)\nasync def health_check():\n    return HealthCheck(status='healthy')",
            "models.py": "from sqlalchemy import Column, Integer, String\nfrom sqlalchemy.ext.declarative import declarative_base\nfrom pydantic import BaseModel, EmailStr\n\nBase = declarative_base()\n\nclass User(Base):\n    __tablename__ = 'users'\n    \n    id = Column(Integer, primary_key=True)\n    username = Column(String(50), unique=True, nullable=False)\n    email = Column(String(100), unique=True, nullable=False)\n\nclass UserCreate(BaseModel):\n    username: str\n    email: EmailStr"
        }
        
        for filename, content in refactored_files.items():
            state.files[filename] = content
            
        state.update_agent_status("developer", AgentStatus.COMPLETED)
        
        return state
        
    def _generate_tests(self, state: ProjectState) -> Dict[str, Any]:
        """生成测试节点"""
        logger.info("Generating tests")
        
        # 模拟测试生成
        test_files = {
            "test_main.py": "import pytest\nfrom fastapi.testclient import TestClient\nfrom main import app\n\nclient = TestClient(app)\n\ndef test_read_root():\n    response = client.get('/')\n    assert response.status_code == 200\n    assert response.json() == {'message': 'Hello World'}\n\ndef test_health_check():\n    response = client.get('/health')\n    assert response.status_code == 200\n    assert 'status' in response.json()",
            "test_models.py": "import pytest\nfrom sqlalchemy import create_engine\nfrom sqlalchemy.orm import sessionmaker\nfrom models import User, Base\n\<EMAIL>\ndef db_session():\n    engine = create_engine('sqlite:///:memory:')\n    Base.metadata.create_all(engine)\n    Session = sessionmaker(bind=engine)\n    return Session()"
        }
        
        for filename, content in test_files.items():
            state.files[filename] = content
            
        # 模拟测试结果
        test_results = {
            "total_tests": 5,
            "passed": 5,
            "failed": 0,
            "coverage": 85.5
        }
        
        state.test_results.append(test_results)
        state.update_agent_status("tester", AgentStatus.COMPLETED)
        
        return state
        
    async def execute(self, initial_state: ProjectState,
                     config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行代码生成工作流
        
        Args:
            initial_state: 初始状态
            config: 配置选项
            
        Returns:
            执行结果
        """
        logger.info("Starting code generation workflow")
        
        try:
            if config is None:
                config = {"configurable": {"thread_id": initial_state.project_id}}
                
            final_state_data = self.graph.invoke(initial_state, config)

            # 确保我们处理的是ProjectState对象
            if isinstance(final_state_data, dict):
                final_state = ProjectState.from_dict(final_state_data)
            else:
                final_state = final_state_data

            return {
                "status": "success",
                "final_state": final_state.to_dict(),
                "project_id": final_state.project_id,
                "files_generated": len(final_state.files),
                "tests_created": len(final_state.test_results),
                "execution_time": str(datetime.now() - final_state.start_time)
            }
            
        except Exception as e:
            logger.error(f"Code generation workflow failed: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "project_id": initial_state.project_id
            }
            
    def get_workflow_info(self) -> Dict[str, Any]:
        """获取工作流信息"""
        return {
            "name": "CodeGenerationWorkflow",
            "nodes": ["analyze_requirements", "design_architecture", "generate_code", 
                     "review_code", "generate_tests", "refactor_code"],
            "description": "Code generation workflow with review and testing",
            "type": "sequential_with_feedback"
        }
