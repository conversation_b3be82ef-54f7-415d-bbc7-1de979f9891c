"""
TestProject_20250726_100441 测试用例
"""

import pytest
from fastapi.testclient import TestClient
from backend.main import app

client = TestClient(app)

def test_read_root():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200

def test_health_check():
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_get_items():
    """测试获取项目列表"""
    response = client.get("/api/items")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_create_item():
    """测试创建项目"""
    item_data = {
        "title": "测试项目",
        "description": "这是一个测试项目"
    }
    response = client.post("/api/items", json=item_data)
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "测试项目"
    assert data["completed"] == False

def test_create_item_without_description():
    """测试创建没有描述的项目"""
    item_data = {
        "title": "简单项目"
    }
    response = client.post("/api/items", json=item_data)
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "简单项目"
    assert data["description"] is None

if __name__ == "__main__":
    pytest.main([__file__])
