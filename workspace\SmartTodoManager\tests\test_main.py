"""
智能待办事项管理器 - 测试用例
"""
import pytest
from fastapi.testclient import TestClient
from backend.main import app

client = TestClient(app)

def test_root():
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Smart Todo Manager API"}

def test_create_todo():
    todo_data = {
        "title": "测试任务",
        "description": "这是一个测试任务",
        "priority": 2
    }
    response = client.post("/todos", json=todo_data)
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "测试任务"
    assert data["completed"] == False

def test_get_todos():
    response = client.get("/todos")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
