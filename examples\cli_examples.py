#!/usr/bin/env python3
"""
CLI使用示例
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.langgraph_system.cli.interactive import InteractiveCLI
from src.langgraph_system.cli.commands import ProjectCommands, AgentCommands
from src.langgraph_system.cli.config import get_config_manager
from src.langgraph_system.states.project_state import ProjectState, TaskType
from src.langgraph_system.graphs.project_workflow import ProjectWorkflow
from src.langgraph_system.agents.supervisor_agent import SupervisorAgent

async def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 初始化组件
    config_manager = get_config_manager()
    supervisor = SupervisorAgent(config_manager.get_llm_config())
    workflow = ProjectWorkflow()
    
    # 创建项目命令实例
    project_commands = ProjectCommands(workflow, supervisor)
    
    # 创建项目
    project_state = await project_commands.create_project(
        name="ExampleProject",
        task_type="development",
        description="这是一个示例项目"
    )
    
    print(f"项目创建成功: {project_state.project_name}")
    print(f"任务类型: {project_state.current_task}")
    print(f"项目ID: {project_state.project_id}")
    
    # 执行项目
    print("\n开始执行项目...")
    result = await project_commands.execute_project(project_state)
    
    if result['status'] == 'success':
        print("✅ 项目执行成功!")
        print(f"执行时间: {result['execution_time']}")
    else:
        print(f"❌ 项目执行失败: {result.get('error')}")

async def example_agent_management():
    """智能体管理示例"""
    print("\n=== 智能体管理示例 ===")
    
    config_manager = get_config_manager()
    supervisor = SupervisorAgent(config_manager.get_llm_config())
    
    # 创建智能体命令实例
    agent_commands = AgentCommands(supervisor)
    
    # 获取智能体列表
    agents = agent_commands.get_agent_list()
    print(f"可用智能体: {', '.join(agents)}")
    
    # 获取智能体能力
    capabilities = agent_commands.get_agent_capabilities()
    print("\n智能体能力:")
    for agent_name, info in capabilities.items():
        print(f"  {agent_name}: {info['description']}")
        print(f"    支持任务: {', '.join(info['supported_tasks'])}")
    
    # 创建任务计划
    plan = agent_commands.create_task_plan(
        TaskType.DEVELOPMENT,
        {"description": "开发Web应用"}
    )
    
    print("\n任务执行计划:")
    for step in plan:
        print(f"  步骤 {step['step']}: {step['action']} ({step['agent']})")
        print(f"    {step['description']}")

async def example_interactive_session():
    """交互式会话示例"""
    print("\n=== 交互式会话示例 ===")
    
    config_manager = get_config_manager()
    supervisor = SupervisorAgent(config_manager.get_llm_config())
    workflow = ProjectWorkflow()
    
    # 创建交互式CLI实例
    interactive_cli = InteractiveCLI(workflow, supervisor)
    
    # 创建示例项目
    project_state = ProjectState(
        project_name="InteractiveExample",
        current_task=TaskType.DEVELOPMENT,
        description="交互式示例项目",
        context={"description": "交互式示例项目"},
        execution_status="initialized"
    )
    
    print("启动交互式会话...")
    print("注意: 这将启动完整的交互式界面")
    print("在实际使用中，您可以输入命令与系统交互")
    
    # 在实际使用中，这里会启动交互式会话
    # await interactive_cli.start_interactive_session(project_state)

def example_config_management():
    """配置管理示例"""
    print("\n=== 配置管理示例 ===")
    
    # 获取配置管理器
    config_manager = get_config_manager()
    
    print("当前配置:")
    config_dict = config_manager.config.to_dict()
    for key, value in config_dict.items():
        print(f"  {key}: {value}")
    
    # 更新配置
    config_manager.update_config(
        verbose=True,
        max_messages=100,
        llm_temperature=0.8
    )
    
    print("\n更新后的配置:")
    print(f"  verbose: {config_manager.config.verbose}")
    print(f"  max_messages: {config_manager.config.max_messages}")
    print(f"  llm_temperature: {config_manager.config.llm_temperature}")
    
    # 保存配置
    if config_manager.save_config():
        print("✅ 配置保存成功")
    else:
        print("❌ 配置保存失败")

async def example_project_lifecycle():
    """完整项目生命周期示例"""
    print("\n=== 完整项目生命周期示例 ===")
    
    config_manager = get_config_manager()
    supervisor = SupervisorAgent(config_manager.get_llm_config())
    workflow = ProjectWorkflow()
    project_commands = ProjectCommands(workflow, supervisor)
    
    # 1. 创建项目
    print("1. 创建项目...")
    project_state = await project_commands.create_project(
        name="LifecycleExample",
        task_type="development",
        description="完整生命周期示例项目"
    )
    
    # 2. 保存项目状态
    print("2. 保存项目状态...")
    save_path = "lifecycle_example.json"
    if project_commands.save_project(project_state, save_path):
        print(f"✅ 项目状态已保存到: {save_path}")
    
    # 3. 加载项目状态
    print("3. 加载项目状态...")
    loaded_project = project_commands.load_project(save_path)
    if loaded_project:
        print(f"✅ 项目状态已加载: {loaded_project.project_name}")
    
    # 4. 执行项目
    print("4. 执行项目...")
    result = await project_commands.execute_project(loaded_project)
    
    if result['status'] == 'success':
        print("✅ 项目执行完成!")
        
        # 5. 保存最终状态
        final_state = ProjectState.from_dict(result['final_state'])
        final_save_path = "lifecycle_example_final.json"
        if project_commands.save_project(final_state, final_save_path):
            print(f"✅ 最终状态已保存到: {final_save_path}")
    else:
        print(f"❌ 项目执行失败: {result.get('error')}")
    
    # 清理临时文件
    try:
        Path(save_path).unlink(missing_ok=True)
        Path("lifecycle_example_final.json").unlink(missing_ok=True)
        print("🧹 临时文件已清理")
    except Exception:
        pass

def example_cli_utilities():
    """CLI工具函数示例"""
    print("\n=== CLI工具函数示例 ===")
    
    from src.langgraph_system.cli.utils import (
        CLIFormatter, CLIValidator, format_file_size, 
        truncate_string, safe_filename
    )
    from rich.console import Console
    
    console = Console()
    formatter = CLIFormatter(console)
    
    # 验证示例
    print("验证功能:")
    print(f"  项目名称 'MyProject' 有效: {CLIValidator.validate_project_name('MyProject')}")
    print(f"  项目名称 'My/Project' 有效: {CLIValidator.validate_project_name('My/Project')}")
    print(f"  任务类型 'development' 有效: {CLIValidator.validate_task_type('development')}")
    print(f"  任务类型 'invalid' 有效: {CLIValidator.validate_task_type('invalid')}")
    
    # 格式化示例
    print("\n格式化功能:")
    print(f"  文件大小 1024 字节: {format_file_size(1024)}")
    print(f"  文件大小 1048576 字节: {format_file_size(1048576)}")
    print(f"  截断字符串: {truncate_string('这是一个很长的字符串示例', 10)}")
    print(f"  安全文件名: {safe_filename('My Project: Version 1.0')}")

async def main():
    """主函数"""
    print("🤖 LangGraph CLI 使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        await example_basic_usage()
        await example_agent_management()
        await example_interactive_session()
        example_config_management()
        await example_project_lifecycle()
        example_cli_utilities()
        
        print("\n" + "=" * 50)
        print("✅ 所有示例执行完成!")
        print("\n要启动交互式CLI，请运行:")
        print("python -m src.langgraph_system.cli.cli_main interactive")
        
    except Exception as e:
        print(f"\n❌ 示例执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())