# 🌐 LangGraph多智能体系统 Web界面使用指南

## 📋 概述

LangGraph多智能体系统现在提供了一个功能完整的Streamlit Web界面，让用户可以通过浏览器直观地管理和操作多智能体系统。

## 🚀 启动Web界面

### 方法1: 使用启动脚本
```bash
python run_web_ui.py
```

### 方法2: 直接使用Streamlit
```bash
streamlit run src/langgraph_system/web/streamlit_app.py
```

启动后，Web界面将在浏览器中自动打开，默认地址为：`http://localhost:8501`

## 🎛️ 界面功能

### 1. 📊 系统状态页面

**功能概述**：
- 显示系统基本信息（名称、版本、状态）
- 展示可用智能体列表
- 显示工具统计信息
- 实时监控系统运行状态

**使用方法**：
1. 点击侧边栏的"🔄 初始化系统"按钮
2. 系统初始化成功后，状态页面会显示详细信息
3. 可以查看当前可用的智能体和工具数量

### 2. 🚀 创建项目页面

**功能概述**：
- 创建新的多智能体协作项目
- 支持多种任务类型选择
- 提供详细的项目配置选项

**使用步骤**：
1. 输入项目名称
2. 选择任务类型：
   - 🏗️ 架构设计
   - 💻 开发编码
   - 🔍 研究分析
   - 🧪 测试验证
   - 🐛 调试修复
   - 📚 文档编写
   - 🚀 部署发布
   - ⌨️ 代码实现
3. 填写项目描述和具体要求
4. 点击"🚀 创建项目"按钮
5. 查看执行结果和智能体协作过程

**示例项目**：
- **项目名称**: "计算器应用"
- **任务类型**: 开发编码
- **项目描述**: "创建一个简单的Python计算器应用"
- **具体要求**: "支持基本的四则运算，有用户友好的界面"

### 3. 🛠️ 工具管理页面

#### 3.1 📋 工具列表
- 查看所有可用工具
- 显示工具描述和参数信息
- 支持工具元数据查看

#### 3.2 🧪 工具测试
**文件写入测试**：
- 输入文件路径和内容
- 测试文件写入功能
- 实时查看执行结果

**文件读取测试**：
- 指定文件路径
- 读取并显示文件内容
- 支持多种文件格式

**文件列表测试**：
- 指定目录路径
- 列出目录中的所有文件
- 显示文件数量统计

#### 3.3 📊 工具统计
- 显示工具总数
- 按类型分类统计
- 工具使用情况分析

### 4. 🤖 智能体管理页面

#### 4.1 📋 智能体列表
- 查看所有注册的智能体
- 显示智能体能力和支持的任务类型
- 智能体元数据管理

#### 4.2 🎯 智能体测试
- 测试智能体功能（需要LLM API配置）
- 智能体性能监控
- 错误诊断和调试

### 5. 📚 任务历史页面

**功能特性**：
- 查看所有历史项目记录
- 显示项目执行结果
- 支持结果详情查看
- 任务执行时间统计

## 🎨 界面特色

### 美观的UI设计
- 现代化的界面风格
- 响应式布局设计
- 直观的图标和颜色系统
- 清晰的信息层次结构

### 实时状态反馈
- 成功/失败状态提示
- 详细的错误信息显示
- 进度指示器
- 实时日志输出

### 交互式操作
- 表单验证
- 动态内容更新
- 可展开的详情面板
- 多标签页组织

## 🔧 配置要求

### 系统要求
- Python 3.7+
- Streamlit
- LangGraph多智能体系统核心模块

### 环境配置
1. 确保`.env`文件中配置了正确的LLM API密钥
2. 安装所有必需的依赖包
3. 确保工作区目录权限正确

### LLM配置示例
```env
LLM_PROVIDER=moonshot
MOONSHOT_API_KEY=your_api_key_here
MOONSHOT_API_BASE=https://api.moonshot.cn/v1
```

## 🚨 故障排除

### 常见问题

**1. 系统初始化失败**
- 检查LLM API配置
- 确认网络连接
- 查看错误日志详情

**2. 工具执行失败**
- 检查文件路径权限
- 确认工作区目录存在
- 查看工具参数是否正确

**3. 智能体无响应**
- 检查LLM API密钥
- 确认API服务可用性
- 查看智能体注册状态

### 调试技巧
1. 查看浏览器控制台错误信息
2. 检查Streamlit终端输出
3. 使用工具测试功能验证系统状态
4. 查看任务历史中的详细错误信息

## 📈 性能优化

### 建议设置
- 使用现代浏览器（Chrome、Firefox、Edge）
- 确保足够的内存空间
- 稳定的网络连接
- 合理的LLM API调用频率

### 最佳实践
1. 定期清理任务历史
2. 合理设置项目描述长度
3. 避免同时运行多个大型项目
4. 定期检查系统状态

## 🔮 未来功能

### 计划中的功能
- 实时智能体协作可视化
- 项目模板管理
- 批量任务处理
- 性能监控面板
- 用户权限管理
- 项目导入/导出功能

### 扩展可能性
- 多用户支持
- 云端部署
- API接口集成
- 第三方插件支持

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 查看本指南的故障排除部分
2. 检查系统日志和错误信息
3. 确认环境配置正确
4. 联系技术支持团队

---

**版本**: v0.2.0  
**更新日期**: 2025-07-24  
**维护团队**: LangGraph开发团队