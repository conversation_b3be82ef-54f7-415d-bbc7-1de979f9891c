# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview
This is a LangGraph-based multi-agent collaboration system designed to simulate a software development team. It features dynamic agent loading, role-based workflows, swappable LLM backends (<PERSON><PERSON><PERSON>, Moonshot Kimi, <PERSON>throp<PERSON> Claude), and human-in-the-loop capabilities.

## Key Commands

### Development & Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Run web UI (recommended)
streamlit run app.py

# CLI usage
python -m src.langgraph_system.cli run --project-name "MyApp" --task "DEVELOPMENT" --description "Create a simple API"

# List available resources
python -m src.langgraph_system.cli list workflows
python -m src.langgraph_system.cli list agents
python -m src.langgraph_system.cli list tasks

# Test setup
python verify_setup.py
python test_config.py
```

### Environment Setup
Create `.env` file with:
```
LLM_PROVIDER="moonshot"  # or "openai", "anthropic"
MOONSHOT_API_KEY="sk-..."
OPENAI_API_KEY="sk-..."
ANTHROPIC_API_KEY="sk-..."
```

## Architecture

### Core Components
- **Agents**: Located in `src/langgraph_system/agents/` - dynamically loaded at runtime
- **Graphs**: Workflow definitions in `src/langgraph_system/graphs/`
- **LLM Clients**: Provider-specific implementations in `src/langgraph_system/llm/`
- **States**: Project state management in `src/langgraph_system/states/`
- **Tools**: File system and utility tools in `src/langgraph_system/tools/`

### Key Workflows
1. **ProjectWorkflow** (`src/langgraph_system/graphs/project_workflow.py`): Main orchestration graph
2. **CodeGenerationWorkflow** (`src/langgraph_system/graphs/code_generation.py`): Specialized for code generation

### Agent Types
- **SupervisorAgent**: Central coordinator that routes tasks
- **CoderAgent**: Code generation and modification
- **ResearcherAgent**: Information gathering and analysis
- Additional agents are loaded dynamically from `src/langgraph_system/agents/`

### State Management
- **ProjectState**: Central state object tracking project progress, agent status, messages, and tool results
- **TaskType**: Enum defining task categories (DEVELOPMENT, RESEARCH, ARCHITECTURE, etc.)
- **AgentStatus**: Enum tracking agent execution states

### File System
- **Workspace**: All agent operations restricted to `workspace/` directory
- **Tools**: File operations use `src/langgraph_system/tools/file_system_tools.py`

## Development Notes

### Adding New Agents
1. Create new agent class in `src/langgraph_system/agents/`
2. Inherit from `LangGraphAgentAdapter` or `SimpleAgentAdapter`
3. Implement `process()` method
4. Agent will be auto-discovered at runtime

### LLM Provider Switching
- Configure via `LLM_PROVIDER` in `.env`
- Implement new providers by extending `BaseLLMClient` in `src/langgraph_system/llm/`

### Key Entry Points
- **Web UI**: `app.py` - Streamlit interface
- **CLI**: `src/main.py` - Command-line interface
- **Core**: `src/langgraph_system/graphs/project_workflow.py` - Main workflow engine