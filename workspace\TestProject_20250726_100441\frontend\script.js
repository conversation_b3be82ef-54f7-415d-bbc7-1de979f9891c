// 应用状态
let items = [];

// DOM元素
const itemForm = document.getElementById('itemForm');
const titleInput = document.getElementById('title');
const descriptionInput = document.getElementById('description');
const itemsList = document.getElementById('itemsList');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    loadItems();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    itemForm.addEventListener('submit', handleAddItem);
}

// 加载项目列表
async function loadItems() {
    try {
        const response = await fetch('/api/items');
        if (response.ok) {
            items = await response.json();
            renderItems();
        } else {
            console.error('Failed to load items');
        }
    } catch (error) {
        console.error('Error loading items:', error);
    }
}

// 渲染项目列表
function renderItems() {
    if (items.length === 0) {
        itemsList.innerHTML = '<p>暂无项目</p>';
        return;
    }
    
    const itemsHTML = items.map(item => `
        <div class="item ${item.completed ? 'completed' : ''}">
            <div class="item-title">${escapeHtml(item.title)}</div>
            <div class="item-description">${escapeHtml(item.description || '')}</div>
            <div class="item-actions">
                <button class="btn-small btn-success" onclick="toggleItem(${item.id}, ${!item.completed})">
                    ${item.completed ? '取消完成' : '标记完成'}
                </button>
                <button class="btn-small btn-danger" onclick="deleteItem(${item.id})">
                    删除
                </button>
            </div>
        </div>
    `).join('');
    
    itemsList.innerHTML = itemsHTML;
}

// 添加新项目
async function handleAddItem(event) {
    event.preventDefault();
    
    const title = titleInput.value.trim();
    const description = descriptionInput.value.trim();
    
    if (!title) {
        alert('请输入项目标题');
        return;
    }
    
    try {
        const response = await fetch('/api/items', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                title: title,
                description: description
            })
        });
        
        if (response.ok) {
            titleInput.value = '';
            descriptionInput.value = '';
            loadItems(); // 重新加载列表
        } else {
            alert('添加项目失败');
        }
    } catch (error) {
        console.error('Error adding item:', error);
        alert('添加项目时发生错误');
    }
}

// 切换项目状态
async function toggleItem(itemId, completed) {
    try {
        const response = await fetch(`/api/items/${itemId}?completed=${completed}`, {
            method: 'PUT'
        });
        
        if (response.ok) {
            loadItems(); // 重新加载列表
        } else {
            alert('更新项目状态失败');
        }
    } catch (error) {
        console.error('Error updating item:', error);
        alert('更新项目时发生错误');
    }
}

// 删除项目
async function deleteItem(itemId) {
    if (!confirm('确定要删除这个项目吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/items/${itemId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            loadItems(); // 重新加载列表
        } else {
            alert('删除项目失败');
        }
    } catch (error) {
        console.error('Error deleting item:', error);
        alert('删除项目时发生错误');
    }
}

// HTML转义函数
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}