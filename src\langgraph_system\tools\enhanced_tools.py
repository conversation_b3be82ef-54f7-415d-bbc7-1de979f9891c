"""
增强的工具集 - 提供更丰富的功能
"""

import os
import subprocess
import json
import yaml
from typing import Dict, Any, List, Optional
from pathlib import Path
import logging

from ..core.registry import register_tool
from ..core.exceptions import ToolError

logger = logging.getLogger(__name__)

# 工作区配置
WORKSPACE_DIR = Path(__file__).parent.parent.parent.parent / "workspace"
WORKSPACE_DIR.mkdir(exist_ok=True)


def _get_safe_path(file_path: str) -> Path:
    """获取安全的文件路径"""
    abs_path = (WORKSPACE_DIR / file_path).resolve()
    if not str(abs_path).startswith(str(WORKSPACE_DIR.resolve())):
        raise ToolError("file_system", f"路径 '{file_path}' 超出工作区范围", "PATH_TRAVERSAL")
    return abs_path


@register_tool("read_file", {
    "description": "读取文件内容",
    "parameters": {
        "file_path": {"type": "string", "description": "文件路径（相对于工作区）"}
    }
})
def read_file(file_path: str) -> str:
    """读取文件内容"""
    try:
        safe_path = _get_safe_path(file_path)
        with open(safe_path, "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        raise ToolError("read_file", f"文件 '{file_path}' 不存在", "FILE_NOT_FOUND")
    except Exception as e:
        raise ToolError("read_file", f"读取文件失败: {str(e)}", "READ_ERROR")


@register_tool("write_file", {
    "description": "写入文件内容",
    "parameters": {
        "file_path": {"type": "string", "description": "文件路径"},
        "content": {"type": "string", "description": "文件内容"}
    }
})
def write_file(file_path: str, content: str) -> str:
    """写入文件内容"""
    try:
        safe_path = _get_safe_path(file_path)
        safe_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(safe_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        logger.info(f"文件已写入: {file_path}")
        return f"文件 '{file_path}' 写入成功"
    except Exception as e:
        raise ToolError("write_file", f"写入文件失败: {str(e)}", "WRITE_ERROR")


@register_tool("list_files", {
    "description": "列出目录中的文件",
    "parameters": {
        "directory_path": {"type": "string", "description": "目录路径", "default": "."}
    }
})
def list_files(directory_path: str = ".") -> List[str]:
    """列出目录文件"""
    try:
        safe_path = _get_safe_path(directory_path)
        if not safe_path.is_dir():
            raise ToolError("list_files", f"'{directory_path}' 不是目录", "NOT_DIRECTORY")
        
        return [item.name for item in safe_path.iterdir()]
    except Exception as e:
        raise ToolError("list_files", f"列出文件失败: {str(e)}", "LIST_ERROR")


@register_tool("create_directory", {
    "description": "创建目录",
    "parameters": {
        "directory_path": {"type": "string", "description": "目录路径"}
    }
})
def create_directory(directory_path: str) -> str:
    """创建目录"""
    try:
        safe_path = _get_safe_path(directory_path)
        safe_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"目录已创建: {directory_path}")
        return f"目录 '{directory_path}' 创建成功"
    except Exception as e:
        raise ToolError("create_directory", f"创建目录失败: {str(e)}", "CREATE_DIR_ERROR")


@register_tool("delete_file", {
    "description": "删除文件",
    "parameters": {
        "file_path": {"type": "string", "description": "文件路径"}
    }
})
def delete_file(file_path: str) -> str:
    """删除文件"""
    try:
        safe_path = _get_safe_path(file_path)
        if not safe_path.exists():
            raise ToolError("delete_file", f"文件 '{file_path}' 不存在", "FILE_NOT_FOUND")
        
        safe_path.unlink()
        logger.info(f"文件已删除: {file_path}")
        return f"文件 '{file_path}' 删除成功"
    except Exception as e:
        raise ToolError("delete_file", f"删除文件失败: {str(e)}", "DELETE_ERROR")


@register_tool("read_json", {
    "description": "读取JSON文件",
    "parameters": {
        "file_path": {"type": "string", "description": "JSON文件路径"}
    }
})
def read_json(file_path: str) -> Dict[str, Any]:
    """读取JSON文件"""
    try:
        content = read_file(file_path)
        return json.loads(content)
    except json.JSONDecodeError as e:
        raise ToolError("read_json", f"JSON格式错误: {str(e)}", "JSON_DECODE_ERROR")


@register_tool("write_json", {
    "description": "写入JSON文件",
    "parameters": {
        "file_path": {"type": "string", "description": "JSON文件路径"},
        "data": {"type": "object", "description": "要写入的数据"}
    }
})
def write_json(file_path: str, data: Dict[str, Any]) -> str:
    """写入JSON文件"""
    try:
        content = json.dumps(data, ensure_ascii=False, indent=2)
        return write_file(file_path, content)
    except Exception as e:
        raise ToolError("write_json", f"写入JSON失败: {str(e)}", "JSON_WRITE_ERROR")


@register_tool("execute_command", {
    "description": "执行系统命令（限制在工作区内）",
    "parameters": {
        "command": {"type": "string", "description": "要执行的命令"},
        "timeout": {"type": "integer", "description": "超时时间（秒）", "default": 30}
    }
})
def execute_command(command: str, timeout: int = 30) -> Dict[str, Any]:
    """执行系统命令"""
    try:
        # 安全检查：禁止危险命令
        dangerous_commands = ["rm", "del", "format", "fdisk", "mkfs", "dd"]
        if any(cmd in command.lower() for cmd in dangerous_commands):
            raise ToolError("execute_command", "禁止执行危险命令", "DANGEROUS_COMMAND")
        
        result = subprocess.run(
            command,
            shell=True,
            cwd=str(WORKSPACE_DIR),
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        return {
            "returncode": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "success": result.returncode == 0
        }
    except subprocess.TimeoutExpired:
        raise ToolError("execute_command", f"命令执行超时（{timeout}秒）", "COMMAND_TIMEOUT")
    except Exception as e:
        raise ToolError("execute_command", f"命令执行失败: {str(e)}", "COMMAND_ERROR")


@register_tool("search_files", {
    "description": "在文件中搜索文本",
    "parameters": {
        "pattern": {"type": "string", "description": "搜索模式"},
        "file_pattern": {"type": "string", "description": "文件模式", "default": "*.py"},
        "directory": {"type": "string", "description": "搜索目录", "default": "."}
    }
})
def search_files(pattern: str, file_pattern: str = "*.py", directory: str = ".") -> List[Dict[str, Any]]:
    """在文件中搜索文本"""
    try:
        import re
        from fnmatch import fnmatch
        
        safe_dir = _get_safe_path(directory)
        results = []
        
        for file_path in safe_dir.rglob(file_pattern):
            if file_path.is_file():
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        for line_num, line in enumerate(f, 1):
                            if re.search(pattern, line, re.IGNORECASE):
                                results.append({
                                    "file": str(file_path.relative_to(WORKSPACE_DIR)),
                                    "line": line_num,
                                    "content": line.strip()
                                })
                except Exception:
                    continue  # 跳过无法读取的文件
        
        return results
    except Exception as e:
        raise ToolError("search_files", f"搜索失败: {str(e)}", "SEARCH_ERROR")


@register_tool("get_file_info", {
    "description": "获取文件信息",
    "parameters": {
        "file_path": {"type": "string", "description": "文件路径"}
    }
})
def get_file_info(file_path: str) -> Dict[str, Any]:
    """获取文件信息"""
    try:
        safe_path = _get_safe_path(file_path)
        if not safe_path.exists():
            raise ToolError("get_file_info", f"文件 '{file_path}' 不存在", "FILE_NOT_FOUND")
        
        stat = safe_path.stat()
        return {
            "name": safe_path.name,
            "size": stat.st_size,
            "modified": stat.st_mtime,
            "is_file": safe_path.is_file(),
            "is_directory": safe_path.is_dir(),
            "extension": safe_path.suffix
        }
    except Exception as e:
        raise ToolError("get_file_info", f"获取文件信息失败: {str(e)}", "INFO_ERROR")