#!/usr/bin/env python3
"""
LangGraph多智能体系统基本使用示例
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from langgraph_system import ProjectState, TaskType
from langgraph_system.graphs import ProjectWorkflow, CodeGenerationWorkflow

async def basic_project_example():
    """基本项目示例"""
    print("=== 基本项目示例 ===")
    
    workflow = ProjectWorkflow()
    
    # 创建项目状态
    state = ProjectState(
        project_name="MyFirstProject",
        current_task=TaskType.ARCHITECTURE,
        context={
            "description": "A simple web API for user management",
            "requirements": ["user registration", "login", "profile management"]
        }
    )
    
    # 执行工作流
    result = await workflow.execute(state)
    
    if result["status"] == "success":
        print(f"✅ 项目成功完成")
        print(f"📋 项目ID: {result['project_id']}")
        print(f"⏱️  执行时间: {result['execution_time']}")
        
        final_state = result["final_state"]
        print(f"💬 消息数量: {len(final_state.messages)}")
        print(f"📁 文件数量: {len(final_state.files)}")
        print(f"🔍 工件数量: {len(final_state.artifacts)}")
        
    else:
        print(f"❌ 执行失败: {result['error']}")

async def code_generation_example():
    """代码生成示例"""
    print("\n=== 代码生成示例 ===")
    
    workflow = CodeGenerationWorkflow()
    
    # 创建项目状态
    state = ProjectState(
        project_name="TodoAPI",
        context={
            "type": "REST API",
            "features": ["create todo", "list todos", "update status", "delete todo"],
            "tech_stack": ["FastAPI", "SQLite", "Pydantic"]
        }
    )
    
    # 执行代码生成工作流
    result = await workflow.execute(state)
    
    if result["status"] == "success":
        print(f"✅ 代码生成完成")
        print(f"📋 项目ID: {result['project_id']}")
        print(f"📁 生成文件: {result['files_generated']}")
        print(f"🧪 测试用例: {result['tests_created']}")
        print(f"⏱️  执行时间: {result['execution_time']}")
        
        final_state = result["final_state"]
        print("\n📄 生成的文件:")
        for filename in final_state.files.keys():
            print(f"   - {filename}")
            
    else:
        print(f"❌ 代码生成失败: {result['error']}")

async def multi_task_example():
    """多任务示例"""
    print("\n=== 多任务示例 ===")
    
    workflow = ProjectWorkflow()
    
    # 任务序列
    tasks = [
        TaskType.ARCHITECTURE,
        TaskType.DEVELOPMENT,
        TaskType.REVIEW,
        TaskType.TESTING
    ]
    
    project_id = None
    
    for task in tasks:
        print(f"\n🔄 执行任务: {task.value}")
        
        state = ProjectState(
            project_name="MultiTaskProject",
            current_task=task,
            project_id=project_id  # 保持同一个项目
        )
        
        result = await workflow.execute(state)
        
        if result["status"] == "success":
            print(f"✅ {task.value} 完成")
            project_id = result["project_id"]
        else:
            print(f"❌ {task.value} 失败: {result['error']}")
            break

async def main():
    """主函数"""
    print("🚀 LangGraph多智能体系统示例")
    print("=" * 50)
    
    try:
        await basic_project_example()
        await code_generation_example()
        await multi_task_example()
        
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
