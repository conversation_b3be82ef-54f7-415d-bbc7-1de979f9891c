"""
Redis配置管理器
支持多种Redis连接模式：本地、<PERSON><PERSON>、云服务、内存模拟
"""

import os
import asyncio
import logging
from typing import Optional, Dict, Any, Union
from enum import Enum
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)


class RedisMode(Enum):
    """Redis运行模式"""
    LOCAL = "local"           # 本地Redis服务
    DOCKER = "docker"         # Docker容器Redis
    CLOUD = "cloud"           # 云Redis服务
    MEMORY = "memory"         # 内存模拟（开发/测试）
    DISABLED = "disabled"     # 禁用Redis


@dataclass
class RedisConfig:
    """Redis配置"""
    mode: RedisMode = RedisMode.MEMORY
    host: str = "localhost"
    port: int = 6379
    password: Optional[str] = None
    db: int = 0
    url: Optional[str] = None
    max_connections: int = 20
    timeout: int = 30
    retry_attempts: int = 3
    
    def get_redis_url(self) -> str:
        """获取Redis连接URL"""
        if self.url:
            return self.url
        
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        else:
            return f"redis://{self.host}:{self.port}/{self.db}"


class MemoryRedis:
    """内存模拟Redis - 用于开发和测试"""
    
    def __init__(self):
        self._data: Dict[str, Any] = {}
        self._ttl: Dict[str, float] = {}
        self._connected = True
    
    async def ping(self) -> bool:
        """模拟ping"""
        return True
    
    async def get(self, key: str) -> Optional[bytes]:
        """获取值"""
        if key in self._data:
            # 检查TTL
            if key in self._ttl:
                import time
                if time.time() > self._ttl[key]:
                    del self._data[key]
                    del self._ttl[key]
                    return None
            
            value = self._data[key]
            if isinstance(value, str):
                return value.encode('utf-8')
            return value
        return None
    
    async def set(self, key: str, value: Union[str, bytes], ex: Optional[int] = None) -> bool:
        """设置值"""
        if isinstance(value, bytes):
            self._data[key] = value
        else:
            self._data[key] = str(value).encode('utf-8')
        
        if ex:
            import time
            self._ttl[key] = time.time() + ex
        
        return True
    
    async def delete(self, key: str) -> int:
        """删除键"""
        if key in self._data:
            del self._data[key]
            if key in self._ttl:
                del self._ttl[key]
            return 1
        return 0
    
    async def exists(self, key: str) -> int:
        """检查键是否存在"""
        return 1 if key in self._data else 0
    
    async def keys(self, pattern: str = "*") -> list:
        """获取匹配的键"""
        if pattern == "*":
            return list(self._data.keys())
        
        # 简单的模式匹配
        import fnmatch
        return [key for key in self._data.keys() if fnmatch.fnmatch(key, pattern)]
    
    async def close(self):
        """关闭连接"""
        self._connected = False


class RedisManager:
    """Redis管理器"""
    
    def __init__(self, config: Optional[RedisConfig] = None):
        self.config = config or self._load_config()
        self.redis: Optional[Union['aioredis.Redis', MemoryRedis]] = None
        self._connected = False
    
    def _load_config(self) -> RedisConfig:
        """从环境变量加载配置"""
        mode_str = os.getenv("REDIS_MODE", "memory").lower()
        try:
            mode = RedisMode(mode_str)
        except ValueError:
            logger.warning(f"无效的Redis模式: {mode_str}，使用内存模式")
            mode = RedisMode.MEMORY
        
        return RedisConfig(
            mode=mode,
            host=os.getenv("REDIS_HOST", "localhost"),
            port=int(os.getenv("REDIS_PORT", "6379")),
            password=os.getenv("REDIS_PASSWORD"),
            db=int(os.getenv("REDIS_DB", "0")),
            url=os.getenv("REDIS_URL"),
            max_connections=int(os.getenv("REDIS_MAX_CONNECTIONS", "20")),
            timeout=int(os.getenv("REDIS_TIMEOUT", "30")),
            retry_attempts=int(os.getenv("REDIS_RETRY_ATTEMPTS", "3"))
        )
    
    async def connect(self) -> bool:
        """连接Redis"""
        if self._connected:
            return True
        
        try:
            if self.config.mode == RedisMode.DISABLED:
                logger.info("Redis已禁用")
                return False
            
            elif self.config.mode == RedisMode.MEMORY:
                logger.info("使用内存模拟Redis")
                self.redis = MemoryRedis()
                self._connected = True
                return True
            
            else:
                # 尝试连接真实Redis
                await self._connect_real_redis()
                return True
                
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            # 降级到内存模式
            logger.info("降级到内存模拟Redis")
            self.redis = MemoryRedis()
            self._connected = True
            return True
    
    async def _connect_real_redis(self):
        """连接真实Redis"""
        try:
            import aioredis
        except ImportError:
            raise ImportError("需要安装aioredis: pip install aioredis>=2.0.0")
        
        redis_url = self.config.get_redis_url()
        logger.info(f"连接Redis: {redis_url}")
        
        for attempt in range(self.config.retry_attempts):
            try:
                self.redis = aioredis.from_url(
                    redis_url,
                    encoding="utf-8",
                    decode_responses=False,
                    max_connections=self.config.max_connections,
                    socket_timeout=self.config.timeout
                )
                
                # 测试连接
                await asyncio.wait_for(self.redis.ping(), timeout=self.config.timeout)
                logger.info("Redis连接成功")
                self._connected = True
                return
                
            except Exception as e:
                logger.warning(f"Redis连接尝试 {attempt + 1}/{self.config.retry_attempts} 失败: {e}")
                if attempt < self.config.retry_attempts - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                else:
                    raise
    
    async def disconnect(self):
        """断开连接"""
        if self.redis and self._connected:
            try:
                if hasattr(self.redis, 'close'):
                    await self.redis.close()
                elif hasattr(self.redis, 'aclose'):
                    await self.redis.aclose()
            except Exception as e:
                logger.error(f"Redis断开连接失败: {e}")
            finally:
                self._connected = False
                self.redis = None
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected and self.redis is not None
    
    def get_client(self):
        """获取Redis客户端"""
        if not self.is_connected():
            raise RuntimeError("Redis未连接，请先调用connect()")
        return self.redis
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        if not self.is_connected():
            return {
                "status": "disconnected",
                "mode": self.config.mode.value,
                "message": "Redis未连接"
            }
        
        try:
            await self.redis.ping()
            return {
                "status": "healthy",
                "mode": self.config.mode.value,
                "config": {
                    "host": self.config.host,
                    "port": self.config.port,
                    "db": self.config.db
                }
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "mode": self.config.mode.value,
                "error": str(e)
            }


# 全局Redis管理器实例
_redis_manager: Optional[RedisManager] = None


def get_redis_manager() -> RedisManager:
    """获取Redis管理器实例"""
    global _redis_manager
    if _redis_manager is None:
        _redis_manager = RedisManager()
    return _redis_manager


async def get_redis_client():
    """获取Redis客户端"""
    manager = get_redis_manager()
    if not manager.is_connected():
        await manager.connect()
    return manager.get_client()


# 便捷函数
async def setup_redis(config: Optional[RedisConfig] = None) -> RedisManager:
    """设置Redis连接"""
    global _redis_manager
    _redis_manager = RedisManager(config)
    await _redis_manager.connect()
    return _redis_manager


async def cleanup_redis():
    """清理Redis连接"""
    global _redis_manager
    if _redis_manager:
        await _redis_manager.disconnect()
        _redis_manager = None
