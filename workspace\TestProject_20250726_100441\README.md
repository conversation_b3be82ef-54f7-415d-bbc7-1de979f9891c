# TestProject_20250726_100441

测试项目 - 验证文件创建功能

## 技术栈

- **后端**: FastAPI
- **前端**: HTML/CSS/JavaScript
- **数据库**: SQLite
- **认证**: JWT

## 快速开始

1. 安装依赖:
   ```bash
   pip install -r requirements.txt
   ```

2. 启动后端:
   ```bash
   python backend/main.py
   ```

3. 访问应用:
   ```
   http://localhost:8000
   ```

## 项目结构

```
TestProject_20250726_100441/
├── backend/          # 后端代码
├── frontend/         # 前端代码
├── tests/           # 测试代码
├── docs/            # 文档
├── config/          # 配置文件
└── scripts/         # 脚本文件
```

## 开发指南

请查看 [docs/DEVELOPMENT.md](docs/DEVELOPMENT.md) 获取详细的开发指南。

---

*项目创建时间: 2025-07-26 10:04:41*
