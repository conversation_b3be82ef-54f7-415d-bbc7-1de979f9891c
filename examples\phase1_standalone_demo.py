#!/usr/bin/env python3
"""
LangGraph多智能体系统 v0.3 第一阶段独立演示
不依赖Redis，展示核心基础设施功能
"""

import asyncio
import time
import json
from typing import Dict, Any, List
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ============================================================================
# 简化的状态管理器演示
# ============================================================================

@dataclass
class StateSnapshot:
    """状态快照"""
    version: int
    timestamp: float
    data: Dict[str, Any]
    checksum: str

class SimpleStateManager:
    """简化的状态管理器（内存版本）"""
    
    def __init__(self):
        self.states: Dict[str, Any] = {}
        self.versions: Dict[str, List[StateSnapshot]] = {}
        self.current_version: Dict[str, int] = {}
        
    async def set_state(self, key: str, value: Any) -> bool:
        """设置状态"""
        try:
            # 创建快照
            version = self.current_version.get(key, 0) + 1
            snapshot = StateSnapshot(
                version=version,
                timestamp=time.time(),
                data={"value": value},
                checksum=str(hash(str(value)))
            )
            
            # 保存状态和版本历史
            self.states[key] = value
            if key not in self.versions:
                self.versions[key] = []
            self.versions[key].append(snapshot)
            self.current_version[key] = version
            
            logger.info(f"状态已设置: {key} = {value} (版本: {version})")
            return True
        except Exception as e:
            logger.error(f"设置状态失败: {e}")
            return False
    
    async def get_state(self, key: str) -> Any:
        """获取状态"""
        return self.states.get(key)
    
    async def get_version_history(self, key: str) -> List[StateSnapshot]:
        """获取版本历史"""
        return self.versions.get(key, [])

# ============================================================================
# 简化的缓存管理器演示
# ============================================================================

@dataclass
class CacheEntry:
    """缓存条目"""
    value: Any
    timestamp: float
    access_count: int
    ttl: float

class SimpleCacheManager:
    """简化的缓存管理器（内存版本）"""
    
    def __init__(self, max_size: int = 100, default_ttl: float = 3600):
        self.cache: Dict[str, CacheEntry] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.stats = {"hits": 0, "misses": 0, "evictions": 0}
    
    async def get(self, key: str) -> Any:
        """获取缓存"""
        if key in self.cache:
            entry = self.cache[key]
            # 检查TTL
            if time.time() - entry.timestamp < entry.ttl:
                entry.access_count += 1
                self.stats["hits"] += 1
                logger.info(f"缓存命中: {key}")
                return entry.value
            else:
                # 过期删除
                del self.cache[key]
        
        self.stats["misses"] += 1
        logger.info(f"缓存未命中: {key}")
        return None
    
    async def set(self, key: str, value: Any, ttl: float = None) -> bool:
        """设置缓存"""
        try:
            # 检查容量
            if len(self.cache) >= self.max_size and key not in self.cache:
                await self._evict_lru()
            
            # 设置缓存
            self.cache[key] = CacheEntry(
                value=value,
                timestamp=time.time(),
                access_count=1,
                ttl=ttl or self.default_ttl
            )
            logger.info(f"缓存已设置: {key}")
            return True
        except Exception as e:
            logger.error(f"设置缓存失败: {e}")
            return False
    
    async def _evict_lru(self):
        """LRU驱逐策略"""
        if not self.cache:
            return
        
        # 找到最少使用的条目
        lru_key = min(self.cache.keys(), 
                     key=lambda k: self.cache[k].access_count)
        del self.cache[lru_key]
        self.stats["evictions"] += 1
        logger.info(f"LRU驱逐: {lru_key}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = self.stats["hits"] / total_requests if total_requests > 0 else 0
        return {
            **self.stats,
            "hit_rate": hit_rate,
            "cache_size": len(self.cache)
        }

# ============================================================================
# 简化的任务调度器演示
# ============================================================================

@dataclass
class Task:
    """任务定义"""
    id: str
    name: str
    priority: int
    func: callable
    args: tuple = ()
    kwargs: dict = None
    dependencies: List[str] = None
    status: str = "pending"  # pending, running, completed, failed
    result: Any = None
    error: str = None
    created_at: float = None
    started_at: float = None
    completed_at: float = None
    
    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}
        if self.dependencies is None:
            self.dependencies = []
        if self.created_at is None:
            self.created_at = time.time()

class SimpleTaskScheduler:
    """简化的任务调度器"""
    
    def __init__(self, max_workers: int = 5):
        self.tasks: Dict[str, Task] = {}
        self.task_queue: List[str] = []
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.max_workers = max_workers
        self.stats = {"submitted": 0, "completed": 0, "failed": 0}
    
    async def submit_task(self, task: Task) -> str:
        """提交任务"""
        self.tasks[task.id] = task
        self.task_queue.append(task.id)
        self.stats["submitted"] += 1
        logger.info(f"任务已提交: {task.name} (ID: {task.id}, 优先级: {task.priority})")
        return task.id
    
    async def start_scheduler(self):
        """启动调度器"""
        logger.info("任务调度器已启动")
        while True:
            await self._process_queue()
            await asyncio.sleep(0.1)
    
    async def _process_queue(self):
        """处理任务队列"""
        # 检查是否有可用的工作器
        if len(self.running_tasks) >= self.max_workers:
            return
        
        # 按优先级排序任务队列
        available_tasks = []
        for task_id in self.task_queue:
            task = self.tasks[task_id]
            if task.status == "pending" and self._dependencies_satisfied(task):
                available_tasks.append((task.priority, task_id))
        
        if not available_tasks:
            return
        
        # 选择最高优先级的任务
        available_tasks.sort(reverse=True)  # 高优先级在前
        _, task_id = available_tasks[0]
        
        # 启动任务
        await self._execute_task(task_id)
    
    def _dependencies_satisfied(self, task: Task) -> bool:
        """检查任务依赖是否满足"""
        for dep_id in task.dependencies:
            if dep_id not in self.tasks:
                return False
            if self.tasks[dep_id].status != "completed":
                return False
        return True
    
    async def _execute_task(self, task_id: str):
        """执行任务"""
        task = self.tasks[task_id]
        task.status = "running"
        task.started_at = time.time()
        
        logger.info(f"开始执行任务: {task.name}")
        
        async def task_wrapper():
            try:
                if asyncio.iscoroutinefunction(task.func):
                    result = await task.func(*task.args, **task.kwargs)
                else:
                    result = task.func(*task.args, **task.kwargs)
                
                task.result = result
                task.status = "completed"
                task.completed_at = time.time()
                self.stats["completed"] += 1
                logger.info(f"任务完成: {task.name}")
                
            except Exception as e:
                task.error = str(e)
                task.status = "failed"
                task.completed_at = time.time()
                self.stats["failed"] += 1
                logger.error(f"任务失败: {task.name} - {e}")
            
            finally:
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]
                if task_id in self.task_queue:
                    self.task_queue.remove(task_id)
        
        # 启动任务
        self.running_tasks[task_id] = asyncio.create_task(task_wrapper())
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "running": len(self.running_tasks),
            "queued": len([t for t in self.tasks.values() if t.status == "pending"])
        }

# ============================================================================
# 简化的性能监控器演示
# ============================================================================

class SimplePerformanceMonitor:
    """简化的性能监控器"""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self.counters: Dict[str, int] = {}
        self.start_time = time.time()
    
    def record_metric(self, name: str, value: float):
        """记录指标"""
        if name not in self.metrics:
            self.metrics[name] = []
        self.metrics[name].append(value)
        logger.info(f"指标记录: {name} = {value}")
    
    def increment_counter(self, name: str, value: int = 1):
        """增加计数器"""
        self.counters[name] = self.counters.get(name, 0) + value
        logger.info(f"计数器增加: {name} = {self.counters[name]}")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        summary = {
            "uptime": time.time() - self.start_time,
            "counters": self.counters.copy(),
            "metrics": {}
        }
        
        for name, values in self.metrics.items():
            if values:
                summary["metrics"][name] = {
                    "count": len(values),
                    "avg": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values),
                    "latest": values[-1]
                }
        
        return summary

# ============================================================================
# 演示函数
# ============================================================================

async def demo_state_management():
    """演示状态管理功能"""
    print("\n" + "="*60)
    print("🗃️  状态管理器演示")
    print("="*60)
    
    state_manager = SimpleStateManager()
    
    # 设置一些状态
    await state_manager.set_state("user_count", 100)
    await state_manager.set_state("system_status", "running")
    await state_manager.set_state("user_count", 150)  # 更新状态
    
    # 获取状态
    user_count = await state_manager.get_state("user_count")
    system_status = await state_manager.get_state("system_status")
    
    print(f"当前用户数: {user_count}")
    print(f"系统状态: {system_status}")
    
    # 查看版本历史
    history = await state_manager.get_version_history("user_count")
    print(f"用户数版本历史: {len(history)} 个版本")
    for snapshot in history:
        print(f"  版本 {snapshot.version}: {snapshot.data['value']} (时间: {datetime.fromtimestamp(snapshot.timestamp).strftime('%H:%M:%S')})")

async def demo_cache_management():
    """演示缓存管理功能"""
    print("\n" + "="*60)
    print("🚀 缓存管理器演示")
    print("="*60)
    
    cache_manager = SimpleCacheManager(max_size=5, default_ttl=10)
    
    # 设置一些缓存
    await cache_manager.set("user:1", {"name": "Alice", "age": 30})
    await cache_manager.set("user:2", {"name": "Bob", "age": 25})
    await cache_manager.set("config:app", {"theme": "dark", "lang": "zh"})
    
    # 获取缓存
    user1 = await cache_manager.get("user:1")
    user3 = await cache_manager.get("user:3")  # 不存在
    
    print(f"用户1信息: {user1}")
    print(f"用户3信息: {user3}")
    
    # 填满缓存触发LRU
    for i in range(3, 8):
        await cache_manager.set(f"temp:{i}", f"data_{i}")
    
    # 查看统计
    stats = cache_manager.get_stats()
    print(f"缓存统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")

async def demo_task_scheduling():
    """演示任务调度功能"""
    print("\n" + "="*60)
    print("⚡ 任务调度器演示")
    print("="*60)
    
    scheduler = SimpleTaskScheduler(max_workers=3)
    
    # 定义一些示例任务
    async def async_task(name: str, duration: float):
        print(f"  执行异步任务: {name}")
        await asyncio.sleep(duration)
        return f"任务 {name} 完成"
    
    def sync_task(name: str, value: int):
        print(f"  执行同步任务: {name}")
        time.sleep(0.1)  # 模拟工作
        return value * 2
    
    # 提交任务
    tasks = [
        Task("task1", "高优先级任务", 10, async_task, ("高优先级", 0.5)),
        Task("task2", "普通任务A", 5, sync_task, ("普通A", 10)),
        Task("task3", "普通任务B", 5, sync_task, ("普通B", 20)),
        Task("task4", "低优先级任务", 1, async_task, ("低优先级", 0.3)),
        Task("task5", "依赖任务", 8, sync_task, ("依赖", 30), dependencies=["task2"])
    ]
    
    for task in tasks:
        await scheduler.submit_task(task)
    
    # 启动调度器并等待任务完成
    scheduler_task = asyncio.create_task(scheduler.start_scheduler())
    
    # 等待所有任务完成
    start_time = time.time()
    while time.time() - start_time < 5:  # 最多等待5秒
        stats = scheduler.get_stats()
        if stats["completed"] + stats["failed"] == len(tasks):
            break
        await asyncio.sleep(0.1)
    
    scheduler_task.cancel()
    
    # 显示结果
    print("\n任务执行结果:")
    for task in tasks:
        status_emoji = {"completed": "✅", "failed": "❌", "running": "🔄", "pending": "⏳"}
        print(f"  {status_emoji.get(task.status, '❓')} {task.name}: {task.status}")
        if task.result:
            print(f"    结果: {task.result}")
        if task.error:
            print(f"    错误: {task.error}")
    
    # 显示统计
    final_stats = scheduler.get_stats()
    print(f"\n调度器统计: {json.dumps(final_stats, indent=2, ensure_ascii=False)}")

async def demo_performance_monitoring():
    """演示性能监控功能"""
    print("\n" + "="*60)
    print("📊 性能监控器演示")
    print("="*60)
    
    monitor = SimplePerformanceMonitor()
    
    # 模拟一些性能数据
    print("模拟系统运行，收集性能数据...")
    
    for i in range(10):
        # 模拟响应时间
        response_time = 0.1 + (i % 3) * 0.05
        monitor.record_metric("response_time", response_time)
        
        # 模拟请求计数
        monitor.increment_counter("requests", 1 + i % 2)
        
        # 模拟CPU使用率
        cpu_usage = 20 + (i % 5) * 10
        monitor.record_metric("cpu_usage", cpu_usage)
        
        await asyncio.sleep(0.1)
    
    # 模拟一些错误
    monitor.increment_counter("errors", 2)
    monitor.increment_counter("cache_hits", 15)
    monitor.increment_counter("cache_misses", 3)
    
    # 显示监控摘要
    summary = monitor.get_summary()
    print(f"\n性能监控摘要:")
    print(f"运行时间: {summary['uptime']:.2f} 秒")
    print(f"计数器: {json.dumps(summary['counters'], indent=2, ensure_ascii=False)}")
    print(f"指标统计: {json.dumps(summary['metrics'], indent=2, ensure_ascii=False)}")

async def main():
    """主演示函数"""
    print("🎯 LangGraph多智能体系统 v0.3 第一阶段功能演示")
    print("=" * 80)
    print("本演示展示了第一阶段实现的核心基础设施组件:")
    print("• 分布式状态管理器")
    print("• 智能缓存系统") 
    print("• 任务调度器")
    print("• 性能监控系统")
    print("=" * 80)
    
    try:
        # 依次演示各个组件
        await demo_state_management()
        await demo_cache_management()
        await demo_task_scheduling()
        await demo_performance_monitoring()
        
        print("\n" + "="*80)
        print("🎉 第一阶段功能演示完成!")
        print("所有核心基础设施组件运行正常，为第二阶段的智能体扩展奠定了坚实基础。")
        print("="*80)
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())