@echo off
REM LangGraph CLI启动脚本 - Windows版本
REM 修复编码问题

REM 设置编码为UTF-8
chcp 65001 >nul 2>&1

REM 设置环境变量
set PYTHONIOENCODING=utf-8
set PYTHONLEGACYWINDOWSSTDIO=0
set LANG=en_US.UTF-8
set LC_ALL=en_US.UTF-8

REM 获取脚本目录
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..

REM 激活虚拟环境（如果存在）
if exist "%PROJECT_ROOT%\venv\Scripts\activate.bat" (
    call "%PROJECT_ROOT%\venv\Scripts\activate.bat"
)

REM 运行CLI
python "%PROJECT_ROOT%\src\main.py" %*

REM 保持错误代码
exit /b %errorlevel%
