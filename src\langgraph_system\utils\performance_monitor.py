"""
性能监控工具
"""

import time
import psutil
import logging
from typing import Dict, Any, Optional
from contextlib import contextmanager
from functools import wraps

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics: Dict[str, Any] = {}
        self.start_time = time.time()
        
    @contextmanager
    def timer(self, name: str):
        """上下文管理器用于计时"""
        start = time.time()
        try:
            yield
        finally:
            elapsed = time.time() - start
            self.metrics[name] = elapsed
            logger.info(f"{name} took {elapsed:.2f}s")
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,
            "vms_mb": memory_info.vms / 1024 / 1024,
            "percent": process.memory_percent()
        }
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage("/").percent
        }

class LLMCache:
    """LLM响应缓存"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, str] = {}
        self.max_size = max_size
        self.hits = 0
        self.misses = 0
    
    def _get_key(self, messages: list, model: str, **kwargs) -> str:
        """生成缓存键"""
        import hashlib
        content = str(messages) + str(model) + str(kwargs)
        return hashlib.md5(content.encode()).hexdigest()
    
    def get(self, messages: list, model: str, **kwargs) -> Optional[str]:
        """获取缓存响应"""
        key = self._get_key(messages, model, **kwargs)
        if key in self.cache:
            self.hits += 1
            logger.debug(f"Cache hit for key: {key}")
            return self.cache[key]
        
        self.misses += 1
        return None
    
    def set(self, messages: list, model: str, response: str, **kwargs):
        """设置缓存响应"""
        key = self._get_key(messages, model, **kwargs)
        
        if len(self.cache) >= self.max_size:
            # LRU eviction
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        
        self.cache[key] = response
    
    def get_stats(self) -> Dict[str, int]:
        """获取缓存统计"""
        total = self.hits + self.misses
        hit_rate = (self.hits / total * 100) if total > 0 else 0
        
        return {
            "hits": self.hits,
            "misses": self.misses,
            "total": total,
            "hit_rate": round(hit_rate, 2),
            "cache_size": len(self.cache)
        }

# 全局实例
monitor = PerformanceMonitor()
cache = LLMCache()

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        with monitor.timer(func.__name__):
            return func(*args, **kwargs)
    return wrapper

def log_memory_usage():
    """记录内存使用情况"""
    memory = monitor.get_memory_usage()
    logger.info(f"Memory usage: RSS={memory['rss_mb']:.1f}MB, "
                f"VMS={memory['vms_mb']:.1f}MB, "
                f"Percent={memory['percent']:.1f}%")

if __name__ == "__main__":
    # 测试代码
    print("Testing performance monitor...")
    log_memory_usage()
    
    # 测试缓存
    messages = [{"role": "user", "content": "Hello"}]
    cache.set(messages, "gpt-4", "Hello! How can I help you?")
    cached_response = cache.get(messages, "gpt-4")
    print(f"Cached response: {cached_response}")
    print(f"Cache stats: {cache.get_stats()}")