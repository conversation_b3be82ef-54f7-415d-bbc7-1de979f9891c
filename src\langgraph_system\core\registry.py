"""
注册中心 - 管理智能体和工具的注册与发现
"""

from typing import Dict, Any, Type, Callable, List, Optional
from abc import ABC, abstractmethod
import logging
from .exceptions import AgentError, ToolError

logger = logging.getLogger(__name__)


class AgentRegistry:
    """智能体注册中心"""
    
    def __init__(self):
        self._agents: Dict[str, Type] = {}
        self._instances: Dict[str, Any] = {}
        self._metadata: Dict[str, Dict[str, Any]] = {}
    
    def register(self, name: str, agent_class: Type, metadata: Dict[str, Any] = None):
        """注册智能体类"""
        if name in self._agents:
            logger.warning(f"智能体 '{name}' 已存在，将被覆盖")
        
        self._agents[name] = agent_class
        self._metadata[name] = metadata or {}
        logger.info(f"智能体 '{name}' 注册成功")
    
    def get_agent_class(self, name: str) -> Type:
        """获取智能体类"""
        if name not in self._agents:
            raise AgentError(name, f"智能体 '{name}' 未注册", "AGENT_NOT_FOUND")
        return self._agents[name]
    
    def create_agent(self, name: str, **kwargs) -> Any:
        """创建智能体实例"""
        agent_class = self.get_agent_class(name)
        try:
            instance = agent_class(**kwargs)
            self._instances[name] = instance
            return instance
        except Exception as e:
            raise AgentError(name, f"创建智能体实例失败: {str(e)}", "AGENT_CREATION_FAILED")
    
    def get_agent(self, name: str) -> Any:
        """获取智能体实例（如果不存在则创建）"""
        if name not in self._instances:
            self.create_agent(name)
        return self._instances[name]
    
    def list_agents(self) -> List[str]:
        """列出所有已注册的智能体"""
        return list(self._agents.keys())
    
    def get_metadata(self, name: str) -> Dict[str, Any]:
        """获取智能体元数据"""
        return self._metadata.get(name, {})
    
    def unregister(self, name: str):
        """注销智能体"""
        if name in self._agents:
            del self._agents[name]
            if name in self._instances:
                del self._instances[name]
            if name in self._metadata:
                del self._metadata[name]
            logger.info(f"智能体 '{name}' 已注销")


class ToolRegistry:
    """工具注册中心"""
    
    def __init__(self):
        self._tools: Dict[str, Callable] = {}
        self._metadata: Dict[str, Dict[str, Any]] = {}
    
    def register(self, name: str, tool_func: Callable, metadata: Dict[str, Any] = None):
        """注册工具函数"""
        if name in self._tools:
            logger.warning(f"工具 '{name}' 已存在，将被覆盖")
        
        self._tools[name] = tool_func
        self._metadata[name] = metadata or {}
        logger.info(f"工具 '{name}' 注册成功")
    
    def get_tool(self, name: str) -> Callable:
        """获取工具函数"""
        if name not in self._tools:
            raise ToolError(name, f"工具 '{name}' 未注册", "TOOL_NOT_FOUND")
        return self._tools[name]
    
    def execute_tool(self, name: str, **kwargs) -> Any:
        """执行工具"""
        tool_func = self.get_tool(name)
        try:
            return tool_func(**kwargs)
        except Exception as e:
            raise ToolError(name, f"工具执行失败: {str(e)}", "TOOL_EXECUTION_FAILED")
    
    def list_tools(self) -> List[str]:
        """列出所有已注册的工具"""
        return list(self._tools.keys())
    
    def get_metadata(self, name: str) -> Dict[str, Any]:
        """获取工具元数据"""
        return self._metadata.get(name, {})
    
    def unregister(self, name: str):
        """注销工具"""
        if name in self._tools:
            del self._tools[name]
            if name in self._metadata:
                del self._metadata[name]
            logger.info(f"工具 '{name}' 已注销")


# 全局注册中心实例
agent_registry = AgentRegistry()
tool_registry = ToolRegistry()


def register_agent(name: str, metadata: Dict[str, Any] = None):
    """智能体注册装饰器"""
    def decorator(agent_class: Type):
        agent_registry.register(name, agent_class, metadata)
        return agent_class
    return decorator


def register_tool(name: str, metadata: Dict[str, Any] = None):
    """工具注册装饰器"""
    def decorator(tool_func: Callable):
        tool_registry.register(name, tool_func, metadata)
        return tool_func
    return decorator