#!/usr/bin/env python3
"""
第二阶段智能体系统测试
测试所有专业智能体的功能和协作机制
"""

import asyncio
import unittest
import json
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

# 导入智能体
from src.langgraph_system.agents import (
    ArchitectAgent, ProductManagerAgent, DevOpsAgent, 
    QAAgent, DocumentationAgent, CoderAgent
)
from src.langgraph_system.agents.base_agent import TaskRequest, Priority
from src.langgraph_system.agents.collaboration import AgentCollaborationManager
from src.langgraph_system.agents.communication import AgentCommunicationProtocol
from src.langgraph_system.agents.capability_assessment import AgentCapabilityAssessment
from src.langgraph_system.agents.learning import AgentLearningSystem


class TestArchitectAgent(unittest.TestCase):
    """测试架构师智能体"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.agent = ArchitectAgent()
    
    async def async_setUp(self):
        """异步初始化"""
        await self.agent.initialize()
    
    def test_agent_initialization(self):
        """测试智能体初始化"""
        asyncio.run(self.async_setUp())
        self.assertIsNotNone(self.agent.agent_id)
        self.assertEqual(self.agent.name, "架构师智能体")
        self.assertTrue(len(self.agent.capabilities) > 0)
    
    def test_system_design_capability(self):
        """测试系统设计能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="design_system",
                description="设计电商平台系统架构",
                parameters={
                    "requirements": {
                        "name": "电商平台",
                        "type": "web_application",
                        "expected_users": 10000
                    }
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("design_id", result.result)
            self.assertIn("system_name", result.result)
            self.assertIn("components_count", result.result)
            self.assertGreater(result.result["components_count"], 0)
        
        asyncio.run(run_test())
    
    def test_technology_selection(self):
        """测试技术选型能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="select_technology",
                description="为电商平台选择数据库技术",
                parameters={
                    "category": "database",
                    "requirements": {
                        "data_volume": "large",
                        "consistency": "strong"
                    }
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("recommended_technology", result.result)
            self.assertIn("score", result.result)
            self.assertGreater(result.result["score"], 0)
        
        asyncio.run(run_test())


class TestProductManagerAgent(unittest.TestCase):
    """测试产品经理智能体"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.agent = ProductManagerAgent()
    
    async def async_setUp(self):
        """异步初始化"""
        await self.agent.initialize()
    
    def test_requirements_analysis(self):
        """测试需求分析能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="analyze_requirements",
                description="分析电商平台需求",
                parameters={
                    "requirements": [
                        {
                            "id": "req_001",
                            "title": "用户注册登录",
                            "description": "用户可以注册账号并登录系统",
                            "type": "functional",
                            "business_impact": "high"
                        }
                    ]
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("total_requirements", result.result)
            self.assertIn("requirements_by_type", result.result)
            self.assertEqual(result.result["total_requirements"], 1)
        
        asyncio.run(run_test())
    
    def test_user_story_creation(self):
        """测试用户故事创建能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="create_user_stories",
                description="创建用户故事",
                parameters={
                    "requirements": [
                        {
                            "id": "req_001",
                            "title": "用户登录",
                            "description": "用户可以登录系统",
                            "priority": "high"
                        }
                    ]
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("total_stories", result.result)
            self.assertGreater(result.result["total_stories"], 0)
        
        asyncio.run(run_test())


class TestDevOpsAgent(unittest.TestCase):
    """测试DevOps智能体"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.agent = DevOpsAgent()
    
    async def async_setUp(self):
        """异步初始化"""
        await self.agent.initialize()
    
    def test_cicd_setup(self):
        """测试CI/CD设置能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="setup_cicd",
                description="设置CI/CD管道",
                parameters={
                    "project": {
                        "name": "test-project",
                        "type": "web_application",
                        "language": "python"
                    },
                    "platform": "jenkins"
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("pipeline_id", result.result)
            self.assertIn("stages_count", result.result)
            self.assertGreater(result.result["stages_count"], 0)
        
        asyncio.run(run_test())
    
    def test_infrastructure_deployment(self):
        """测试基础设施部署能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="deploy_infrastructure",
                description="部署基础设施",
                parameters={
                    "infrastructure": {
                        "compute": [
                            {
                                "name": "web-server",
                                "instance_type": "t3.medium"
                            }
                        ]
                    },
                    "provider": "aws"
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("deployed_resources", result.result)
            self.assertGreater(len(result.result["deployed_resources"]), 0)
        
        asyncio.run(run_test())


class TestQAAgent(unittest.TestCase):
    """测试QA智能体"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.agent = QAAgent()
    
    async def async_setUp(self):
        """异步初始化"""
        await self.agent.initialize()
    
    def test_test_strategy_design(self):
        """测试测试策略设计能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="design_test_strategy",
                description="设计测试策略",
                parameters={
                    "project_name": "测试项目",
                    "requirements": {
                        "type": "web_application",
                        "complexity": "medium"
                    }
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("strategy_id", result.result)
            self.assertIn("test_types_covered", result.result)
            self.assertGreater(result.result["test_types_covered"], 0)
        
        asyncio.run(run_test())
    
    def test_test_case_creation(self):
        """测试测试用例创建能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="create_test_cases",
                description="创建测试用例",
                parameters={
                    "requirements": [
                        {
                            "id": "req_001",
                            "title": "用户登录",
                            "description": "用户可以登录系统",
                            "type": "functional"
                        }
                    ]
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("total_test_cases", result.result)
            self.assertGreater(result.result["total_test_cases"], 0)
        
        asyncio.run(run_test())


class TestDocumentationAgent(unittest.TestCase):
    """测试文档智能体"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.agent = DocumentationAgent()
    
    async def async_setUp(self):
        """异步初始化"""
        await self.agent.initialize()
    
    def test_documentation_generation(self):
        """测试文档生成能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="generate_documentation",
                description="生成用户指南",
                parameters={
                    "document_type": "user_guide",
                    "content_source": {
                        "title": "测试产品用户指南",
                        "product_name": "测试产品",
                        "features": [
                            {"name": "登录功能", "description": "用户登录系统"}
                        ]
                    }
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("document_id", result.result)
            self.assertIn("content", result.result)
            self.assertGreater(result.result["word_count"], 0)
        
        asyncio.run(run_test())
    
    def test_api_documentation_generation(self):
        """测试API文档生成能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="generate_api_docs",
                description="生成API文档",
                parameters={
                    "api_spec": {
                        "name": "测试API",
                        "version": "1.0.0",
                        "endpoints": [
                            {
                                "path": "/users",
                                "method": "GET",
                                "description": "获取用户列表"
                            }
                        ]
                    }
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("api_doc_id", result.result)
            self.assertIn("endpoints_count", result.result)
            self.assertEqual(result.result["endpoints_count"], 1)
        
        asyncio.run(run_test())


class TestCoderAgent(unittest.TestCase):
    """测试编程智能体"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.agent = CoderAgent()
    
    async def async_setUp(self):
        """异步初始化"""
        await self.agent.initialize()
    
    def test_code_generation(self):
        """测试代码生成能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="generate_code",
                description="生成Python函数",
                parameters={
                    "requirements": "创建一个计算器函数",
                    "language": "python",
                    "code_type": "function"
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("code_snippet_id", result.result)
            self.assertIn("generated_code", result.result)
            self.assertGreater(len(result.result["generated_code"]), 0)
        
        asyncio.run(run_test())
    
    def test_code_review(self):
        """测试代码审查能力"""
        async def run_test():
            await self.agent.initialize()
            
            task = TaskRequest(
                type="review_code",
                description="审查Python代码",
                parameters={
                    "code_content": """
def calculate(a, b):
    return a + b
""",
                    "language": "python"
                },
                priority=Priority.HIGH
            )
            
            result = await self.agent.process_task(task)
            
            self.assertEqual(result.status, "completed")
            self.assertIn("review_id", result.result)
            self.assertIn("overall_rating", result.result)
            self.assertIn("issues", result.result)
        
        asyncio.run(run_test())


class TestAgentCollaboration(unittest.TestCase):
    """测试智能体协作机制"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.collaboration_manager = AgentCollaborationManager()
        self.architect = ArchitectAgent()
        self.pm = ProductManagerAgent()
    
    def test_agent_registration(self):
        """测试智能体注册"""
        async def run_test():
            await self.architect.initialize()
            await self.pm.initialize()
            
            # 注册智能体
            result1 = await self.collaboration_manager.register_agent(self.architect)
            result2 = await self.collaboration_manager.register_agent(self.pm)
            
            self.assertTrue(result1)
            self.assertTrue(result2)
            
            # 检查注册的智能体
            agents = self.collaboration_manager.get_registered_agents()
            self.assertEqual(len(agents), 2)
        
        asyncio.run(run_test())
    
    def test_workflow_creation(self):
        """测试工作流创建"""
        async def run_test():
            await self.architect.initialize()
            await self.pm.initialize()
            
            # 注册智能体
            await self.collaboration_manager.register_agent(self.architect)
            await self.collaboration_manager.register_agent(self.pm)
            
            # 创建工作流
            workflow_id = await self.collaboration_manager.create_workflow(
                "software_development",
                {
                    "project_name": "测试项目",
                    "analyze_requirements": {
                        "requirements": [
                            {
                                "id": "req_001",
                                "title": "基础功能",
                                "description": "实现基础功能"
                            }
                        ]
                    }
                }
            )
            
            self.assertIsNotNone(workflow_id)
            
            # 检查工作流状态
            status = await self.collaboration_manager.get_workflow_status(workflow_id)
            self.assertEqual(status["status"], "pending")
        
        asyncio.run(run_test())


class TestAgentCommunication(unittest.TestCase):
    """测试智能体通信协议"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.communication = AgentCommunicationProtocol()
    
    def test_channel_creation(self):
        """测试通信通道创建"""
        async def run_test():
            channel_id = await self.communication.create_channel({
                "name": "测试通道",
                "pattern": "request_response",
                "participants": ["agent1", "agent2"]
            })
            
            self.assertIsNotNone(channel_id)
            
            # 检查通道统计
            stats = self.communication.get_channel_statistics(channel_id)
            self.assertEqual(stats["participants_count"], 2)
        
        asyncio.run(run_test())
    
    def test_message_sending(self):
        """测试消息发送"""
        async def run_test():
            from src.langgraph_system.agents.base_agent import MessageType
            
            # 创建通道
            channel_id = await self.communication.create_channel({
                "name": "测试通道",
                "pattern": "request_response"
            })
            
            # 发送消息
            message_id = await self.communication.send_message(
                sender_id="agent1",
                recipient_id="agent2",
                message_type=MessageType.REQUEST,
                content={"test": "message"},
                channel_id=channel_id
            )
            
            self.assertIsNotNone(message_id)
            
            # 检查消息状态
            status = await self.communication.get_message_status(message_id)
            self.assertIsNotNone(status)
        
        asyncio.run(run_test())


class TestCapabilityAssessment(unittest.TestCase):
    """测试能力评估系统"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.assessment = AgentCapabilityAssessment()
        self.agent = ArchitectAgent()
    
    def test_agent_assessment(self):
        """测试智能体能力评估"""
        async def run_test():
            await self.agent.initialize()
            
            # 执行能力评估
            assessment_id = await self.assessment.assess_agent_capability(
                self.agent,
                "system_design"
            )
            
            self.assertIsNotNone(assessment_id)
            
            # 获取评估结果
            result = self.assessment.get_assessment_result(assessment_id)
            self.assertIsNotNone(result)
            self.assertGreater(result.overall_score, 0)
        
        asyncio.run(run_test())
    
    def test_batch_assessment(self):
        """测试批量评估"""
        async def run_test():
            await self.agent.initialize()
            pm = ProductManagerAgent()
            await pm.initialize()
            
            # 批量评估
            results = await self.assessment.batch_assess_agents([self.agent, pm])
            
            self.assertEqual(len(results), 2)
            self.assertIn(self.agent.agent_id, results)
            self.assertIn(pm.agent_id, results)
        
        asyncio.run(run_test())


class TestLearningSystem(unittest.TestCase):
    """测试智能体学习机制"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.learning = AgentLearningSystem()
        self.agent = ArchitectAgent()
    
    def test_experience_recording(self):
        """测试经验记录"""
        async def run_test():
            await self.agent.initialize()
            
            # 创建任务和结果
            task = TaskRequest(
                type="design_system",
                description="测试任务",
                parameters={"test": "data"},
                priority=Priority.MEDIUM
            )
            
            from src.langgraph_system.agents.base_agent import TaskResult
            result = TaskResult(
                task_id=task.id,
                agent_id=self.agent.agent_id,
                status="completed",
                result={"success": True},
                execution_time=1.0
            )
            
            # 记录经验
            experience_id = await self.learning.record_experience(
                self.agent.agent_id, task, result
            )
            
            self.assertIsNotNone(experience_id)
            
            # 检查学习统计
            stats = self.learning.get_learning_statistics(self.agent.agent_id)
            self.assertEqual(stats["total_experiences"], 1)
        
        asyncio.run(run_test())
    
    def test_learning_session(self):
        """测试学习会话"""
        async def run_test():
            from src.langgraph_system.agents.learning import LearningType
            
            # 先记录一些经验
            await self.agent.initialize()
            
            task = TaskRequest(
                type="design_system",
                description="测试任务",
                parameters={"test": "data"},
                priority=Priority.MEDIUM
            )
            
            from src.langgraph_system.agents.base_agent import TaskResult
            result = TaskResult(
                task_id=task.id,
                agent_id=self.agent.agent_id,
                status="completed",
                result={"success": True},
                execution_time=1.0
            )
            
            # 记录多个经验
            for i in range(5):
                await self.learning.record_experience(
                    self.agent.agent_id, task, result
                )
            
            # 开始学习会话
            session_id = await self.learning.start_learning_session(
                self.agent.agent_id,
                LearningType.INCREMENTAL,
                "测试学习"
            )
            
            self.assertIsNotNone(session_id)
        
        asyncio.run(run_test())


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_full_workflow_integration(self):
        """测试完整工作流集成"""
        async def run_test():
            # 创建所有组件
            collaboration = AgentCollaborationManager()
            communication = AgentCommunicationProtocol()
            assessment = AgentCapabilityAssessment()
            learning = AgentLearningSystem()
            
            # 创建智能体
            architect = ArchitectAgent()
            pm = ProductManagerAgent()
            devops = DevOpsAgent()
            
            # 初始化智能体
            await architect.initialize()
            await pm.initialize()
            await devops.initialize()
            
            # 注册智能体
            await collaboration.register_agent(architect)
            await collaboration.register_agent(pm)
            await collaboration.register_agent(devops)
            
            # 创建通信通道
            channel_id = await communication.create_channel({
                "name": "项目协作通道",
                "pattern": "publish_subscribe",
                "participants": [architect.agent_id, pm.agent_id, devops.agent_id]
            })
            
            # 创建工作流
            workflow_id = await collaboration.create_workflow(
                "software_development",
                {
                    "project_name": "集成测试项目",
                    "analyze_requirements": {
                        "requirements": [
                            {
                                "id": "req_001",
                                "title": "核心功能",
                                "description": "实现核心功能"
                            }
                        ]
                    }
                }
            )
            
            # 验证集成结果
            self.assertIsNotNone(workflow_id)
            self.assertIsNotNone(channel_id)
            
            # 检查系统状态
            agents = collaboration.get_registered_agents()
            self.assertEqual(len(agents), 3)
            
            protocol_stats = communication.get_protocol_statistics()
            self.assertGreater(protocol_stats["total_channels"], 0)
        
        asyncio.run(run_test())


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)