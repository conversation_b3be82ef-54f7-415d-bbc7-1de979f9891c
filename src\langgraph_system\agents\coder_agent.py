#!/usr/bin/env python3
"""
编程智能体 (CoderAgent) - v0.3.0 重构
负责代码生成、代码审查、重构优化和编程问题解决
"""

import asyncio
import json
import uuid
import re
import ast
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
import logging

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.tools import tool

from .base_v3 import SpecialistAgent, AgentCapability

logger = logging.getLogger(__name__)

# ============================================================================
# 编程领域数据模型
# ============================================================================

class ProgrammingLanguage(str, Enum):
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    GO = "go"
    RUST = "rust"

class CodeQuality(str, Enum):
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"

@dataclass
class CodeSnippet:
    id: str
    language: ProgrammingLanguage
    content: str
    description: str
    complexity_score: float = 0.0
    quality_score: float = 0.0

@dataclass
class CodeReview:
    id: str
    issues: List[Dict[str, Any]]
    suggestions: List[Dict[str, Any]]
    overall_rating: CodeQuality

# ============================================================================
# 编程智能体实现 (v3)
# ============================================================================

class CoderAgent(SpecialistAgent):
    """
    编程智能体 v3
    
    核心职责：
    - 代码生成、审查、重构和调试
    """
    
    def __init__(self, model: BaseLanguageModel, custom_tools: List = None, **kwargs):
        self.code_snippets: Dict[str, CodeSnippet] = {}
        self.code_reviews: Dict[str, CodeReview] = {}

        agent_tools = [
            self.generate_code,
            self.review_code,
            self.refactor_code,
            self.debug_code,
            self.generate_tests,
        ]
        if custom_tools:
            agent_tools.extend(custom_tools)

        super().__init__(
            agent_id="coder_001",
            name="编程智能体",
            capabilities=[
                AgentCapability.CODE_DEVELOPMENT,
                AgentCapability.TESTING_QA,
            ],
            model=model,
            tools=agent_tools,
            **kwargs,
        )

    # ========================================================================
    # 智能体工具定义
    # ========================================================================

    @tool
    async def generate_code(self, requirements: str, language: str, context: str = "{}") -> str:
        """
        根据需求、语言和上下文生成代码片段。
        Args:
            requirements (str): 代码功能需求描述.
            language (str): 目标编程语言 (e.g., 'python', 'javascript').
            context (str, optional): JSON格式的附加上下文.
        Returns:
            str: JSON格式的代码生成结果, 包含代码片段ID、内容和评估.
        """
        try:
            lang = ProgrammingLanguage(language)
            logger.info(f"开始生成代码: {lang.value}")
            
            # (简化的) 调用LLM生成代码
            code_content = await self._invoke_llm_for_code_generation(requirements, lang, context)
            
            snippet_id = f"code_{uuid.uuid4().hex[:8]}"
            snippet = CodeSnippet(
                id=snippet_id,
                language=lang,
                content=code_content,
                description=requirements,
                complexity_score=self._calculate_complexity_score(code_content, lang),
                quality_score=self._calculate_quality_score(code_content, lang)
            )
            self.code_snippets[snippet_id] = snippet
            
            result = {
                "code_snippet_id": snippet.id,
                "language": snippet.language.value,
                "code": snippet.content,
                "complexity_score": snippet.complexity_score,
                "quality_score": snippet.quality_score,
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"代码生成失败: {e}", exc_info=True)
            return json.dumps({"error": f"代码生成失败: {str(e)}"})

    @tool
    async def review_code(self, code_content: str, language: str) -> str:
        """
        对给定的代码进行审查。
        Args:
            code_content (str): 需要审查的代码.
            language (str): 代码的编程语言.
        Returns:
            str: JSON格式的审查报告.
        """
        try:
            lang = ProgrammingLanguage(language)
            logger.info(f"开始代码审查: {lang.value}")

            issues = await self._analyze_code_issues(code_content, lang)
            suggestions = await self._generate_code_suggestions(issues)
            rating = self._calculate_overall_rating(issues)

            review_id = f"review_{uuid.uuid4().hex[:8]}"
            review = CodeReview(
                id=review_id,
                issues=issues,
                suggestions=suggestions,
                overall_rating=rating,
            )
            self.code_reviews[review_id] = review

            result = {
                "review_id": review.id,
                "issues_count": len(review.issues),
                "suggestions_count": len(review.suggestions),
                "overall_rating": review.overall_rating.value,
                "issues": review.issues,
                "suggestions": review.suggestions,
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"代码审查失败: {e}", exc_info=True)
            return json.dumps({"error": f"代码审查失败: {str(e)}"})

    @tool
    async def refactor_code(self, code_content: str, language: str, goals: List[str]) -> str:
        """
        重构代码以满足特定目标。
        Args:
            code_content (str): 需要重构的代码.
            language (str): 代码的编程语言.
            goals (List[str]): 重构目标列表 (e.g., 'improve_readability', 'reduce_complexity').
        Returns:
            str: JSON格式的重构结果, 包含重构后的代码.
        """
        try:
            lang = ProgrammingLanguage(language)
            logger.info(f"开始重构代码: {', '.join(goals)}")

            refactored_code = await self._invoke_llm_for_refactoring(code_content, lang, goals)

            original_quality = self._calculate_quality_score(code_content, lang)
            refactored_quality = self._calculate_quality_score(refactored_code, lang)

            result = {
                "original_code": code_content,
                "refactored_code": refactored_code,
                "refactoring_goals": goals,
                "quality_improvement": refactored_quality - original_quality,
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"代码重构失败: {e}", exc_info=True)
            return json.dumps({"error": f"代码重构失败: {str(e)}"})

    @tool
    async def debug_code(self, code_content: str, language: str, error_description: str) -> str:
        """
        根据错误描述调试代码。
        Args:
            code_content (str): 需要调试的代码.
            language (str): 代码的编程语言.
            error_description (str): 遇到的错误信息或堆栈跟踪.
        Returns:
            str: JSON格式的调试结果, 包括修复建议和修复后的代码.
        """
        try:
            lang = ProgrammingLanguage(language)
            logger.info("开始调试代码")

            fixed_code = await self._invoke_llm_for_debugging(code_content, lang, error_description)

            result = {
                "error_description": error_description,
                "fixed_code": fixed_code,
                "explanation": "LLM分析了错误并提供了修复方案。",
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"代码调试失败: {e}", exc_info=True)
            return json.dumps({"error": f"代码调试失败: {str(e)}"})

    @tool
    async def generate_tests(self, code_content: str, language: str, test_framework: str) -> str:
        """
        为给定的代码生成测试用例。
        Args:
            code_content (str): 需要测试的代码.
            language (str): 代码的编程语言.
            test_framework (str): 使用的测试框架 (e.g., 'pytest', 'jest').
        Returns:
            str: JSON格式的测试代码.
        """
        try:
            lang = ProgrammingLanguage(language)
            logger.info(f"开始生成测试: {test_framework}")

            test_code = await self._invoke_llm_for_test_generation(code_content, lang, test_framework)

            result = {
                "test_framework": test_framework,
                "test_code": test_code,
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"测试生成失败: {e}", exc_info=True)
            return json.dumps({"error": f"测试生成失败: {str(e)}"})

    # ========================================================================
    # 内部辅助方法 & LLM 调用
    # ========================================================================

    async def _invoke_llm_for_code_generation(self, requirements: str, lang: ProgrammingLanguage, context: str) -> str:
        prompt = f"""As a professional {lang.value} developer, generate a code snippet based on the following requirements.
Requirements: {requirements}
Context: {context}
Provide only the raw code without any explanations or markdown formatting."""
        response = await self.model.ainvoke(prompt)
        return response.content
    
    async def _invoke_llm_for_refactoring(self, code: str, lang: ProgrammingLanguage, goals: List[str]) -> str:
        prompt = f"""As a senior {lang.value} developer, refactor the following code to achieve these goals: {', '.join(goals)}.
Code:
{code}
Provide only the refactored raw code."""
        response = await self.model.ainvoke(prompt)
        return response.content

    async def _invoke_llm_for_debugging(self, code: str, lang: ProgrammingLanguage, error: str) -> str:
        prompt = f"""As an expert {lang.value} debugger, analyze the following code and error, then provide the fixed code.
Code:
{code}
Error:
{error}
Provide only the fixed raw code."""
        response = await self.model.ainvoke(prompt)
        return response.content
        
    async def _invoke_llm_for_test_generation(self, code: str, lang: ProgrammingLanguage, framework: str) -> str:
        prompt = f"""As a QA engineer, write unit tests for the following {lang.value} code using the {framework} framework.
Code:
{code}
Provide only the raw test code."""
        response = await self.model.ainvoke(prompt)
        return response.content

    def _calculate_complexity_score(self, code: str, language: ProgrammingLanguage) -> float:
        """静态分析代码复杂度 (简化版)"""
        if language == ProgrammingLanguage.PYTHON:
            try:
                # 尝试通过计算AST节点数量来评估复杂度
                tree = ast.parse(code)
                score = len(list(ast.walk(tree))) / 100.0
                return min(10.0, score)
            except SyntaxError:
                return 8.0 # 语法错误意味着高复杂度
        return 5.0 # 默认中等复杂度

    def _calculate_quality_score(self, code: str, language: ProgrammingLanguage) -> float:
        """静态分析代码质量 (简化版)"""
        lines = code.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        if not non_empty_lines: return 0.0

        # 注释与代码行比例
        comment_lines = [line for line in lines if line.strip().startswith(('#', '//', '/*'))]
        comment_ratio = len(comment_lines) / len(non_empty_lines)
        
        # 函数长度
        func_lengths = [len(f.body) for f in ast.walk(ast.parse(code)) if isinstance(f, ast.FunctionDef)] if language == ProgrammingLanguage.PYTHON else [10]
        avg_func_length = sum(func_lengths) / len(func_lengths) if func_lengths else 0
        
        quality = 10.0
        if comment_ratio < 0.05: quality -= 2 # 注释太少
        if avg_func_length > 30: quality -= 3 # 函数太长
        
        return max(0.0, quality)

    async def _analyze_code_issues(self, code: str, language: ProgrammingLanguage) -> List[Dict[str, Any]]:
        """分析代码中的潜在问题 (简化版)"""
        issues = []
        if language == ProgrammingLanguage.PYTHON:
            try:
                ast.parse(code)
            except SyntaxError as e:
                issues.append({"type": "syntax_error", "severity": "high", "line": e.lineno, "message": str(e)})
        
        # 查找 "TODO" 或 "FIXME"
        for i, line in enumerate(code.split('\n'), 1):
            if "TODO" in line or "FIXME" in line:
                issues.append({"type": "task_comment", "severity": "low", "line": i, "message": line.strip()})
        return issues
    
    async def _generate_code_suggestions(self, issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据问题生成改进建议"""
        suggestions = []
        for issue in issues:
            if issue['type'] == 'syntax_error':
                suggestions.append({"action": "Fix syntax error", "details": f"Check line {issue['line']} for syntax problems."})
        return suggestions

    def _calculate_overall_rating(self, issues: List[Dict[str, Any]]) -> CodeQuality:
        """计算总体评级"""
        if any(i['severity'] == 'high' for i in issues):
            return CodeQuality.POOR
        if len(issues) > 5:
            return CodeQuality.FAIR
        if len(issues) > 0:
            return CodeQuality.GOOD
        return CodeQuality.EXCELLENT

__all__ = ['CoderAgent', 'ProgrammingLanguage', 'CodeQuality', 'CodeSnippet', 'CodeReview']
