"""
智能缓存系统 - v0.3增强版
支持多层缓存、智能失效策略和缓存预热
"""

import asyncio
import json
import logging
import pickle
import hashlib
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
from enum import Enum
from dataclasses import dataclass
import weakref

from redis import asyncio as aioredis
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class CacheLevel(str, Enum):
    """缓存级别"""
    MEMORY = "memory"
    REDIS = "redis"
    BOTH = "both"


class CacheStrategy(str, Enum):
    """缓存策略"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    TTL = "ttl"  # 基于时间
    ADAPTIVE = "adaptive"  # 自适应


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: float
    last_accessed: float
    access_count: int
    ttl: Optional[int]
    size: int
    metadata: Dict[str, Any]
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def update_access(self):
        """更新访问信息"""
        self.last_accessed = time.time()
        self.access_count += 1


class CacheStatistics(BaseModel):
    """缓存统计信息"""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    memory_hits: int = 0
    redis_hits: int = 0
    evictions: int = 0
    errors: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        if self.total_requests == 0:
            return 0.0
        return self.cache_hits / self.total_requests
    
    @property
    def miss_rate(self) -> float:
        """未命中率"""
        return 1.0 - self.hit_rate
    
    def record_hit(self, cache_level: str):
        """记录命中"""
        self.total_requests += 1
        self.cache_hits += 1
        if cache_level == "memory":
            self.memory_hits += 1
    
    def record_miss(self):
        """记录未命中"""
        self.total_requests += 1
        self.cache_misses += 1
    
    def record_eviction(self):
        """记录驱逐"""
        self.evictions += 1
    
    def record_error(self):
        """记录错误"""
        self.errors += 1


class MemoryCache:
    """内存缓存实现"""
    
    def __init__(self, max_size: int = 1000, strategy: CacheStrategy = CacheStrategy.LRU):
        self.max_size = max_size
        self.strategy = strategy
        self.cache: Dict[str, CacheEntry] = {}
        self.access_order: List[str] = []  # LRU使用
        self.frequency: Dict[str, int] = {}  # LFU使用
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        async with self._lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            
            # 检查是否过期
            if entry.is_expired():
                await self._remove(key)
                return None
            
            # 更新访问信息
            entry.update_access()
            self._update_access_tracking(key)
            
            return entry.value
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, 
                 metadata: Optional[Dict[str, Any]] = None) -> bool:
        """设置缓存值"""
        async with self._lock:
            # 计算值的大小
            size = self._calculate_size(value)
            
            # 创建缓存条目
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                last_accessed=time.time(),
                access_count=1,
                ttl=ttl,
                size=size,
                metadata=metadata or {}
            )
            
            # 如果已存在，先删除
            if key in self.cache:
                await self._remove(key)
            
            # 检查是否需要驱逐
            while len(self.cache) >= self.max_size:
                await self._evict_one()
            
            # 添加新条目
            self.cache[key] = entry
            self._update_access_tracking(key)
            
            return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存条目"""
        async with self._lock:
            return await self._remove(key)
    
    async def clear(self):
        """清空缓存"""
        async with self._lock:
            self.cache.clear()
            self.access_order.clear()
            self.frequency.clear()
    
    async def _remove(self, key: str) -> bool:
        """内部删除方法"""
        if key not in self.cache:
            return False
        
        del self.cache[key]
        
        if key in self.access_order:
            self.access_order.remove(key)
        
        if key in self.frequency:
            del self.frequency[key]
        
        return True
    
    async def _evict_one(self):
        """驱逐一个条目"""
        if not self.cache:
            return
        
        if self.strategy == CacheStrategy.LRU:
            # 驱逐最近最少使用的
            if self.access_order:
                key_to_evict = self.access_order[0]
                await self._remove(key_to_evict)
        
        elif self.strategy == CacheStrategy.LFU:
            # 驱逐使用频率最低的
            if self.frequency:
                key_to_evict = min(self.frequency.items(), key=lambda x: x[1])[0]
                await self._remove(key_to_evict)
        
        elif self.strategy == CacheStrategy.TTL:
            # 驱逐最早过期的
            now = time.time()
            earliest_key = None
            earliest_time = float('inf')
            
            for key, entry in self.cache.items():
                if entry.ttl and entry.created_at + entry.ttl < earliest_time:
                    earliest_time = entry.created_at + entry.ttl
                    earliest_key = key
            
            if earliest_key:
                await self._remove(earliest_key)
        
        elif self.strategy == CacheStrategy.ADAPTIVE:
            # 自适应策略：结合访问频率和时间
            scores = {}
            now = time.time()
            
            for key, entry in self.cache.items():
                # 计算分数：访问频率 / 时间衰减
                time_factor = 1.0 / (1.0 + (now - entry.last_accessed) / 3600)  # 1小时衰减
                frequency_factor = entry.access_count
                scores[key] = frequency_factor * time_factor
            
            if scores:
                key_to_evict = min(scores.items(), key=lambda x: x[1])[0]
                await self._remove(key_to_evict)
    
    def _update_access_tracking(self, key: str):
        """更新访问跟踪"""
        # 更新LRU顺序
        if key in self.access_order:
            self.access_order.remove(key)
        self.access_order.append(key)
        
        # 更新LFU频率
        self.frequency[key] = self.frequency.get(key, 0) + 1
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小"""
        try:
            return len(pickle.dumps(value))
        except:
            return len(str(value))
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_size = sum(entry.size for entry in self.cache.values())
        return {
            "entries": len(self.cache),
            "max_size": self.max_size,
            "total_size": total_size,
            "strategy": self.strategy.value,
            "utilization": len(self.cache) / self.max_size if self.max_size > 0 else 0
        }


class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379", 
                 memory_cache_size: int = 1000,
                 default_ttl: int = 3600):
        self.redis_url = redis_url
        self.redis: Optional[aioredis.Redis] = None
        self.memory_cache = MemoryCache(memory_cache_size, CacheStrategy.ADAPTIVE)
        self.default_ttl = default_ttl
        self.statistics = CacheStatistics()
        self._initialized = False
        
        # 缓存预热配置
        self.preload_patterns: List[str] = []
        self.preload_functions: Dict[str, Callable] = {}
        
        # 智能失效配置
        self.invalidation_rules: Dict[str, List[str]] = {}
        
        # 性能监控
        self._performance_metrics: Dict[str, List[float]] = {
            "get_latency": [],
            "set_latency": [],
            "redis_latency": []
        }
    
    async def initialize(self):
        """初始化缓存管理器"""
        if self._initialized:
            return
        
        try:
            # 连接Redis
            self.redis = aioredis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=False,
                max_connections=20
            )
            
            # 测试连接
            await self.redis.ping()
            logger.info("Redis connection established for cache")
            
            # 启动后台任务
            asyncio.create_task(self._cleanup_expired_entries())
            asyncio.create_task(self._performance_monitor())
            
            self._initialized = True
            logger.info("IntelligentCacheManager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize cache manager: {e}")
            # 即使Redis连接失败，也可以使用内存缓存
            self._initialized = True
    
    async def get(self, key: str, cache_level: CacheLevel = CacheLevel.BOTH) -> Optional[Any]:
        """获取缓存值"""
        start_time = time.time()
        
        try:
            # 1. 先检查内存缓存
            if cache_level in [CacheLevel.MEMORY, CacheLevel.BOTH]:
                value = await self.memory_cache.get(key)
                if value is not None:
                    self.statistics.record_hit("memory")
                    self._record_latency("get_latency", time.time() - start_time)
                    return value
            
            # 2. 检查Redis缓存
            if cache_level in [CacheLevel.REDIS, CacheLevel.BOTH] and self.redis:
                redis_start = time.time()
                cached_data = await self.redis.get(f"cache:{key}")
                self._record_latency("redis_latency", time.time() - redis_start)
                
                if cached_data:
                    try:
                        value = pickle.loads(cached_data)
                        
                        # 回填内存缓存
                        if cache_level == CacheLevel.BOTH:
                            await self.memory_cache.set(key, value, self.default_ttl)
                        
                        self.statistics.record_hit("redis")
                        self._record_latency("get_latency", time.time() - start_time)
                        return value
                    except Exception as e:
                        logger.error(f"Failed to deserialize cached value for key {key}: {e}")
            
            # 3. 缓存未命中
            self.statistics.record_miss()
            self._record_latency("get_latency", time.time() - start_time)
            return None
            
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            self.statistics.record_error()
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None,
                 cache_level: CacheLevel = CacheLevel.BOTH,
                 metadata: Optional[Dict[str, Any]] = None) -> bool:
        """设置缓存值"""
        start_time = time.time()
        ttl = ttl or self.default_ttl
        
        try:
            success = True
            
            # 1. 设置内存缓存
            if cache_level in [CacheLevel.MEMORY, CacheLevel.BOTH]:
                await self.memory_cache.set(key, value, ttl, metadata)
            
            # 2. 设置Redis缓存
            if cache_level in [CacheLevel.REDIS, CacheLevel.BOTH] and self.redis:
                try:
                    serialized_value = pickle.dumps(value)
                    await self.redis.setex(f"cache:{key}", ttl, serialized_value)
                except Exception as e:
                    logger.error(f"Failed to set Redis cache for key {key}: {e}")
                    success = False
            
            # 3. 处理智能失效
            await self._handle_invalidation(key)
            
            self._record_latency("set_latency", time.time() - start_time)
            return success
            
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            self.statistics.record_error()
            return False
    
    async def delete(self, key: str, cache_level: CacheLevel = CacheLevel.BOTH) -> bool:
        """删除缓存条目"""
        try:
            success = True
            
            # 1. 删除内存缓存
            if cache_level in [CacheLevel.MEMORY, CacheLevel.BOTH]:
                await self.memory_cache.delete(key)
            
            # 2. 删除Redis缓存
            if cache_level in [CacheLevel.REDIS, CacheLevel.BOTH] and self.redis:
                await self.redis.delete(f"cache:{key}")
            
            return success
            
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    async def clear(self, cache_level: CacheLevel = CacheLevel.BOTH):
        """清空缓存"""
        try:
            # 1. 清空内存缓存
            if cache_level in [CacheLevel.MEMORY, CacheLevel.BOTH]:
                await self.memory_cache.clear()
            
            # 2. 清空Redis缓存
            if cache_level in [CacheLevel.REDIS, CacheLevel.BOTH] and self.redis:
                # 删除所有cache:*键
                pattern = "cache:*"
                keys = await self.redis.keys(pattern)
                if keys:
                    await self.redis.delete(*keys)
            
            logger.info("Cache cleared successfully")
            
        except Exception as e:
            logger.error(f"Cache clear error: {e}")
    
    def generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        # 创建一个包含所有参数的字符串
        key_parts = [prefix]
        
        # 添加位置参数
        for arg in args:
            key_parts.append(str(arg))
        
        # 添加关键字参数（排序以确保一致性）
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}={v}")
        
        # 生成哈希
        key_string = ":".join(key_parts)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        
        return f"{prefix}:{key_hash}"
    
    def cache_result(self, ttl: Optional[int] = None, 
                    cache_level: CacheLevel = CacheLevel.BOTH,
                    key_prefix: str = "func"):
        """装饰器：缓存函数结果"""
        def decorator(func: Callable):
            async def wrapper(*args, **kwargs):
                # 生成缓存键
                cache_key = self.generate_cache_key(
                    f"{key_prefix}:{func.__name__}", *args, **kwargs
                )
                
                # 尝试从缓存获取
                cached_result = await self.get(cache_key, cache_level)
                if cached_result is not None:
                    return cached_result
                
                # 执行函数
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 缓存结果
                await self.set(cache_key, result, ttl, cache_level)
                
                return result
            
            return wrapper
        return decorator
    
    async def preload_cache(self, patterns: Optional[List[str]] = None):
        """预热缓存"""
        patterns = patterns or self.preload_patterns
        
        for pattern in patterns:
            if pattern in self.preload_functions:
                try:
                    preload_func = self.preload_functions[pattern]
                    if asyncio.iscoroutinefunction(preload_func):
                        await preload_func()
                    else:
                        preload_func()
                    logger.info(f"Cache preloaded for pattern: {pattern}")
                except Exception as e:
                    logger.error(f"Cache preload failed for pattern {pattern}: {e}")
    
    def register_preload_function(self, pattern: str, func: Callable):
        """注册预热函数"""
        self.preload_functions[pattern] = func
        if pattern not in self.preload_patterns:
            self.preload_patterns.append(pattern)
    
    def add_invalidation_rule(self, trigger_key: str, affected_keys: List[str]):
        """添加失效规则"""
        if trigger_key not in self.invalidation_rules:
            self.invalidation_rules[trigger_key] = []
        self.invalidation_rules[trigger_key].extend(affected_keys)
    
    async def _handle_invalidation(self, key: str):
        """处理智能失效"""
        if key in self.invalidation_rules:
            affected_keys = self.invalidation_rules[key]
            for affected_key in affected_keys:
                await self.delete(affected_key)
                logger.debug(f"Invalidated cache key: {affected_key}")
    
    async def _cleanup_expired_entries(self):
        """清理过期条目"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                
                # 清理内存缓存中的过期条目
                expired_keys = []
                for key, entry in self.memory_cache.cache.items():
                    if entry.is_expired():
                        expired_keys.append(key)
                
                for key in expired_keys:
                    await self.memory_cache.delete(key)
                    self.statistics.record_eviction()
                
                if expired_keys:
                    logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cache cleanup error: {e}")
    
    async def _performance_monitor(self):
        """性能监控"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟记录一次
                
                # 计算平均延迟
                for metric_name, values in self._performance_metrics.items():
                    if values:
                        avg_latency = sum(values) / len(values)
                        logger.debug(f"Cache {metric_name}: {avg_latency:.4f}s")
                        
                        # 保留最近100个值
                        if len(values) > 100:
                            self._performance_metrics[metric_name] = values[-100:]
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Performance monitor error: {e}")
    
    def _record_latency(self, metric_name: str, latency: float):
        """记录延迟"""
        if metric_name in self._performance_metrics:
            self._performance_metrics[metric_name].append(latency)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        memory_stats = self.memory_cache.get_stats()
        
        # 计算平均延迟
        avg_latencies = {}
        for metric_name, values in self._performance_metrics.items():
            if values:
                avg_latencies[metric_name] = sum(values) / len(values)
            else:
                avg_latencies[metric_name] = 0.0
        
        return {
            "statistics": self.statistics.dict(),
            "memory_cache": memory_stats,
            "performance": avg_latencies,
            "preload_patterns": len(self.preload_patterns),
            "invalidation_rules": len(self.invalidation_rules),
            "initialized": self._initialized
        }


# 全局缓存管理器实例
cache_manager = IntelligentCacheManager()


async def get_cache_manager() -> IntelligentCacheManager:
    """获取缓存管理器实例"""
    if not cache_manager._initialized:
        await cache_manager.initialize()
    return cache_manager