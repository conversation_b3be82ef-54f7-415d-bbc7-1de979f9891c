#!/usr/bin/env python3
"""
智能体学习机制 (Agent Learning System)
负责智能体的自适应学习、经验积累和能力提升
"""

import asyncio
import json
import uuid
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging

# from .base_agent import (
#     IAgent, BaseAgent, AgentMessage, MessageType, Priority,
#     TaskRequest, TaskResult, AgentCapability
# )

# Placeholder classes to avoid import errors
class IAgent: pass
class BaseAgent: pass
class AgentMessage: pass
class MessageType: pass
class Priority: pass
class TaskRequest: pass
class TaskResult: pass
class AgentCapability: pass

logger = logging.getLogger(__name__)

# ============================================================================
# 学习系统数据模型
# ============================================================================

class LearningType(Enum):
    """学习类型枚举"""
    SUPERVISED = "supervised"
    UNSUPERVISED = "unsupervised"
    REINFORCEMENT = "reinforcement"
    TRANSFER = "transfer"
    INCREMENTAL = "incremental"

class ExperienceType(Enum):
    """经验类型枚举"""
    SUCCESS = "success"
    FAILURE = "failure"
    FEEDBACK = "feedback"
    OBSERVATION = "observation"

class LearningStatus(Enum):
    """学习状态枚举"""
    IDLE = "idle"
    LEARNING = "learning"
    EVALUATING = "evaluating"
    UPDATING = "updating"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class Experience:
    """经验记录"""
    id: str
    agent_id: str
    experience_type: ExperienceType
    task_type: str
    context: Dict[str, Any]
    action: Dict[str, Any]
    result: Dict[str, Any]
    feedback: Optional[Dict[str, Any]] = None
    reward: Optional[float] = None
    timestamp: datetime = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.metadata is None:
            self.metadata = {}

@dataclass
class LearningPattern:
    """学习模式"""
    id: str
    name: str
    description: str
    pattern_type: str
    conditions: Dict[str, Any]
    actions: List[Dict[str, Any]]
    success_rate: float
    usage_count: int
    last_used: datetime
    confidence: float = 0.0

@dataclass
class KnowledgeItem:
    """知识项"""
    id: str
    category: str
    content: Any
    source: str
    confidence: float
    created_at: datetime
    updated_at: datetime
    usage_count: int = 0
    effectiveness: float = 0.0

@dataclass
class LearningSession:
    """学习会话"""
    id: str
    agent_id: str
    learning_type: LearningType
    objective: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: LearningStatus = LearningStatus.IDLE
    experiences_processed: int = 0
    patterns_learned: int = 0
    knowledge_updated: int = 0
    performance_improvement: float = 0.0
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

# ============================================================================
# 智能体学习系统
# ============================================================================

class AgentLearningSystem:
    """
    智能体学习系统
    负责管理智能体的学习过程、经验积累和知识更新
    """
    
    def __init__(self):
        self.experiences: Dict[str, List[Experience]] = {}
        self.patterns: Dict[str, List[LearningPattern]] = {}
        self.knowledge_base: Dict[str, Dict[str, KnowledgeItem]] = {}
        self.learning_sessions: Dict[str, LearningSession] = {}
        self.learning_algorithms: Dict[LearningType, Callable] = {}
        
        # 学习参数
        self.max_experiences_per_agent = 10000
        self.pattern_confidence_threshold = 0.7
        self.knowledge_decay_rate = 0.01
        self.learning_rate = 0.1
        
        # 初始化学习算法
        self._initialize_learning_algorithms()
    
    def _initialize_learning_algorithms(self):
        """初始化学习算法"""
        self.learning_algorithms = {
            LearningType.SUPERVISED: self._supervised_learning,
            LearningType.UNSUPERVISED: self._unsupervised_learning,
            LearningType.REINFORCEMENT: self._reinforcement_learning,
            LearningType.TRANSFER: self._transfer_learning,
            LearningType.INCREMENTAL: self._incremental_learning
        }
    
    async def record_experience(self, agent_id: str, task_request: TaskRequest,
                               task_result: TaskResult, feedback: Dict[str, Any] = None) -> str:
        """记录经验"""
        try:
            experience_id = f"exp_{uuid.uuid4().hex[:8]}"
            
            # 确定经验类型
            if task_result.status == "completed":
                exp_type = ExperienceType.SUCCESS
                reward = 1.0
            elif task_result.status == "failed":
                exp_type = ExperienceType.FAILURE
                reward = -1.0
            else:
                exp_type = ExperienceType.OBSERVATION
                reward = 0.0
            
            # 如果有反馈，调整奖励
            if feedback:
                exp_type = ExperienceType.FEEDBACK
                reward = feedback.get("reward", reward)
            
            experience = Experience(
                id=experience_id,
                agent_id=agent_id,
                experience_type=exp_type,
                task_type=task_request.type,
                context={
                    "task_description": task_request.description,
                    "parameters": task_request.parameters,
                    "priority": task_request.priority.value
                },
                action={
                    "execution_time": task_result.execution_time,
                    "approach": "default"
                },
                result={
                    "status": task_result.status,
                    "result": task_result.result
                },
                feedback=feedback,
                reward=reward
            )
            
            # 存储经验
            if agent_id not in self.experiences:
                self.experiences[agent_id] = []
            
            self.experiences[agent_id].append(experience)
            
            # 限制经验数量
            if len(self.experiences[agent_id]) > self.max_experiences_per_agent:
                self.experiences[agent_id].pop(0)
            
            logger.info(f"为智能体 {agent_id} 记录经验 {experience_id}")
            
            # 触发增量学习
            await self._trigger_incremental_learning(agent_id, experience)
            
            return experience_id
            
        except Exception as e:
            logger.error(f"记录经验失败: {e}")
            raise
    
    async def start_learning_session(self, agent_id: str, learning_type: LearningType,
                                   objective: str = "general_improvement") -> str:
        """开始学习会话"""
        try:
            session_id = f"session_{uuid.uuid4().hex[:8]}"
            
            session = LearningSession(
                id=session_id,
                agent_id=agent_id,
                learning_type=learning_type,
                objective=objective,
                start_time=datetime.now(),
                status=LearningStatus.LEARNING
            )
            
            self.learning_sessions[session_id] = session
            
            # 执行学习算法
            learning_algorithm = self.learning_algorithms.get(learning_type)
            if learning_algorithm:
                await learning_algorithm(agent_id, session)
            else:
                raise ValueError(f"不支持的学习类型: {learning_type}")
            
            session.status = LearningStatus.COMPLETED
            session.end_time = datetime.now()
            
            logger.info(f"智能体 {agent_id} 的学习会话 {session_id} 完成")
            return session_id
            
        except Exception as e:
            logger.error(f"学习会话失败: {e}")
            if session_id in self.learning_sessions:
                self.learning_sessions[session_id].status = LearningStatus.FAILED
            raise
    
    async def _supervised_learning(self, agent_id: str, session: LearningSession):
        """监督学习算法"""
        experiences = self.experiences.get(agent_id, [])
        
        # 筛选有标签的经验
        labeled_experiences = [
            exp for exp in experiences 
            if exp.experience_type in [ExperienceType.SUCCESS, ExperienceType.FAILURE]
        ]
        
        if len(labeled_experiences) < 10:
            logger.warning(f"智能体 {agent_id} 的标记经验不足，无法进行监督学习")
            return
        
        # 按任务类型分组学习
        task_groups = {}
        for exp in labeled_experiences:
            task_type = exp.task_type
            if task_type not in task_groups:
                task_groups[task_type] = []
            task_groups[task_type].append(exp)
        
        patterns_learned = 0
        for task_type, task_experiences in task_groups.items():
            if len(task_experiences) >= 5:
                pattern = await self._extract_success_pattern(task_type, task_experiences)
                if pattern:
                    await self._store_learning_pattern(agent_id, pattern)
                    patterns_learned += 1
        
        session.patterns_learned = patterns_learned
        session.experiences_processed = len(labeled_experiences)
    
    async def _unsupervised_learning(self, agent_id: str, session: LearningSession):
        """无监督学习算法"""
        experiences = self.experiences.get(agent_id, [])
        
        if len(experiences) < 20:
            logger.warning(f"智能体 {agent_id} 的经验不足，无法进行无监督学习")
            return
        
        # 聚类分析
        clusters = await self._cluster_experiences(experiences)
        
        patterns_learned = 0
        for cluster in clusters:
            if len(cluster) >= 3:
                pattern = await self._extract_cluster_pattern(cluster)
                if pattern:
                    await self._store_learning_pattern(agent_id, pattern)
                    patterns_learned += 1
        
        session.patterns_learned = patterns_learned
        session.experiences_processed = len(experiences)
    
    async def _reinforcement_learning(self, agent_id: str, session: LearningSession):
        """强化学习算法"""
        experiences = self.experiences.get(agent_id, [])
        
        # 筛选有奖励的经验
        reward_experiences = [exp for exp in experiences if exp.reward is not None]
        
        if len(reward_experiences) < 10:
            logger.warning(f"智能体 {agent_id} 的奖励经验不足，无法进行强化学习")
            return
        
        # 更新价值函数和策略
        knowledge_updated = 0
        
        # 按任务类型分组
        task_groups = {}
        for exp in reward_experiences:
            task_type = exp.task_type
            if task_type not in task_groups:
                task_groups[task_type] = []
            task_groups[task_type].append(exp)
        
        for task_type, task_experiences in task_groups.items():
            # 计算平均奖励
            avg_reward = statistics.mean([exp.reward for exp in task_experiences])
            
            # 更新知识库中的价值估计
            knowledge_item = KnowledgeItem(
                id=f"value_{task_type}_{uuid.uuid4().hex[:4]}",
                category="task_value",
                content={"task_type": task_type, "expected_reward": avg_reward},
                source="reinforcement_learning",
                confidence=min(1.0, len(task_experiences) / 20.0),
                created_at=datetime.now(),
                updated_at=datetime.now(),
                effectiveness=avg_reward
            )
            
            await self._store_knowledge_item(agent_id, knowledge_item)
            knowledge_updated += 1
        
        session.knowledge_updated = knowledge_updated
        session.experiences_processed = len(reward_experiences)
    
    async def _transfer_learning(self, agent_id: str, session: LearningSession):
        """迁移学习算法"""
        # 从其他智能体学习经验
        source_agents = [aid for aid in self.experiences.keys() if aid != agent_id]
        
        if not source_agents:
            logger.warning("没有其他智能体的经验可供迁移学习")
            return
        
        transferred_patterns = 0
        
        for source_agent_id in source_agents:
            source_patterns = self.patterns.get(source_agent_id, [])
            
            for pattern in source_patterns:
                # 评估模式的可迁移性
                if pattern.success_rate > 0.8 and pattern.confidence > 0.7:
                    # 创建迁移的模式
                    transferred_pattern = LearningPattern(
                        id=f"transfer_{uuid.uuid4().hex[:8]}",
                        name=f"Transferred_{pattern.name}",
                        description=f"从 {source_agent_id} 迁移的模式: {pattern.description}",
                        pattern_type=pattern.pattern_type,
                        conditions=pattern.conditions.copy(),
                        actions=pattern.actions.copy(),
                        success_rate=pattern.success_rate * 0.8,
                        usage_count=0,
                        last_used=datetime.now(),
                        confidence=pattern.confidence * 0.7
                    )
                    
                    await self._store_learning_pattern(agent_id, transferred_pattern)
                    transferred_patterns += 1
        
        session.patterns_learned = transferred_patterns
    
    async def _incremental_learning(self, agent_id: str, session: LearningSession):
        """增量学习算法"""
        # 获取最近的经验
        recent_experiences = self._get_recent_experiences(agent_id, hours=24)
        
        if len(recent_experiences) < 5:
            return
        
        # 更新现有模式
        patterns_updated = 0
        existing_patterns = self.patterns.get(agent_id, [])
        
        for pattern in existing_patterns:
            # 查找相关经验
            relevant_experiences = [
                exp for exp in recent_experiences
                if self._matches_pattern_conditions(exp, pattern.conditions)
            ]
            
            if relevant_experiences:
                # 更新模式统计
                success_count = len([exp for exp in relevant_experiences 
                                   if exp.experience_type == ExperienceType.SUCCESS])
                total_count = len(relevant_experiences)
                
                # 使用指数移动平均更新成功率
                new_success_rate = success_count / total_count
                pattern.success_rate = (
                    pattern.success_rate * 0.8 + new_success_rate * 0.2
                )
                pattern.usage_count += total_count
                pattern.last_used = datetime.now()
                
                patterns_updated += 1
        
        session.patterns_learned = patterns_updated
        session.experiences_processed = len(recent_experiences)
    
    async def _trigger_incremental_learning(self, agent_id: str, experience: Experience):
        """触发增量学习"""
        try:
            # 每10个新经验触发一次增量学习
            agent_experiences = self.experiences.get(agent_id, [])
            if len(agent_experiences) % 10 == 0:
                session_id = await self.start_learning_session(
                    agent_id, LearningType.INCREMENTAL, "auto_incremental"
                )
                logger.info(f"为智能体 {agent_id} 触发增量学习会话 {session_id}")
        except Exception as e:
            logger.error(f"触发增量学习失败: {e}")
    
    async def _extract_success_pattern(self, task_type: str, 
                                     experiences: List[Experience]) -> Optional[LearningPattern]:
        """从成功经验中提取模式"""
        success_experiences = [
            exp for exp in experiences 
            if exp.experience_type == ExperienceType.SUCCESS
        ]
        
        if len(success_experiences) < 3:
            return None
        
        # 分析成功经验的共同特征
        common_conditions = {}
        common_actions = []
        
        # 提取共同的上下文条件
        for exp in success_experiences:
            for key, value in exp.context.items():
                if key not in common_conditions:
                    common_conditions[key] = []
                common_conditions[key].append(value)
        
        # 过滤出现频率高的条件
        filtered_conditions = {}
        for key, values in common_conditions.items():
            if len(set(values)) <= len(values) * 0.5:
                filtered_conditions[key] = max(set(values), key=values.count)
        
        # 提取共同的行动模式
        for exp in success_experiences:
            common_actions.append(exp.action)
        
        success_rate = len(success_experiences) / len(experiences)
        
        pattern = LearningPattern(
            id=f"pattern_{uuid.uuid4().hex[:8]}",
            name=f"Success_Pattern_{task_type}",
            description=f"从{len(success_experiences)}个成功经验中提取的{task_type}任务模式",
            pattern_type="success_pattern",
            conditions=filtered_conditions,
            actions=common_actions,
            success_rate=success_rate,
            usage_count=0,
            last_used=datetime.now(),
            confidence=min(1.0, len(success_experiences) / 10.0)
        )
        
        return pattern
    
    async def _cluster_experiences(self, experiences: List[Experience]) -> List[List[Experience]]:
        """对经验进行聚类分析"""
        # 简化的聚类实现
        clusters = {}
        
        for exp in experiences:
            cluster_key = (
                exp.task_type,
                exp.experience_type.value,
                exp.result.get("status", "unknown")
            )
            
            if cluster_key not in clusters:
                clusters[cluster_key] = []
            clusters[cluster_key].append(exp)
        
        return [cluster for cluster in clusters.values() if len(cluster) > 1]
    
    async def _extract_cluster_pattern(self, cluster: List[Experience]) -> Optional[LearningPattern]:
        """从聚类中提取模式"""
        if len(cluster) < 3:
            return None
        
        task_types = [exp.task_type for exp in cluster]
        most_common_task_type = max(set(task_types), key=task_types.count)
        
        execution_times = [
            exp.action.get("execution_time", 0) for exp in cluster
        ]
        avg_execution_time = statistics.mean(execution_times) if execution_times else 0
        
        pattern = LearningPattern(
            id=f"cluster_pattern_{uuid.uuid4().hex[:8]}",
            name=f"Cluster_Pattern_{most_common_task_type}",
            description=f"从{len(cluster)}个相似经验中发现的模式",
            pattern_type="cluster_pattern",
            conditions={"task_type": most_common_task_type},
            actions=[{"avg_execution_time": avg_execution_time}],
            success_rate=0.5,
            usage_count=0,
            last_used=datetime.now(),
            confidence=min(1.0, len(cluster) / 10.0)
        )
        
        return pattern
    
    async def _store_learning_pattern(self, agent_id: str, pattern: LearningPattern):
        """存储学习模式"""
        if agent_id not in self.patterns:
            self.patterns[agent_id] = []
        
        # 检查是否已存在相似模式
        existing_pattern = self._find_similar_pattern(agent_id, pattern)
        if existing_pattern:
            # 合并模式
            existing_pattern.success_rate = (
                existing_pattern.success_rate * 0.7 + pattern.success_rate * 0.3
            )
            existing_pattern.confidence = max(existing_pattern.confidence, pattern.confidence)
            existing_pattern.last_used = datetime.now()
        else:
            # 添加新模式
            self.patterns[agent_id].append(pattern)
        
        logger.info(f"为智能体 {agent_id} 存储学习模式 {pattern.id}")
    
    async def _store_knowledge_item(self, agent_id: str, knowledge_item: KnowledgeItem):
        """存储知识项"""
        if agent_id not in self.knowledge_base:
            self.knowledge_base[agent_id] = {}
        
        self.knowledge_base[agent_id][knowledge_item.id] = knowledge_item
        logger.info(f"为智能体 {agent_id} 存储知识项 {knowledge_item.id}")
    
    def _find_similar_pattern(self, agent_id: str, pattern: LearningPattern) -> Optional[LearningPattern]:
        """查找相似模式"""
        existing_patterns = self.patterns.get(agent_id, [])
        
        for existing_pattern in existing_patterns:
            if (existing_pattern.pattern_type == pattern.pattern_type and
                existing_pattern.conditions == pattern.conditions):
                return existing_pattern
        
        return None
    
    def _get_recent_experiences(self, agent_id: str, hours: int = 24) -> List[Experience]:
        """获取最近的经验"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        experiences = self.experiences.get(agent_id, [])
        
        return [exp for exp in experiences if exp.timestamp >= cutoff_time]
    
    def _matches_pattern_conditions(self, experience: Experience, 
                                  conditions: Dict[str, Any]) -> bool:
        """检查经验是否匹配模式条件"""
        for key, expected_value in conditions.items():
            if key in experience.context:
                if experience.context[key] != expected_value:
                    return False
            elif key == "task_type":
                if experience.task_type != expected_value:
                    return False
        
        return True
    
    def get_agent_patterns(self, agent_id: str) -> List[LearningPattern]:
        """获取智能体的学习模式"""
        return self.patterns.get(agent_id, [])
    
    def get_agent_knowledge(self, agent_id: str) -> Dict[str, KnowledgeItem]:
        """获取智能体的知识库"""
        return self.knowledge_base.get(agent_id, {})
    
    def get_learning_statistics(self, agent_id: str = None) -> Dict[str, Any]:
        """获取学习统计信息"""
        if agent_id:
            # 单个智能体的统计
            experiences = self.experiences.get(agent_id, [])
            patterns = self.patterns.get(agent_id, [])
            knowledge = self.knowledge_base.get(agent_id, {})
            
            return {
                "agent_id": agent_id,
                "total_experiences": len(experiences),
                "total_patterns": len(patterns),
                "total_knowledge_items": len(knowledge),
                "experience_types": self._count_experience_types(experiences),
                "pattern_confidence_avg": statistics.mean([p.confidence for p in patterns]) if patterns else 0,
                "recent_learning_activity": len(self._get_recent_experiences(agent_id, hours=24))
            }
        else:
            # 全局统计
            total_experiences = sum(len(exps) for exps in self.experiences.values())
            total_patterns = sum(len(patterns) for patterns in self.patterns.values())
            total_knowledge = sum(len(kb) for kb in self.knowledge_base.values())
            
            return {
                "total_agents": len(self.experiences),
                "total_experiences": total_experiences,
                "total_patterns": total_patterns,
                "total_knowledge_items": total_knowledge,
                "active_learning_sessions": len([s for s in self.learning_sessions.values() 
                                               if s.status == LearningStatus.LEARNING])
            }
    
    def _count_experience_types(self, experiences: List[Experience]) -> Dict[str, int]:
        """统计经验类型"""
        counts = {}
        for exp in experiences:
            exp_type = exp.experience_type.value
            counts[exp_type] = counts.get(exp_type, 0) + 1
        return counts
    
    def export_learning_data(self, agent_id: str, format: str = "json") -> str:
        """导出学习数据"""
        data = {
            "agent_id": agent_id,
            "export_timestamp": datetime.now().isoformat(),
            "experiences": [asdict(exp) for exp in self.experiences.get(agent_id, [])],
            "patterns": [asdict(pattern) for pattern in self.patterns.get(agent_id, [])],
            "knowledge": {k: asdict(v) for k, v in self.knowledge_base.get(agent_id, {}).items()}
        }
        
        if format.lower() == "json":
            return json.dumps(data, default=str, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的导出格式: {format}")