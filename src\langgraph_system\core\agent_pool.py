"""
智能体池化管理系统 - v0.3增强版
支持智能体资源池化、负载均衡和动态扩缩容
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Type, Callable, Set
from enum import Enum
from dataclasses import dataclass, field
import uuid
import weakref

from pydantic import BaseModel, Field

from ..agents.base_v3 import SpecialistAgent, AgentCapability
from ..states.project_state import AgentStatus
from .exceptions import AgentPoolError, AgentNotAvailableError

logger = logging.getLogger(__name__)


class PoolStrategy(str, Enum):
    """池化策略"""
    FIXED = "fixed"  # 固定大小
    DYNAMIC = "dynamic"  # 动态调整
    ADAPTIVE = "adaptive"  # 自适应


class LoadBalanceStrategy(str, Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"  # 轮询
    LEAST_CONNECTIONS = "least_connections"  # 最少连接
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"  # 加权轮询
    LEAST_RESPONSE_TIME = "least_response_time"  # 最短响应时间
    CAPABILITY_BASED = "capability_based"  # 基于能力


@dataclass
class AgentMetrics:
    """智能体指标"""
    agent_id: str
    total_requests: int = 0
    active_requests: int = 0
    completed_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    last_request_time: Optional[float] = None
    created_at: float = field(default_factory=time.time)
    last_active: float = field(default_factory=time.time)
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        if self.completed_requests == 0:
            return 0.0
        return self.total_response_time / self.completed_requests
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.completed_requests / self.total_requests
    
    @property
    def load_factor(self) -> float:
        """负载因子"""
        return min(self.active_requests / 10.0, 1.0)  # 假设最大并发为10
    
    def update_request_start(self):
        """更新请求开始"""
        self.total_requests += 1
        self.active_requests += 1
        self.last_request_time = time.time()
        self.last_active = time.time()
    
    def update_request_complete(self, response_time: float, success: bool):
        """更新请求完成"""
        self.active_requests = max(0, self.active_requests - 1)
        self.total_response_time += response_time
        
        if success:
            self.completed_requests += 1
        else:
            self.failed_requests += 1


@dataclass
class PooledAgent:
    """池化智能体"""
    agent_id: str
    agent_instance: SpecialistAgent
    agent_type: str
    status: AgentStatus
    metrics: AgentMetrics
    capabilities: Dict[str, float]
    created_at: float = field(default_factory=time.time)
    last_used: float = field(default_factory=time.time)
    max_concurrent_tasks: int = 5
    current_tasks: Set[str] = field(default_factory=set)
    
    @property
    def is_available(self) -> bool:
        """是否可用"""
        return (self.status == AgentStatus.AVAILABLE and 
                len(self.current_tasks) < self.max_concurrent_tasks)
    
    @property
    def utilization(self) -> float:
        """利用率"""
        return len(self.current_tasks) / self.max_concurrent_tasks
    
    def assign_task(self, task_id: str):
        """分配任务"""
        if len(self.current_tasks) >= self.max_concurrent_tasks:
            raise AgentNotAvailableError(f"Agent {self.agent_id} is at capacity")
        
        self.current_tasks.add(task_id)
        self.status = AgentStatus.WORKING
        self.last_used = time.time()
        self.metrics.update_request_start()
    
    def complete_task(self, task_id: str, response_time: float, success: bool):
        """完成任务"""
        if task_id in self.current_tasks:
            self.current_tasks.remove(task_id)
        
        if not self.current_tasks:
            self.status = AgentStatus.AVAILABLE
        
        self.metrics.update_request_complete(response_time, success)
    
    def get_capability_score(self, required_capabilities: Dict[str, float]) -> float:
        """获取能力匹配分数"""
        if not required_capabilities:
            return 1.0
        
        total_score = 0.0
        total_weight = 0.0
        
        for capability, required_level in required_capabilities.items():
            agent_level = self.capabilities.get(capability, 0.0)
            weight = required_level
            
            # 计算匹配分数
            if agent_level >= required_level:
                score = 1.0
            else:
                score = agent_level / required_level if required_level > 0 else 0.0
            
            total_score += score * weight
            total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0


class AgentPool:
    """智能体池"""
    
    def __init__(self, agent_type: str, 
                 min_size: int = 1, 
                 max_size: int = 10,
                 strategy: PoolStrategy = PoolStrategy.DYNAMIC,
                 load_balance_strategy: LoadBalanceStrategy = LoadBalanceStrategy.CAPABILITY_BASED):
        self.agent_type = agent_type
        self.min_size = min_size
        self.max_size = max_size
        self.strategy = strategy
        self.load_balance_strategy = load_balance_strategy
        
        self.agents: Dict[str, PooledAgent] = {}
        self.agent_factory: Optional[Callable[[], SpecialistAgent]] = None
        self.round_robin_index = 0
        
        self._lock = asyncio.Lock()
        self._scaling_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        
        # 配置参数
        self.scale_up_threshold = 0.8  # 平均利用率超过80%时扩容
        self.scale_down_threshold = 0.3  # 平均利用率低于30%时缩容
        self.idle_timeout = 300  # 5分钟空闲超时
        self.health_check_interval = 60  # 1分钟健康检查间隔
    
    def set_agent_factory(self, factory: Callable[[], SpecialistAgent]):
        """设置智能体工厂函数"""
        self.agent_factory = factory
    
    async def initialize(self):
        """初始化池"""
        if not self.agent_factory:
            raise AgentPoolError(f"Agent factory not set for pool {self.agent_type}")
        
        # 创建最小数量的智能体
        for _ in range(self.min_size):
            await self._create_agent()
        
        # 启动后台任务
        if self.strategy in [PoolStrategy.DYNAMIC, PoolStrategy.ADAPTIVE]:
            self._scaling_task = asyncio.create_task(self._auto_scaling())
        
        self._cleanup_task = asyncio.create_task(self._cleanup_idle_agents())
        
        logger.info(f"Agent pool initialized: {self.agent_type} (size: {len(self.agents)})")
    
    async def shutdown(self):
        """关闭池"""
        # 停止后台任务
        if self._scaling_task:
            self._scaling_task.cancel()
            try:
                await self._scaling_task
            except asyncio.CancelledError:
                pass
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 清理所有智能体
        async with self._lock:
            for agent in self.agents.values():
                await self._destroy_agent(agent)
            self.agents.clear()
        
        logger.info(f"Agent pool shutdown: {self.agent_type}")
    
    async def get_agent(self, required_capabilities: Optional[Dict[str, float]] = None,
                       timeout: float = 30.0) -> PooledAgent:
        """获取智能体"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            async with self._lock:
                # 查找可用智能体
                available_agents = [agent for agent in self.agents.values() if agent.is_available]
                
                if available_agents:
                    # 根据负载均衡策略选择智能体
                    selected_agent = self._select_agent(available_agents, required_capabilities)
                    return selected_agent
                
                # 如果没有可用智能体，尝试创建新的
                if len(self.agents) < self.max_size:
                    new_agent = await self._create_agent()
                    if new_agent and new_agent.is_available:
                        return new_agent
            
            # 等待一段时间后重试
            await asyncio.sleep(0.1)
        
        raise AgentNotAvailableError(f"No available agent in pool {self.agent_type} within timeout")
    
    async def return_agent(self, agent: PooledAgent):
        """归还智能体"""
        async with self._lock:
            if agent.agent_id in self.agents:
                # 重置智能体状态
                if not agent.current_tasks:
                    agent.status = AgentStatus.AVAILABLE
                agent.last_used = time.time()
    
    def _select_agent(self, available_agents: List[PooledAgent], 
                     required_capabilities: Optional[Dict[str, float]] = None) -> PooledAgent:
        """选择智能体"""
        if not available_agents:
            raise AgentNotAvailableError("No available agents")
        
        if self.load_balance_strategy == LoadBalanceStrategy.ROUND_ROBIN:
            # 轮询
            agent = available_agents[self.round_robin_index % len(available_agents)]
            self.round_robin_index += 1
            return agent
        
        elif self.load_balance_strategy == LoadBalanceStrategy.LEAST_CONNECTIONS:
            # 最少连接
            return min(available_agents, key=lambda a: len(a.current_tasks))
        
        elif self.load_balance_strategy == LoadBalanceStrategy.LEAST_RESPONSE_TIME:
            # 最短响应时间
            return min(available_agents, key=lambda a: a.metrics.average_response_time)
        
        elif self.load_balance_strategy == LoadBalanceStrategy.CAPABILITY_BASED:
            # 基于能力
            if required_capabilities:
                # 计算能力匹配分数
                scored_agents = [
                    (agent, agent.get_capability_score(required_capabilities))
                    for agent in available_agents
                ]
                # 选择分数最高的智能体
                return max(scored_agents, key=lambda x: x[1])[0]
            else:
                # 如果没有特定能力要求，选择负载最低的
                return min(available_agents, key=lambda a: a.utilization)
        
        else:
            # 默认选择第一个
            return available_agents[0]
    
    async def _create_agent(self) -> Optional[PooledAgent]:
        """创建智能体"""
        if not self.agent_factory:
            return None
        
        try:
            # 创建智能体实例
            agent_instance = self.agent_factory()
            agent_id = str(uuid.uuid4())
            
            # 创建池化智能体
            pooled_agent = PooledAgent(
                agent_id=agent_id,
                agent_instance=agent_instance,
                agent_type=self.agent_type,
                status=AgentStatus.AVAILABLE,
                metrics=AgentMetrics(agent_id=agent_id),
                capabilities=getattr(agent_instance, 'capabilities', {})
            )
            
            self.agents[agent_id] = pooled_agent
            logger.debug(f"Created agent: {agent_id} in pool {self.agent_type}")
            
            return pooled_agent
            
        except Exception as e:
            logger.error(f"Failed to create agent in pool {self.agent_type}: {e}")
            return None
    
    async def _destroy_agent(self, agent: PooledAgent):
        """销毁智能体"""
        try:
            # 等待当前任务完成
            while agent.current_tasks:
                await asyncio.sleep(0.1)
            
            # 清理资源
            if hasattr(agent.agent_instance, 'cleanup'):
                await agent.agent_instance.cleanup()
            
            # 从池中移除
            if agent.agent_id in self.agents:
                del self.agents[agent.agent_id]
            
            logger.debug(f"Destroyed agent: {agent.agent_id} in pool {self.agent_type}")
            
        except Exception as e:
            logger.error(f"Failed to destroy agent {agent.agent_id}: {e}")
    
    async def _auto_scaling(self):
        """自动扩缩容"""
        while True:
            try:
                await asyncio.sleep(30)  # 每30秒检查一次
                
                async with self._lock:
                    if not self.agents:
                        continue
                    
                    # 计算平均利用率
                    total_utilization = sum(agent.utilization for agent in self.agents.values())
                    avg_utilization = total_utilization / len(self.agents)
                    
                    # 扩容逻辑
                    if (avg_utilization > self.scale_up_threshold and 
                        len(self.agents) < self.max_size):
                        await self._create_agent()
                        logger.info(f"Scaled up pool {self.agent_type}: {len(self.agents)} agents")
                    
                    # 缩容逻辑
                    elif (avg_utilization < self.scale_down_threshold and 
                          len(self.agents) > self.min_size):
                        # 找到最空闲的智能体
                        idle_agents = [agent for agent in self.agents.values() 
                                     if agent.is_available and not agent.current_tasks]
                        
                        if idle_agents:
                            # 选择最久未使用的智能体
                            oldest_idle = min(idle_agents, key=lambda a: a.last_used)
                            await self._destroy_agent(oldest_idle)
                            logger.info(f"Scaled down pool {self.agent_type}: {len(self.agents)} agents")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Auto scaling error in pool {self.agent_type}: {e}")
    
    async def _cleanup_idle_agents(self):
        """清理空闲智能体"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                current_time = time.time()
                
                async with self._lock:
                    agents_to_remove = []
                    
                    for agent in self.agents.values():
                        # 检查是否长时间空闲
                        if (agent.status == AgentStatus.AVAILABLE and 
                            not agent.current_tasks and
                            current_time - agent.last_used > self.idle_timeout and
                            len(self.agents) > self.min_size):
                            agents_to_remove.append(agent)
                    
                    # 移除空闲智能体
                    for agent in agents_to_remove:
                        await self._destroy_agent(agent)
                        logger.debug(f"Removed idle agent: {agent.agent_id}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup error in pool {self.agent_type}: {e}")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取池统计信息"""
        if not self.agents:
            return {
                "agent_type": self.agent_type,
                "total_agents": 0,
                "available_agents": 0,
                "working_agents": 0,
                "average_utilization": 0.0,
                "total_requests": 0,
                "average_response_time": 0.0
            }
        
        available_count = sum(1 for agent in self.agents.values() if agent.is_available)
        working_count = sum(1 for agent in self.agents.values() if agent.status == AgentStatus.WORKING)
        total_utilization = sum(agent.utilization for agent in self.agents.values())
        avg_utilization = total_utilization / len(self.agents)
        
        total_requests = sum(agent.metrics.total_requests for agent in self.agents.values())
        total_response_time = sum(agent.metrics.total_response_time for agent in self.agents.values())
        completed_requests = sum(agent.metrics.completed_requests for agent in self.agents.values())
        avg_response_time = total_response_time / completed_requests if completed_requests > 0 else 0.0
        
        return {
            "agent_type": self.agent_type,
            "total_agents": len(self.agents),
            "available_agents": available_count,
            "working_agents": working_count,
            "average_utilization": avg_utilization,
            "total_requests": total_requests,
            "average_response_time": avg_response_time,
            "min_size": self.min_size,
            "max_size": self.max_size,
            "strategy": self.strategy.value,
            "load_balance_strategy": self.load_balance_strategy.value
        }


class AgentPoolManager:
    """智能体池管理器"""
    
    def __init__(self):
        self.pools: Dict[str, AgentPool] = {}
        self.agent_factories: Dict[str, Callable[[], SpecialistAgent]] = {}
        self._initialized = False
    
    async def initialize(self):
        """初始化池管理器"""
        if self._initialized:
            return
        
        # 初始化所有池
        for pool in self.pools.values():
            await pool.initialize()
        
        self._initialized = True
        logger.info("AgentPoolManager initialized successfully")
    
    async def shutdown(self):
        """关闭池管理器"""
        for pool in self.pools.values():
            await pool.shutdown()
        
        self.pools.clear()
        self._initialized = False
        logger.info("AgentPoolManager shutdown completed")
    
    def register_agent_type(self, agent_type: str, 
                          factory: Callable[[], SpecialistAgent],
                          min_size: int = 1,
                          max_size: int = 10,
                          strategy: PoolStrategy = PoolStrategy.DYNAMIC,
                          load_balance_strategy: LoadBalanceStrategy = LoadBalanceStrategy.CAPABILITY_BASED):
        """注册智能体类型"""
        self.agent_factories[agent_type] = factory
        
        pool = AgentPool(
            agent_type=agent_type,
            min_size=min_size,
            max_size=max_size,
            strategy=strategy,
            load_balance_strategy=load_balance_strategy
        )
        pool.set_agent_factory(factory)
        
        self.pools[agent_type] = pool
        logger.info(f"Registered agent type: {agent_type}")
    
    async def get_agent(self, agent_type: str, 
                       required_capabilities: Optional[Dict[str, float]] = None,
                       timeout: float = 30.0) -> PooledAgent:
        """获取智能体"""
        if agent_type not in self.pools:
            raise AgentPoolError(f"Agent type not registered: {agent_type}")
        
        pool = self.pools[agent_type]
        return await pool.get_agent(required_capabilities, timeout)
    
    async def return_agent(self, agent: PooledAgent):
        """归还智能体"""
        if agent.agent_type in self.pools:
            pool = self.pools[agent.agent_type]
            await pool.return_agent(agent)
    
    async def execute_with_agent(self, agent_type: str, 
                                task_func: Callable,
                                required_capabilities: Optional[Dict[str, float]] = None,
                                timeout: float = 30.0) -> Any:
        """使用智能体执行任务"""
        agent = await self.get_agent(agent_type, required_capabilities, timeout)
        task_id = str(uuid.uuid4())
        
        try:
            # 分配任务
            agent.assign_task(task_id)
            
            # 执行任务
            start_time = time.time()
            
            if asyncio.iscoroutinefunction(task_func):
                result = await task_func(agent.agent_instance)
            else:
                result = task_func(agent.agent_instance)
            
            # 记录成功
            response_time = time.time() - start_time
            agent.complete_task(task_id, response_time, True)
            
            return result
            
        except Exception as e:
            # 记录失败
            response_time = time.time() - start_time
            agent.complete_task(task_id, response_time, False)
            raise
        
        finally:
            # 归还智能体
            await self.return_agent(agent)
    
    def get_all_stats(self) -> Dict[str, Any]:
        """获取所有池的统计信息"""
        return {
            agent_type: pool.get_pool_stats()
            for agent_type, pool in self.pools.items()
        }
    
    def get_pool_stats(self, agent_type: str) -> Optional[Dict[str, Any]]:
        """获取特定池的统计信息"""
        if agent_type in self.pools:
            return self.pools[agent_type].get_pool_stats()
        return None


# 全局池管理器实例
pool_manager = AgentPoolManager()


async def get_pool_manager() -> AgentPoolManager:
    """获取池管理器实例"""
    if not pool_manager._initialized:
        await pool_manager.initialize()
    return pool_manager