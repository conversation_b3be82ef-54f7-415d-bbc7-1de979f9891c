"""
Streamlit Web界面组件
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List
import json

def render_system_metrics(system_info: Dict[str, Any]):
    """渲染系统指标仪表板"""
    
    # 创建指标卡片
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="🤖 智能体数量",
            value=len(system_info.get('available_agents', [])),
            delta=None
        )
    
    with col2:
        st.metric(
            label="🛠️ 工具数量", 
            value=len(system_info.get('available_tools', [])),
            delta=None
        )
    
    with col3:
        st.metric(
            label="📊 系统版本",
            value=system_info.get('version', 'N/A'),
            delta=None
        )
    
    with col4:
        status = "🟢 运行中" if system_info.get('initialized') else "🔴 未初始化"
        st.metric(
            label="⚡ 系统状态",
            value=status,
            delta=None
        )

def render_agent_capabilities_chart(agents_info: Dict[str, Any]):
    """渲染智能体能力图表"""
    
    if not agents_info:
        st.info("暂无智能体信息")
        return
    
    # 准备数据
    agent_data = []
    for agent_name, info in agents_info.items():
        capabilities = info.get('capabilities', [])
        for capability in capabilities:
            agent_data.append({
                'Agent': agent_name,
                'Capability': capability,
                'Count': 1
            })
    
    if not agent_data:
        st.info("暂无能力数据")
        return
    
    df = pd.DataFrame(agent_data)
    
    # 创建能力分布图
    fig = px.sunburst(
        df, 
        path=['Agent', 'Capability'], 
        values='Count',
        title="智能体能力分布图"
    )
    
    fig.update_layout(
        font_size=12,
        title_font_size=16
    )
    
    st.plotly_chart(fig, use_container_width=True)

def render_task_history_timeline(task_history: List[Dict[str, Any]]):
    """渲染任务历史时间线"""
    
    if not task_history:
        st.info("暂无任务历史")
        return
    
    # 准备时间线数据
    timeline_data = []
    for task in task_history:
        timestamp = datetime.fromisoformat(task['timestamp'].replace('Z', '+00:00'))
        status = task.get('result', {}).get('status', 'unknown')
        
        timeline_data.append({
            'Project': task['project_name'],
            'Task Type': task['task_type'],
            'Timestamp': timestamp,
            'Status': status,
            'Duration': 1  # 假设每个任务持续1个单位时间
        })
    
    df = pd.DataFrame(timeline_data)
    
    # 创建甘特图
    fig = px.timeline(
        df,
        x_start='Timestamp',
        x_end='Timestamp',
        y='Project',
        color='Status',
        title="任务执行时间线",
        color_discrete_map={
            'success': '#28a745',
            'failed': '#dc3545',
            'unknown': '#6c757d'
        }
    )
    
    fig.update_layout(
        xaxis_title="时间",
        yaxis_title="项目",
        font_size=12,
        title_font_size=16
    )
    
    st.plotly_chart(fig, use_container_width=True)

def render_tool_usage_stats(tools_info: List[str]):
    """渲染工具使用统计"""
    
    if not tools_info:
        st.info("暂无工具信息")
        return
    
    # 按工具类型分类
    tool_categories = {
        '文件操作': [],
        '数据处理': [],
        '系统操作': [],
        '其他': []
    }
    
    for tool in tools_info:
        if 'file' in tool.lower():
            tool_categories['文件操作'].append(tool)
        elif 'json' in tool.lower() or 'data' in tool.lower():
            tool_categories['数据处理'].append(tool)
        elif 'command' in tool.lower() or 'execute' in tool.lower():
            tool_categories['系统操作'].append(tool)
        else:
            tool_categories['其他'].append(tool)
    
    # 创建饼图数据
    categories = []
    counts = []
    for category, tools in tool_categories.items():
        if tools:  # 只包含非空类别
            categories.append(category)
            counts.append(len(tools))
    
    if not categories:
        st.info("暂无工具分类数据")
        return
    
    # 创建饼图
    fig = go.Figure(data=[go.Pie(
        labels=categories,
        values=counts,
        hole=0.3,
        textinfo='label+percent',
        textposition='outside'
    )])
    
    fig.update_layout(
        title="工具类型分布",
        font_size=12,
        title_font_size=16,
        showlegend=True
    )
    
    st.plotly_chart(fig, use_container_width=True)

def render_project_status_card(project_info: Dict[str, Any]):
    """渲染项目状态卡片"""
    
    if not project_info:
        st.info("暂无当前项目")
        return
    
    with st.container():
        st.markdown("### 📋 当前项目")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.markdown(f"**项目名称:** {project_info.get('project_name', 'N/A')}")
            st.markdown(f"**任务类型:** {project_info.get('task_type', 'N/A')}")
            st.markdown(f"**创建时间:** {project_info.get('timestamp', 'N/A')[:19]}")
            
            # 项目描述
            description = project_info.get('description', '')
            if description:
                with st.expander("📝 项目描述"):
                    st.write(description)
        
        with col2:
            result = project_info.get('result', {})
            status = result.get('status', 'unknown')
            
            if status == 'success':
                st.success("✅ 执行成功")
            elif status == 'failed':
                st.error("❌ 执行失败")
            else:
                st.info("⏳ 状态未知")
            
            # 执行时间
            if 'execution_time' in result:
                st.metric("⏱️ 执行时间", result['execution_time'])

def render_real_time_logs(logs: List[str], max_lines: int = 50):
    """渲染实时日志"""
    
    st.markdown("### 📜 实时日志")
    
    # 创建日志容器
    log_container = st.container()
    
    with log_container:
        if logs:
            # 只显示最新的日志
            recent_logs = logs[-max_lines:] if len(logs) > max_lines else logs
            
            for i, log in enumerate(recent_logs):
                # 根据日志级别设置颜色
                if 'ERROR' in log:
                    st.markdown(f'<span style="color: red">{log}</span>', unsafe_allow_html=True)
                elif 'WARNING' in log:
                    st.markdown(f'<span style="color: orange">{log}</span>', unsafe_allow_html=True)
                elif 'INFO' in log:
                    st.markdown(f'<span style="color: blue">{log}</span>', unsafe_allow_html=True)
                else:
                    st.text(log)
        else:
            st.info("暂无日志信息")

def render_agent_performance_chart(performance_data: Dict[str, Any]):
    """渲染智能体性能图表"""
    
    if not performance_data:
        st.info("暂无性能数据")
        return
    
    # 创建性能指标图表
    agents = list(performance_data.keys())
    response_times = [performance_data[agent].get('response_time', 0) for agent in agents]
    success_rates = [performance_data[agent].get('success_rate', 0) for agent in agents]
    
    fig = go.Figure()
    
    # 添加响应时间柱状图
    fig.add_trace(go.Bar(
        name='响应时间 (ms)',
        x=agents,
        y=response_times,
        yaxis='y',
        offsetgroup=1
    ))
    
    # 添加成功率折线图
    fig.add_trace(go.Scatter(
        name='成功率 (%)',
        x=agents,
        y=success_rates,
        yaxis='y2',
        mode='lines+markers',
        line=dict(color='red')
    ))
    
    # 设置双Y轴
    fig.update_layout(
        title='智能体性能指标',
        xaxis=dict(title='智能体'),
        yaxis=dict(title='响应时间 (ms)', side='left'),
        yaxis2=dict(title='成功率 (%)', side='right', overlaying='y'),
        legend=dict(x=0.7, y=1)
    )
    
    st.plotly_chart(fig, use_container_width=True)

def render_system_health_indicator():
    """渲染系统健康指示器"""
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # CPU使用率 (模拟数据)
        cpu_usage = 45  # 可以从实际系统获取
        color = "normal" if cpu_usage < 70 else "inverse"
        st.metric(
            label="💻 CPU使用率",
            value=f"{cpu_usage}%",
            delta=f"{cpu_usage - 50}%" if cpu_usage != 50 else None
        )
    
    with col2:
        # 内存使用率 (模拟数据)
        memory_usage = 62
        st.metric(
            label="🧠 内存使用率",
            value=f"{memory_usage}%",
            delta=f"{memory_usage - 60}%" if memory_usage != 60 else None
        )
    
    with col3:
        # 活跃连接数 (模拟数据)
        active_connections = 3
        st.metric(
            label="🔗 活跃连接",
            value=active_connections,
            delta=1 if active_connections > 2 else None
        )

def render_quick_actions():
    """渲染快速操作面板"""
    
    st.markdown("### ⚡ 快速操作")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔄 刷新系统", use_container_width=True):
            st.rerun()
    
    with col2:
        if st.button("📊 生成报告", use_container_width=True):
            st.info("报告生成功能开发中...")
    
    with col3:
        if st.button("🧹 清理缓存", use_container_width=True):
            st.success("缓存已清理")
    
    with col4:
        if st.button("💾 导出数据", use_container_width=True):
            st.info("数据导出功能开发中...")

def render_notification_center():
    """渲染通知中心"""
    
    st.markdown("### 🔔 通知中心")
    
    # 模拟通知数据
    notifications = [
        {
            "type": "success",
            "title": "系统更新",
            "message": "系统已成功更新到v0.2.0",
            "time": "2分钟前"
        },
        {
            "type": "warning", 
            "title": "性能警告",
            "message": "智能体响应时间略有增加",
            "time": "10分钟前"
        },
        {
            "type": "info",
            "title": "新功能",
            "message": "Web界面新增了实时监控功能",
            "time": "1小时前"
        }
    ]
    
    for notification in notifications:
        notification_type = notification["type"]
        
        if notification_type == "success":
            st.success(f"✅ **{notification['title']}** - {notification['message']} ({notification['time']})")
        elif notification_type == "warning":
            st.warning(f"⚠️ **{notification['title']}** - {notification['message']} ({notification['time']})")
        elif notification_type == "info":
            st.info(f"ℹ️ **{notification['title']}** - {notification['message']} ({notification['time']})")
        else:
            st.write(f"📢 **{notification['title']}** - {notification['message']} ({notification['time']})")