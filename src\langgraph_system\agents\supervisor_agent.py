#!/usr/bin/env python3
"""
Supervisor智能体 v0.3.0
基于v0.3.0架构设计的新一代智能体协调器
集成专业智能体生态、高级工作流引擎和智能协作机制
"""

import asyncio
import uuid
import logging
from typing import Dict, Any, List, Optional, Union, Type
from datetime import datetime
from enum import Enum

from langchain_core.language_models.base import BaseLanguageModel
# from langgraph_supervisor import create_supervisor

from ..states.project_state import ProjectState, TaskType
from ..llm.factory import LLMFactory
from ..llm.config import LLMConfig
from ..core.agent_pool import AgentPool
from ..core.performance_monitor import PerformanceMonitor
from ..workflow.workflow_engine import AdvancedWorkflowEngine
from ..workflow.workflow_monitor import WorkflowMonitor
from .base_v3 import CollaborationMode, AgentCapability, SpecialistAgent

logger = logging.getLogger(__name__)


class SupervisorAgent:
    """Supervisor智能体 v0.3.0版本"""
    
    def __init__(self, llm_config: Optional[LLMConfig] = None):
        """初始化Supervisor v0.3.0"""
        self.llm_config = llm_config or LLMConfig()
        self.model = self._create_llm_model()
        
        # 核心组件
        from ..core.agent_pool import AgentPoolManager
        self.agent_pool = AgentPoolManager()
        self.performance_monitor = PerformanceMonitor()
        self.workflow_engine = AdvancedWorkflowEngine(supervisor_agent=self)
        self.workflow_monitor = WorkflowMonitor()
        
        # 专业智能体
        self.specialist_agents: Dict[str, SpecialistAgent] = {}
        self.collaboration_patterns: Dict[str, CollaborationMode] = {}

        # Supervisor的能力（包含所有专业智能体的能力）
        self.capabilities = list(AgentCapability)
        self.name = "Supervisor"

        # 智能体能力评估
        self.capability_matrix: Dict[str, Dict[AgentCapability, float]] = {}
        
        # 初始化系统
        self._initialize_specialist_agents()
        self._create_supervisor_graph()
        self._setup_collaboration_patterns()
        
        logger.info("Supervisor智能体 v0.3.0 初始化完成")
    
    def _create_llm_model(self) -> BaseLanguageModel:
        """创建LLM模型实例"""
        try:
            client = LLMFactory.get_client(self.llm_config)
            return client.get_model()
        except Exception as e:
            logger.warning(f"无法创建LLM模型: {e}，使用默认配置")
            from langchain_openai import ChatOpenAI
            return ChatOpenAI(
                model="gpt-4o-mini",
                temperature=0.7,
                openai_api_key="sk-placeholder"
            )
    
    def _initialize_specialist_agents(self):
        """初始化专业智能体生态"""
        # 架构师智能体
        architect = SpecialistAgent(
            agent_id="architect",
            name="系统架构师",
            capabilities=[
                AgentCapability.ARCHITECTURE_DESIGN,
                AgentCapability.PERFORMANCE_OPTIMIZATION
            ],
            model=self.model,
            tools=self._get_architect_tools()
        )
        self.specialist_agents["architect"] = architect
        
        # 产品经理智能体
        product_manager = SpecialistAgent(
            agent_id="product_manager",
            name="产品经理",
            capabilities=[
                AgentCapability.REQUIREMENT_ANALYSIS
            ],
            model=self.model,
            tools=self._get_pm_tools()
        )
        self.specialist_agents["product_manager"] = product_manager
        
        # 开发工程师智能体
        coder = SpecialistAgent(
            agent_id="coder",
            name="开发工程师",
            capabilities=[
                AgentCapability.CODE_DEVELOPMENT
            ],
            model=self.model,
            tools=self._get_coder_tools()
        )
        self.specialist_agents["coder"] = coder
        
        # QA测试智能体
        qa_engineer = SpecialistAgent(
            agent_id="qa_engineer",
            name="QA测试工程师",
            capabilities=[
                AgentCapability.TESTING_QA
            ],
            model=self.model,
            tools=self._get_qa_tools()
        )
        self.specialist_agents["qa_engineer"] = qa_engineer
        
        # DevOps智能体
        devops = SpecialistAgent(
            agent_id="devops",
            name="DevOps工程师",
            capabilities=[
                AgentCapability.DEVOPS_DEPLOYMENT
            ],
            model=self.model,
            tools=self._get_devops_tools()
        )
        self.specialist_agents["devops"] = devops
        
        # 文档智能体
        documentation = SpecialistAgent(
            agent_id="documentation",
            name="文档工程师",
            capabilities=[
                AgentCapability.DOCUMENTATION
            ],
            model=self.model,
            tools=self._get_doc_tools()
        )
        self.specialist_agents["documentation"] = documentation
        
        # 安全专家智能体
        security = SpecialistAgent(
            agent_id="security",
            name="安全专家",
            capabilities=[
                AgentCapability.SECURITY_AUDIT
            ],
            model=self.model,
            tools=self._get_security_tools()
        )
        self.specialist_agents["security"] = security
        
        # 更新能力矩阵
        self._update_capability_matrix()
    
    def _get_architect_tools(self) -> List:
        """获取架构师工具"""
        return [
            self._architecture_analysis_tool,
            self._performance_optimization_tool,
            self._technology_selection_tool
        ]
    
    def _get_pm_tools(self) -> List:
        """获取产品经理工具"""
        return [
            self._requirement_analysis_tool,
            self._user_story_tool,
            self._project_planning_tool
        ]
    
    def _get_coder_tools(self) -> List:
        """获取开发工具"""
        return [
            self._code_generation_tool,
            self._code_review_tool,
            self._debugging_tool
        ]
    
    def _get_qa_tools(self) -> List:
        """获取QA工具"""
        return [
            self._test_design_tool,
            self._test_execution_tool,
            self._quality_assessment_tool
        ]
    
    def _get_devops_tools(self) -> List:
        """获取DevOps工具"""
        return [
            self._deployment_tool,
            self._monitoring_tool,
            self._infrastructure_tool
        ]
    
    def _get_doc_tools(self) -> List:
        """获取文档工具"""
        return [
            self._api_documentation_tool,
            self._user_manual_tool,
            self._technical_writing_tool
        ]
    
    def _get_security_tools(self) -> List:
        """获取安全工具"""
        return [
            self._security_scan_tool,
            self._vulnerability_assessment_tool,
            self._compliance_check_tool
        ]
    
    def _create_supervisor_graph(self):
        """创建Supervisor工作流图"""
        # 获取所有智能体的LangGraph实例
        agents = [agent.langgraph_agent for agent in self.specialist_agents.values()]
        
        # self.supervisor_graph = create_supervisor(
        #     agents,
        #     model=self.model,
        #     prompt="""你是一个高级项目协调主管，负责管理一个专业的多智能体团队。
#
# 你的团队包括：
# - 系统架构师：负责架构设计和性能优化
# - 产品经理：负责需求分析和项目规划
# - 开发工程师：负责代码开发和实现
# - QA测试工程师：负责测试和质量保证
# - DevOps工程师：负责部署和运维
# - 文档工程师：负责文档编写
# - 安全专家：负责安全审计和合规
#
# 你的职责包括：
# 1. 智能分析任务需求，选择最合适的智能体组合
# 2. 根据任务复杂度选择协作模式（顺序、并行、审查等）
# 3. 协调智能体间的工作流程和依赖关系
# 4. 监控项目进度和质量
# 5. 处理异常情况和冲突解决
#
# 请根据任务特点智能地分配工作，确保高效协作和高质量交付。""",
        #     output_mode="full_history"
        # ).compile(name="supervisor_v3")
    
    def _setup_collaboration_patterns(self):
        """设置协作模式"""
        self.collaboration_patterns = {
            "architecture_design": CollaborationMode.CONSULTATION,
            "requirement_analysis": CollaborationMode.BRAINSTORMING,
            "code_development": CollaborationMode.PAIR_PROGRAMMING,
            "testing": CollaborationMode.REVIEW,
            "deployment": CollaborationMode.SEQUENTIAL,
            "documentation": CollaborationMode.PARALLEL,
            "security_audit": CollaborationMode.REVIEW
        }
    
    def _update_capability_matrix(self):
        """更新能力矩阵"""
        for agent_id, agent in self.specialist_agents.items():
            self.capability_matrix[agent_id] = {}
            for capability in AgentCapability:
                self.capability_matrix[agent_id][capability] = agent.get_capability_score(capability)
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理任务 - v0.3.0增强版"""
        try:
            # 任务分析和智能体选择
            selected_agents = self._select_optimal_agents(task)
            collaboration_mode = self._determine_collaboration_mode(task, selected_agents)
            
            # 创建执行计划
            execution_plan = self._create_execution_plan(task, selected_agents, collaboration_mode)
            
            # 执行任务
            result = await self._execute_collaborative_task(execution_plan)
            
            # 更新性能指标
            self._update_system_metrics(result)
            
            return result
            
        except Exception as e:
            logger.error(f"任务处理失败: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "task_id": task.get("id", str(uuid.uuid4()))
            }
    
    def _select_optimal_agents(self, task: Dict[str, Any]) -> List[str]:
        """智能选择最优智能体组合"""
        task_type = task.get('type', 'general')
        requirements = task.get('requirements', {})
        
        # 基于任务类型和需求分析所需能力
        required_capabilities = self._analyze_required_capabilities(task_type, requirements)
        
        # 根据能力矩阵选择最佳智能体
        selected_agents = []
        for capability in required_capabilities:
            best_agent = self._find_best_agent_for_capability(capability)
            if best_agent and best_agent not in selected_agents:
                selected_agents.append(best_agent)
        
        return selected_agents
    
    def _analyze_required_capabilities(self, task_type: str, requirements: Dict[str, Any]) -> List[AgentCapability]:
        """分析任务所需能力"""
        capability_mapping = {
            "architecture": [AgentCapability.ARCHITECTURE_DESIGN],
            "development": [AgentCapability.CODE_DEVELOPMENT, AgentCapability.TESTING_QA],
            "testing": [AgentCapability.TESTING_QA],
            "deployment": [AgentCapability.DEVOPS_DEPLOYMENT],
            "documentation": [AgentCapability.DOCUMENTATION],
            "security": [AgentCapability.SECURITY_AUDIT],
            "research": [AgentCapability.REQUIREMENT_ANALYSIS],
            "planning": [AgentCapability.REQUIREMENT_ANALYSIS, AgentCapability.ARCHITECTURE_DESIGN]
        }
        
        return capability_mapping.get(task_type, [AgentCapability.CODE_DEVELOPMENT])
    
    def _find_best_agent_for_capability(self, capability: AgentCapability) -> Optional[str]:
        """为特定能力找到最佳智能体"""
        best_agent = None
        best_score = 0.0
        
        for agent_id, capabilities in self.capability_matrix.items():
            score = capabilities.get(capability, 0.0)
            if score > best_score:
                best_score = score
                best_agent = agent_id
        
        return best_agent if best_score > 0.5 else None
    
    def _determine_collaboration_mode(self, task: Dict[str, Any], agents: List[str]) -> CollaborationMode:
        """确定协作模式"""
        task_type = task.get('type', 'general')
        
        # 基于任务类型和智能体数量确定协作模式
        if len(agents) == 1:
            return CollaborationMode.SEQUENTIAL
        elif task_type in self.collaboration_patterns:
            return self.collaboration_patterns[task_type]
        elif len(agents) > 3:
            return CollaborationMode.PARALLEL
        else:
            return CollaborationMode.REVIEW
    
    def _create_execution_plan(self, task: Dict[str, Any], agents: List[str], 
                             mode: CollaborationMode) -> Dict[str, Any]:
        """创建执行计划"""
        return {
            "task_id": task.get("id", str(uuid.uuid4())),
            "task": task,
            "agents": agents,
            "collaboration_mode": mode,
            "execution_order": self._determine_execution_order(agents, mode),
            "estimated_duration": self._estimate_execution_time(task, agents),
            "created_at": datetime.now().isoformat()
        }
    
    def _determine_execution_order(self, agents: List[str], mode: CollaborationMode) -> List[Dict[str, Any]]:
        """确定执行顺序"""
        if mode == CollaborationMode.SEQUENTIAL:
            return [{"step": i+1, "agents": [agent], "parallel": False} 
                   for i, agent in enumerate(agents)]
        elif mode == CollaborationMode.PARALLEL:
            return [{"step": 1, "agents": agents, "parallel": True}]
        elif mode == CollaborationMode.REVIEW:
            # 主要执行者 + 审查者
            primary = agents[0]
            reviewers = agents[1:]
            return [
                {"step": 1, "agents": [primary], "parallel": False},
                {"step": 2, "agents": reviewers, "parallel": True, "type": "review"}
            ]
        else:
            # 默认顺序执行
            return [{"step": i+1, "agents": [agent], "parallel": False} 
                   for i, agent in enumerate(agents)]
    
    def _estimate_execution_time(self, task: Dict[str, Any], agents: List[str]) -> float:
        """估算执行时间"""
        base_time = 60.0  # 基础时间60秒
        complexity_factor = len(str(task.get('requirements', {}))) / 100.0
        agent_factor = len(agents) * 0.5
        
        return base_time * (1 + complexity_factor) * (1 + agent_factor)
    
    async def _execute_collaborative_task(self, execution_plan: Dict[str, Any]) -> Dict[str, Any]:
        """执行协作任务"""
        task = execution_plan["task"]
        execution_order = execution_plan["execution_order"]
        results = []
        context = {}
        
        for step_info in execution_order:
            step_agents = step_info["agents"]
            is_parallel = step_info.get("parallel", False)
            step_type = step_info.get("type", "execute")
            
            if is_parallel:
                # 并行执行
                step_results = await self._execute_parallel_step(
                    step_agents, task, context, step_type
                )
            else:
                # 顺序执行
                step_results = await self._execute_sequential_step(
                    step_agents, task, context, step_type
                )
            
            results.extend(step_results)
            
            # 更新上下文
            for result in step_results:
                if result["status"] == "completed":
                    context[result["agent_id"]] = result["result"]
        
        # 汇总结果
        return self._aggregate_results(execution_plan, results)
    
    async def _execute_parallel_step(self, agents: List[str], task: Dict[str, Any], 
                                   context: Dict[str, Any], step_type: str) -> List[Dict[str, Any]]:
        """并行执行步骤"""
        tasks = []
        for agent_id in agents:
            agent = self.specialist_agents[agent_id]
            task_copy = task.copy()
            task_copy["step_type"] = step_type
            tasks.append(agent.execute_task(task_copy, context))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "status": "failed",
                    "error": str(result),
                    "agent_id": agents[i],
                    "timestamp": datetime.now().isoformat()
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _execute_sequential_step(self, agents: List[str], task: Dict[str, Any], 
                                     context: Dict[str, Any], step_type: str) -> List[Dict[str, Any]]:
        """顺序执行步骤"""
        results = []
        for agent_id in agents:
            agent = self.specialist_agents[agent_id]
            task_copy = task.copy()
            task_copy["step_type"] = step_type
            result = await agent.execute_task(task_copy, context)
            results.append(result)
            
            # 如果失败，停止后续执行
            if result["status"] == "failed":
                break
        
        return results
    
    def _aggregate_results(self, execution_plan: Dict[str, Any], 
                          step_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """汇总执行结果"""
        successful_results = [r for r in step_results if r["status"] == "completed"]
        failed_results = [r for r in step_results if r["status"] == "failed"]
        
        # 合并成功结果
        final_result = ""
        agent_contributions = {}
        
        for result in successful_results:
            agent_id = result["agent_id"]
            agent_contributions[agent_id] = result["result"]
            final_result += f"\n\n=== {agent_id} 贡献 ===\n{result['result']}"
        
        return {
            "status": "completed" if not failed_results else "partial_success",
            "task_id": execution_plan["task_id"],
            "result": final_result.strip(),
            "agent_contributions": agent_contributions,
            "execution_summary": {
                "total_agents": len(execution_plan["agents"]),
                "successful_agents": len(successful_results),
                "failed_agents": len(failed_results),
                "collaboration_mode": execution_plan["collaboration_mode"].value
            },
            "errors": [r["error"] for r in failed_results] if failed_results else [],
            "timestamp": datetime.now().isoformat()
        }
    
    def _update_system_metrics(self, result: Dict[str, Any]):
        """更新系统指标"""
        # 更新性能监控器
        self.performance_monitor.record_task_completion(
            task_id=result["task_id"],
            success=result["status"] in ["completed", "partial_success"],
            execution_time=0.0,  # 实际应该计算真实执行时间
            agent_count=result["execution_summary"]["total_agents"]
        )
        
        # 更新能力矩阵
        self._update_capability_matrix()
    
    # 工具方法实现
    def _architecture_analysis_tool(self, requirements: str) -> str:
        """架构分析工具"""
        return f"架构分析结果：\n基于需求 '{requirements}' 的架构建议：\n1. 微服务架构\n2. 容器化部署\n3. API网关设计"
    
    def _performance_optimization_tool(self, metrics: str) -> str:
        """性能优化工具"""
        return f"性能优化建议：\n基于指标 '{metrics}'：\n1. 数据库查询优化\n2. 缓存策略\n3. 负载均衡"
    
    def _technology_selection_tool(self, context: str) -> str:
        """技术选型工具"""
        return f"技术选型建议：\n基于上下文 '{context}'：\n1. Python + FastAPI\n2. PostgreSQL\n3. Redis缓存"
    
    def _requirement_analysis_tool(self, description: str) -> str:
        """需求分析工具"""
        return f"需求分析结果：\n'{description}' 的分析：\n1. 功能需求\n2. 非功能需求\n3. 约束条件"
    
    def _user_story_tool(self, requirements: str) -> str:
        """用户故事工具"""
        return f"用户故事：\n基于 '{requirements}'：\n作为用户，我希望...以便..."
    
    def _project_planning_tool(self, scope: str) -> str:
        """项目规划工具"""
        return f"项目规划：\n范围 '{scope}' 的规划：\n1. 里程碑\n2. 时间线\n3. 资源分配"
    
    def _code_generation_tool(self, specification: str) -> str:
        """代码生成工具"""
        return f"代码生成：\n基于规范 '{specification}'：\n```python\n# 生成的代码\npass\n```"
    
    def _code_review_tool(self, code: str) -> str:
        """代码审查工具"""
        return f"代码审查结果：\n代码质量：良好\n建议：添加注释和测试"
    
    def _debugging_tool(self, error: str) -> str:
        """调试工具"""
        return f"调试结果：\n错误 '{error}' 的解决方案：\n1. 检查输入\n2. 验证逻辑"
    
    def _test_design_tool(self, requirements: str) -> str:
        """测试设计工具"""
        return f"测试设计：\n基于 '{requirements}' 的测试用例：\n1. 正常流程\n2. 异常处理"
    
    def _test_execution_tool(self, test_cases: str) -> str:
        """测试执行工具"""
        return f"测试执行结果：\n测试用例通过率：95%\n发现问题：2个"
    
    def _quality_assessment_tool(self, metrics: str) -> str:
        """质量评估工具"""
        return f"质量评估：\n基于指标 '{metrics}'：\n代码覆盖率：90%\n质量等级：A"
    
    def _deployment_tool(self, config: str) -> str:
        """部署工具"""
        return f"部署结果：\n配置 '{config}' 部署成功\n服务状态：运行中"
    
    def _monitoring_tool(self, services: str) -> str:
        """监控工具"""
        return f"监控报告：\n服务 '{services}' 状态：\n1. CPU使用率：60%\n2. 内存使用率：70%"
    
    def _infrastructure_tool(self, requirements: str) -> str:
        """基础设施工具"""
        return f"基础设施配置：\n基于需求 '{requirements}'：\n1. 服务器配置\n2. 网络设置\n3. 存储方案"
    
    def _api_documentation_tool(self, api_spec: str) -> str:
        """API文档工具"""
        return f"API文档：\n基于规范 '{api_spec}'：\n1. 接口说明\n2. 参数定义\n3. 示例代码"
    
    def _user_manual_tool(self, features: str) -> str:
        """用户手册工具"""
        return f"用户手册：\n功能 '{features}' 的使用说明：\n1. 操作步骤\n2. 注意事项\n3. 常见问题"
    
    def _technical_writing_tool(self, content: str) -> str:
        """技术写作工具"""
        return f"技术文档：\n内容 '{content}' 的文档：\n1. 技术概述\n2. 实现细节\n3. 最佳实践"
    
    def _security_scan_tool(self, target: str) -> str:
        """安全扫描工具"""
        return f"安全扫描结果：\n目标 '{target}' 的扫描：\n1. 漏洞检测\n2. 风险评估\n3. 修复建议"
    
    def _vulnerability_assessment_tool(self, system: str) -> str:
        """漏洞评估工具"""
        return f"漏洞评估：\n系统 '{system}' 的评估：\n1. 安全等级：中等\n2. 发现漏洞：3个\n3. 优先级：高"
    
    def _compliance_check_tool(self, standards: str) -> str:
        """合规检查工具"""
        return f"合规检查结果：\n标准 '{standards}' 的检查：\n1. 合规状态：通过\n2. 不合规项：0个\n3. 建议改进：2项"
    
    # 公共接口方法
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "supervisor_version": "v0.3.0",
            "specialist_agents": list(self.specialist_agents.keys()),
            "total_agents": len(self.specialist_agents),
            "collaboration_modes": [mode.value for mode in CollaborationMode],
            "capabilities": [cap.value for cap in AgentCapability],
            "model": str(self.model),
            "status": "ready",
            "performance_metrics": self._get_aggregated_performance_metrics()
        }
    
    def _get_aggregated_performance_metrics(self) -> Dict[str, Any]:
        """获取聚合性能指标"""
        total_tasks = 0
        total_successful = 0
        total_execution_time = 0.0
        
        for agent in self.specialist_agents.values():
            metrics = agent.performance_metrics
            total_tasks += metrics.get("total_tasks", 0)
            total_successful += metrics.get("successful_tasks", 0)
            total_execution_time += metrics.get("total_execution_time", 0.0)
        
        return {
            "total_tasks": total_tasks,
            "success_rate": total_successful / total_tasks if total_tasks > 0 else 0.0,
            "average_execution_time": total_execution_time / total_tasks if total_tasks > 0 else 0.0,
            "active_agents": len(self.specialist_agents)
        }
    
    def get_agent_capabilities(self) -> Dict[str, Any]:
        """获取智能体能力信息"""
        capabilities = {}
        for agent_id, agent in self.specialist_agents.items():
            capabilities[agent_id] = {
                "name": agent.name,
                "capabilities": [cap.value for cap in agent.capabilities],
                "performance_metrics": agent.performance_metrics,
                "capability_scores": {
                    cap.value: agent.get_capability_score(cap)
                    for cap in AgentCapability
                }
            }
        return capabilities

    def get_capability_score(self, capability: AgentCapability) -> float:
        """获取Supervisor的能力评分（基于专业智能体的平均能力）"""
        if not self.specialist_agents:
            return 0.0

        # 计算所有专业智能体在该能力上的平均分数
        total_score = 0.0
        count = 0

        for agent in self.specialist_agents.values():
            if capability in agent.capabilities:
                total_score += agent.get_capability_score(capability)
                count += 1

        return total_score / count if count > 0 else 0.0
    
    def create_task_plan(self, task_type: TaskType, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建任务执行计划"""
        # 模拟任务分析
        task_info = {
            "type": task_type.value,
            "requirements": requirements
        }
        
        # 选择智能体
        selected_agents = self._select_optimal_agents(task_info)
        collaboration_mode = self._determine_collaboration_mode(task_info, selected_agents)
        execution_order = self._determine_execution_order(selected_agents, collaboration_mode)
        
        # 生成计划
        plan = []
        for step_info in execution_order:
            step = step_info["step"]
            agents = step_info["agents"]
            parallel = step_info.get("parallel", False)
            
            plan.append({
                "step": step,
                "agents": agents,
                "parallel": parallel,
                "collaboration_mode": collaboration_mode.value,
                "estimated_duration": self._estimate_step_duration(agents, parallel),
                "description": self._generate_step_description(step, agents, task_type)
            })
        
        return plan
    
    def _estimate_step_duration(self, agents: List[str], parallel: bool) -> float:
        """估算步骤持续时间"""
        base_duration = 30.0  # 基础30秒
        if parallel:
            return base_duration * 1.2  # 并行稍微增加协调时间
        else:
            return base_duration * len(agents)  # 顺序执行累加时间
    
    def _generate_step_description(self, step: int, agents: List[str], task_type: TaskType) -> str:
        """生成步骤描述"""
        agent_names = [self.specialist_agents[agent_id].name for agent_id in agents if agent_id in self.specialist_agents]
        return f"步骤{step}: {', '.join(agent_names)} 协作处理 {task_type.value} 任务"
    
    # 兼容性接口
    async def invoke(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """兼容现有工作流的invoke方法"""
        # 提取任务信息
        task = inputs.get("task", {})
        if not task:
            # 从state中提取
            state = inputs.get("state", {})
            if isinstance(state, dict) and "current_task" in state:
                task = {
                    "type": state.get("current_task", "general"),
                    "description": state.get("description", ""),
                    "requirements": state.get("requirements", {})
                }
        
        # 处理任务
        result = await self.process_task(task)
        
        # 转换为兼容格式
        if result["status"] == "completed":
            return {
                "action": "completed",
                "agent": "supervisor_v3",
                "message": result["result"],
                "execution_summary": result.get("execution_summary", {})
            }
        else:
            return {
                "action": "failed",
                "agent": "supervisor_v3",
                "message": result.get("error", "Task failed"),
                "errors": result.get("errors", [])
            }
    
    def route_task(self, state: ProjectState) -> str:
        """路由任务到合适的智能体"""
        if not state.current_task:
            return "coder"  # 默认返回开发智能体
        
        task_info = {
            "type": state.current_task.value,
            "requirements": state.requirements or {}
        }
        
        selected_agents = self._select_optimal_agents(task_info)
        return selected_agents[0] if selected_agents else "coder"
    
    def shutdown(self):
        """关闭Supervisor v0.3.0"""
        logger.info("正在关闭Supervisor v0.3.0...")
        
        # 停止监控
        if hasattr(self.workflow_monitor, 'stop_monitoring'):
            self.workflow_monitor.stop_monitoring()
        
        # 清理资源
        self.specialist_agents.clear()
        self.capability_matrix.clear()
        
        logger.info("Supervisor v0.3.0 已关闭")
