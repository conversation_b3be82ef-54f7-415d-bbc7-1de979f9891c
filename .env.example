# LangGraph多智能体协作平台配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# LLM配置 (必需)
# =============================================================================

# LLM提供商选择: openai, anthropic, moonshot
LLM_PROVIDER=moonshot

# API密钥 (至少配置一个)
OPENAI_API_KEY=sk-your-openai-key-here
ANTHROPIC_API_KEY=sk-your-anthropic-key-here
MOONSHOT_API_KEY=sk-your-moonshot-key-here

# 模型配置
DEFAULT_MODEL=kimi-k2-0711-preview
TEMPERATURE=0.7

# =============================================================================
# Redis配置 (可选)
# =============================================================================

# Redis模式: local, docker, cloud, memory, disabled
# memory: 内存模拟，适合开发和测试
# local: 本地Redis服务
# docker: Docker容器Redis
# cloud: 云Redis服务
# disabled: 禁用Redis功能
REDIS_MODE=memory

# Redis连接配置 (仅在非memory模式下需要)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=

# Redis连接池配置
REDIS_MAX_CONNECTIONS=20
REDIS_TIMEOUT=30
REDIS_RETRY_ATTEMPTS=3

# =============================================================================
# 系统配置
# =============================================================================

# 日志级别: DEBUG, INFO, WARNING, ERROR
LANGGRAPH_LOG_LEVEL=INFO

# 工作空间目录
LANGGRAPH_WORKSPACE_DIR=./workspace

# 检查点目录
LANGGRAPH_CHECKPOINT_DIR=./checkpoints

# 最大重试次数
LANGGRAPH_MAX_RETRIES=3

# 超时设置 (秒)
LANGGRAPH_TIMEOUT=300

# =============================================================================
# 缓存配置
# =============================================================================

# 内存缓存大小
MEMORY_CACHE_SIZE=1000

# 缓存默认TTL (秒)
CACHE_DEFAULT_TTL=3600

# =============================================================================
# 性能配置
# =============================================================================

# 任务调度器最大工作线程数
SCHEDULER_MAX_WORKERS=10

# 任务队列最大大小
SCHEDULER_MAX_QUEUE_SIZE=1000

# 智能体池配置
AGENT_POOL_MIN_SIZE=1
AGENT_POOL_MAX_SIZE=10

# =============================================================================
# 开发配置
# =============================================================================

# 调试模式
DEBUG=false

# 详细输出
VERBOSE=false

# 性能监控
ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# Docker配置 (使用Docker Redis时)
# =============================================================================

# Docker Redis容器名
DOCKER_REDIS_CONTAINER=langgraph-redis

# Docker Redis端口映射
DOCKER_REDIS_PORT=6379

# =============================================================================
# 云服务配置 (使用云Redis时)
# =============================================================================

# 云Redis提供商: aws, azure, gcp, aliyun
CLOUD_REDIS_PROVIDER=aws

# 云Redis连接字符串
CLOUD_REDIS_URL=

# =============================================================================
# 安全配置
# =============================================================================

# API密钥加密
ENCRYPT_API_KEYS=false

# 访问令牌
ACCESS_TOKEN=

# =============================================================================
# 监控配置
# =============================================================================

# Prometheus指标端口
PROMETHEUS_PORT=8000

# 健康检查端口
HEALTH_CHECK_PORT=8001

# =============================================================================
# Web界面配置
# =============================================================================

# Streamlit端口
STREAMLIT_PORT=8501

# Web界面主题
WEB_THEME=light

# =============================================================================
# 实验性功能
# =============================================================================

# 启用实验性功能
ENABLE_EXPERIMENTAL_FEATURES=false

# 启用分布式模式
ENABLE_DISTRIBUTED_MODE=false

# 启用自动扩缩容
ENABLE_AUTO_SCALING=false
