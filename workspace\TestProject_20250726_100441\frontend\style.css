/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.description {
    color: #7f8c8d;
    font-size: 1.1em;
}

/* 表单样式 */
.add-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.add-item h2 {
    margin-bottom: 15px;
    color: #2c3e50;
}

#itemForm {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

input, textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

input:focus, textarea:focus {
    outline: none;
    border-color: #3498db;
}

button {
    padding: 12px 24px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

button:hover {
    background: #2980b9;
}

/* 项目列表样式 */
.items-list {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.items-list h2 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.item {
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 10px;
    background: #fafafa;
    transition: all 0.3s;
}

.item:hover {
    background: #f0f0f0;
}

.item.completed {
    opacity: 0.6;
    text-decoration: line-through;
}

.item-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.item-description {
    color: #666;
    margin-bottom: 10px;
}

.item-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    padding: 5px 10px;
    font-size: 14px;
}

.btn-success {
    background: #27ae60;
}

.btn-success:hover {
    background: #229954;
}

.btn-danger {
    background: #e74c3c;
}

.btn-danger:hover {
    background: #c0392b;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .container {
        padding: 10px;
    }
    
    .item-actions {
        flex-direction: column;
    }
}