version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./app.db
    volumes:
      - .:/app
    working_dir: /app
    command: python backend/main.py

  # 可选：添加数据库服务
  # db:
  #   image: postgres:15
  #   environment:
  #     POSTGRES_DB: testproject_20250726_100441
  #     POSTGRES_USER: user
  #     POSTGRES_PASSWORD: password
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data

# volumes:
#   postgres_data:
