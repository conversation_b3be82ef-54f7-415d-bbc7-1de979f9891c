#!/usr/bin/env python3
"""
Web界面功能测试脚本
"""

import requests
import time
import sys
import subprocess
import threading
from pathlib import Path

def check_streamlit_running(port=8501, timeout=30):
    """检查Streamlit是否正在运行"""
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"http://localhost:{port}", timeout=5)
            if response.status_code == 200:
                return True
        except requests.exceptions.RequestException:
            pass
        time.sleep(1)
    
    return False

def test_web_ui_endpoints():
    """测试Web界面的各个端点"""
    base_url = "http://localhost:8501"
    
    test_results = {
        "main_page": False,
        "health_check": False,
        "static_resources": False
    }
    
    try:
        # 测试主页
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            test_results["main_page"] = True
            print("✅ 主页访问正常")
        else:
            print(f"❌ 主页访问失败: {response.status_code}")
        
        # 测试健康检查
        health_url = f"{base_url}/healthz"
        try:
            response = requests.get(health_url, timeout=5)
            test_results["health_check"] = True
            print("✅ 健康检查端点正常")
        except:
            print("ℹ️  健康检查端点不可用（正常情况）")
            test_results["health_check"] = True  # Streamlit默认没有此端点
        
        # 测试静态资源
        static_url = f"{base_url}/static"
        try:
            response = requests.get(static_url, timeout=5)
            test_results["static_resources"] = True
            print("✅ 静态资源访问正常")
        except:
            print("ℹ️  静态资源端点测试跳过")
            test_results["static_resources"] = True
            
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
    
    return test_results

def test_system_integration():
    """测试系统集成"""
    print("\n🧪 测试系统集成...")
    
    try:
        # 测试核心系统导入
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        
        from langgraph_system.core import LangGraphSystem
        from langgraph_system.core.registry import tool_registry, agent_registry
        
        print("✅ 核心系统导入成功")
        
        # 测试系统初始化
        system = LangGraphSystem()
        print("✅ 系统实例创建成功")
        
        # 测试注册中心
        tools = tool_registry.list_tools()
        agents = agent_registry.list_agents()
        
        print(f"✅ 工具注册中心正常 ({len(tools)} 个工具)")
        print(f"✅ 智能体注册中心正常 ({len(agents)} 个智能体)")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        return False

def run_web_ui_in_background():
    """在后台运行Web界面"""
    try:
        app_path = Path(__file__).parent / "src" / "langgraph_system" / "web" / "streamlit_app.py"
        
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            str(app_path),
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false",
            "--server.headless", "true"
        ]
        
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            cwd=str(Path(__file__).parent)
        )
        
        return process
        
    except Exception as e:
        print(f"❌ 启动Web界面失败: {e}")
        return None

def main():
    """主测试函数"""
    print("🚀 开始Web界面功能测试...\n")
    
    # 1. 测试系统集成
    integration_ok = test_system_integration()
    
    if not integration_ok:
        print("\n❌ 系统集成测试失败，跳过Web界面测试")
        return False
    
    # 2. 检查是否已有Streamlit在运行
    print("\n🔍 检查Streamlit服务状态...")
    
    if check_streamlit_running(timeout=5):
        print("✅ 检测到Streamlit服务正在运行")
        run_web_tests = True
    else:
        print("ℹ️  未检测到运行中的Streamlit服务")
        
        # 询问是否启动测试服务
        try:
            user_input = input("是否启动临时测试服务？(y/n): ").lower().strip()
            if user_input in ['y', 'yes']:
                print("🚀 启动临时测试服务...")
                
                process = run_web_ui_in_background()
                if process:
                    print("⏳ 等待服务启动...")
                    
                    if check_streamlit_running(timeout=30):
                        print("✅ 测试服务启动成功")
                        run_web_tests = True
                        
                        # 运行测试后清理
                        def cleanup():
                            time.sleep(10)  # 给测试一些时间
                            process.terminate()
                            print("🧹 测试服务已停止")
                        
                        cleanup_thread = threading.Thread(target=cleanup)
                        cleanup_thread.daemon = True
                        cleanup_thread.start()
                    else:
                        print("❌ 测试服务启动失败")
                        if process:
                            process.terminate()
                        run_web_tests = False
                else:
                    run_web_tests = False
            else:
                print("⏭️  跳过Web界面测试")
                run_web_tests = False
                
        except KeyboardInterrupt:
            print("\n⏹️  测试被用户中断")
            return False
    
    # 3. 运行Web界面测试
    if run_web_tests:
        print("\n🌐 测试Web界面功能...")
        test_results = test_web_ui_endpoints()
        
        # 汇总结果
        print("\n📊 测试结果汇总:")
        passed = sum(test_results.values())
        total = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有Web界面测试通过！")
            return True
        else:
            print("⚠️  部分Web界面测试失败")
            return False
    else:
        print("\n⏭️  Web界面测试已跳过")
        return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        sys.exit(1)