"""
CLI配置管理模块
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from ..llm.config import LLMConfig

@dataclass
class CLIConfig:
    """CLI配置类"""
    
    # 基本设置
    verbose: bool = False
    log_level: str = "INFO"
    output_format: str = "table"  # table, json, yaml
    
    # 项目设置
    default_project_dir: str = "./projects"
    auto_save: bool = True
    backup_enabled: bool = True
    
    # 显示设置
    max_messages: int = 50
    show_timestamps: bool = True
    color_output: bool = True
    
    # 交互设置
    confirm_actions: bool = True
    auto_continue: bool = False
    timeout: int = 300  # 秒
    
    # LLM设置
    llm_provider: str = "openai"
    llm_model: str = "gpt-4o-mini"
    llm_temperature: float = 0.7
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CLIConfig":
        """从字典创建"""
        return cls(**data)

class ConfigManager:
    """配置管理器"""
    
    DEFAULT_CONFIG_PATHS = [
        "~/.langgraph/config.json",
        "~/.langgraph/config.yaml",
        "./langgraph.config.json",
        "./langgraph.config.yaml",
        "./.langgraph.json",
        "./.langgraph.yaml"
    ]
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path
        self.config = CLIConfig()
        self._load_config()
    
    def _find_config_file(self) -> Optional[str]:
        """查找配置文件"""
        if self.config_path:
            return self.config_path
        
        for path in self.DEFAULT_CONFIG_PATHS:
            expanded_path = Path(path).expanduser()
            if expanded_path.exists():
                return str(expanded_path)
        
        return None
    
    def _load_config(self):
        """加载配置"""
        config_file = self._find_config_file()
        if not config_file:
            return
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.endswith('.yaml') or config_file.endswith('.yml'):
                    data = yaml.safe_load(f)
                else:
                    data = json.load(f)
            
            if data:
                self.config = CLIConfig.from_dict(data)
                
        except Exception as e:
            print(f"警告: 加载配置文件失败 {config_file}: {e}")
    
    def save_config(self, path: Optional[str] = None) -> bool:
        """保存配置"""
        save_path = path or self.config_path or "~/.langgraph/config.json"
        save_path = Path(save_path).expanduser()
        
        try:
            # 确保目录存在
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                if str(save_path).endswith('.yaml') or str(save_path).endswith('.yml'):
                    yaml.dump(self.config.to_dict(), f, default_flow_style=False)
                else:
                    json.dump(self.config.to_dict(), f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def get_llm_config(self) -> LLMConfig:
        """获取LLM配置"""
        return LLMConfig(
            provider=self.config.llm_provider,
            model=self.config.llm_model,
            temperature=self.config.llm_temperature
        )
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
    
    def reset_config(self):
        """重置配置"""
        self.config = CLIConfig()
    
    def get_project_dir(self) -> Path:
        """获取项目目录"""
        return Path(self.config.default_project_dir).expanduser()
    
    def ensure_project_dir(self) -> bool:
        """确保项目目录存在"""
        try:
            self.get_project_dir().mkdir(parents=True, exist_ok=True)
            return True
        except Exception:
            return False

class EnvironmentManager:
    """环境管理器"""
    
    @staticmethod
    def get_env_config() -> Dict[str, Any]:
        """从环境变量获取配置"""
        config = {}
        
        # LLM配置
        if os.getenv("LANGGRAPH_LLM_PROVIDER"):
            config["llm_provider"] = os.getenv("LANGGRAPH_LLM_PROVIDER")
        
        if os.getenv("LANGGRAPH_LLM_MODEL"):
            config["llm_model"] = os.getenv("LANGGRAPH_LLM_MODEL")
        
        if os.getenv("LANGGRAPH_LLM_TEMPERATURE"):
            try:
                config["llm_temperature"] = float(os.getenv("LANGGRAPH_LLM_TEMPERATURE"))
            except ValueError:
                pass
        
        # 基本配置
        if os.getenv("LANGGRAPH_VERBOSE"):
            config["verbose"] = os.getenv("LANGGRAPH_VERBOSE").lower() in ("true", "1", "yes")
        
        if os.getenv("LANGGRAPH_LOG_LEVEL"):
            config["log_level"] = os.getenv("LANGGRAPH_LOG_LEVEL")
        
        if os.getenv("LANGGRAPH_PROJECT_DIR"):
            config["default_project_dir"] = os.getenv("LANGGRAPH_PROJECT_DIR")
        
        return config
    
    @staticmethod
    def check_dependencies() -> Dict[str, bool]:
        """检查依赖"""
        dependencies = {}
        
        try:
            import click
            dependencies["click"] = True
        except ImportError:
            dependencies["click"] = False
        
        try:
            import rich
            dependencies["rich"] = True
        except ImportError:
            dependencies["rich"] = False
        
        try:
            import yaml
            dependencies["yaml"] = True
        except ImportError:
            dependencies["yaml"] = False
        
        return dependencies
    
    @staticmethod
    def get_system_info() -> Dict[str, Any]:
        """获取系统信息"""
        import platform
        import sys
        
        return {
            "platform": platform.platform(),
            "python_version": sys.version,
            "python_executable": sys.executable,
            "working_directory": os.getcwd(),
            "home_directory": str(Path.home()),
            "dependencies": EnvironmentManager.check_dependencies()
        }

class ProfileManager:
    """配置文件管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.profiles_dir = Path("~/.langgraph/profiles").expanduser()
        self.profiles_dir.mkdir(parents=True, exist_ok=True)
    
    def list_profiles(self) -> List[str]:
        """列出所有配置文件"""
        profiles = []
        for file in self.profiles_dir.glob("*.json"):
            profiles.append(file.stem)
        return profiles
    
    def save_profile(self, name: str, config: Optional[CLIConfig] = None) -> bool:
        """保存配置文件"""
        config = config or self.config_manager.config
        profile_path = self.profiles_dir / f"{name}.json"
        
        try:
            with open(profile_path, 'w', encoding='utf-8') as f:
                json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)
            return True
        except Exception:
            return False
    
    def load_profile(self, name: str) -> Optional[CLIConfig]:
        """加载配置文件"""
        profile_path = self.profiles_dir / f"{name}.json"
        
        if not profile_path.exists():
            return None
        
        try:
            with open(profile_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return CLIConfig.from_dict(data)
        except Exception:
            return None
    
    def delete_profile(self, name: str) -> bool:
        """删除配置文件"""
        profile_path = self.profiles_dir / f"{name}.json"
        
        try:
            if profile_path.exists():
                profile_path.unlink()
            return True
        except Exception:
            return False
    
    def switch_profile(self, name: str) -> bool:
        """切换配置文件"""
        profile = self.load_profile(name)
        if profile:
            self.config_manager.config = profile
            return True
        return False

# 全局配置实例
_config_manager = None

def get_config_manager(config_path: Optional[str] = None) -> ConfigManager:
    """获取配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_path)
        
        # 应用环境变量配置
        env_config = EnvironmentManager.get_env_config()
        if env_config:
            _config_manager.update_config(**env_config)
    
    return _config_manager

def reset_config_manager():
    """重置配置管理器"""
    global _config_manager
    _config_manager = None