"""
性能监控系统 - v0.3增强版
支持实时指标收集、性能分析和告警
"""

import asyncio
import logging
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
from enum import Enum
from dataclasses import dataclass, field
from collections import deque, defaultdict
import statistics
import json

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class MetricType(str, Enum):
    """指标类型"""
    COUNTER = "counter"  # 计数器
    GAUGE = "gauge"  # 仪表
    HISTOGRAM = "histogram"  # 直方图
    TIMER = "timer"  # 计时器


class AlertLevel(str, Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class MetricValue:
    """指标值"""
    value: Union[int, float]
    timestamp: float = field(default_factory=time.time)
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class Alert:
    """告警"""
    alert_id: str
    metric_name: str
    level: AlertLevel
    message: str
    value: Union[int, float]
    threshold: Union[int, float]
    timestamp: float = field(default_factory=time.time)
    resolved: bool = False
    resolved_at: Optional[float] = None


class Metric:
    """指标基类"""
    
    def __init__(self, name: str, metric_type: MetricType, 
                 description: str = "", labels: Dict[str, str] = None):
        self.name = name
        self.type = metric_type
        self.description = description
        self.labels = labels or {}
        self.values: deque = deque(maxlen=1000)  # 保留最近1000个值
        self._lock = threading.Lock()
    
    def add_value(self, value: Union[int, float], labels: Dict[str, str] = None):
        """添加指标值"""
        with self._lock:
            metric_value = MetricValue(
                value=value,
                labels={**self.labels, **(labels or {})}
            )
            self.values.append(metric_value)
    
    def get_latest_value(self) -> Optional[MetricValue]:
        """获取最新值"""
        with self._lock:
            return self.values[-1] if self.values else None
    
    def get_values(self, since: Optional[float] = None) -> List[MetricValue]:
        """获取指标值"""
        with self._lock:
            if since is None:
                return list(self.values)
            return [v for v in self.values if v.timestamp >= since]
    
    def get_statistics(self, since: Optional[float] = None) -> Dict[str, float]:
        """获取统计信息"""
        values = self.get_values(since)
        if not values:
            return {}
        
        numeric_values = [v.value for v in values]
        
        return {
            "count": len(numeric_values),
            "min": min(numeric_values),
            "max": max(numeric_values),
            "mean": statistics.mean(numeric_values),
            "median": statistics.median(numeric_values),
            "std_dev": statistics.stdev(numeric_values) if len(numeric_values) > 1 else 0.0
        }


class Counter(Metric):
    """计数器指标"""
    
    def __init__(self, name: str, description: str = "", labels: Dict[str, str] = None):
        super().__init__(name, MetricType.COUNTER, description, labels)
        self._count = 0
    
    def increment(self, amount: Union[int, float] = 1, labels: Dict[str, str] = None):
        """增加计数"""
        self._count += amount
        self.add_value(self._count, labels)
    
    def get_count(self) -> Union[int, float]:
        """获取当前计数"""
        return self._count


class Gauge(Metric):
    """仪表指标"""
    
    def __init__(self, name: str, description: str = "", labels: Dict[str, str] = None):
        super().__init__(name, MetricType.GAUGE, description, labels)
    
    def set(self, value: Union[int, float], labels: Dict[str, str] = None):
        """设置值"""
        self.add_value(value, labels)
    
    def increment(self, amount: Union[int, float] = 1, labels: Dict[str, str] = None):
        """增加值"""
        latest = self.get_latest_value()
        current_value = latest.value if latest else 0
        self.add_value(current_value + amount, labels)
    
    def decrement(self, amount: Union[int, float] = 1, labels: Dict[str, str] = None):
        """减少值"""
        self.increment(-amount, labels)


class Histogram(Metric):
    """直方图指标"""
    
    def __init__(self, name: str, buckets: List[float] = None, 
                 description: str = "", labels: Dict[str, str] = None):
        super().__init__(name, MetricType.HISTOGRAM, description, labels)
        self.buckets = buckets or [0.1, 0.5, 1.0, 2.5, 5.0, 10.0]
        self.bucket_counts = {bucket: 0 for bucket in self.buckets}
        self.bucket_counts[float('inf')] = 0  # +Inf bucket
    
    def observe(self, value: Union[int, float], labels: Dict[str, str] = None):
        """观察值"""
        self.add_value(value, labels)
        
        # 更新桶计数
        for bucket in self.buckets:
            if value <= bucket:
                self.bucket_counts[bucket] += 1
        self.bucket_counts[float('inf')] += 1
    
    def get_bucket_counts(self) -> Dict[float, int]:
        """获取桶计数"""
        return self.bucket_counts.copy()


class Timer(Metric):
    """计时器指标"""
    
    def __init__(self, name: str, description: str = "", labels: Dict[str, str] = None):
        super().__init__(name, MetricType.TIMER, description, labels)
    
    def time(self, labels: Dict[str, str] = None):
        """计时上下文管理器"""
        return TimerContext(self, labels)
    
    def record(self, duration: float, labels: Dict[str, str] = None):
        """记录持续时间"""
        self.add_value(duration, labels)


class TimerContext:
    """计时器上下文"""
    
    def __init__(self, timer: Timer, labels: Dict[str, str] = None):
        self.timer = timer
        self.labels = labels
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.timer.record(duration, self.labels)


class AlertRule:
    """告警规则"""
    
    def __init__(self, name: str, metric_name: str, 
                 condition: str, threshold: Union[int, float],
                 level: AlertLevel = AlertLevel.WARNING,
                 duration: float = 60.0,
                 message_template: str = None):
        self.name = name
        self.metric_name = metric_name
        self.condition = condition  # "gt", "lt", "eq", "gte", "lte"
        self.threshold = threshold
        self.level = level
        self.duration = duration  # 持续时间（秒）
        self.message_template = message_template or f"Metric {metric_name} {condition} {threshold}"
        
        self.triggered_at: Optional[float] = None
        self.last_alert: Optional[Alert] = None
    
    def evaluate(self, metric: Metric) -> Optional[Alert]:
        """评估告警规则"""
        latest_value = metric.get_latest_value()
        if not latest_value:
            return None
        
        # 检查条件
        triggered = False
        value = latest_value.value
        
        if self.condition == "gt" and value > self.threshold:
            triggered = True
        elif self.condition == "lt" and value < self.threshold:
            triggered = True
        elif self.condition == "eq" and value == self.threshold:
            triggered = True
        elif self.condition == "gte" and value >= self.threshold:
            triggered = True
        elif self.condition == "lte" and value <= self.threshold:
            triggered = True
        
        current_time = time.time()
        
        if triggered:
            if self.triggered_at is None:
                self.triggered_at = current_time
            elif current_time - self.triggered_at >= self.duration:
                # 生成告警
                alert = Alert(
                    alert_id=f"{self.name}_{int(current_time)}",
                    metric_name=self.metric_name,
                    level=self.level,
                    message=self.message_template.format(
                        metric=self.metric_name,
                        value=value,
                        threshold=self.threshold
                    ),
                    value=value,
                    threshold=self.threshold
                )
                self.last_alert = alert
                return alert
        else:
            # 重置触发时间
            self.triggered_at = None
            
            # 如果之前有告警且未解决，标记为已解决
            if self.last_alert and not self.last_alert.resolved:
                self.last_alert.resolved = True
                self.last_alert.resolved_at = current_time
        
        return None


class SystemMetricsCollector:
    """系统指标收集器"""
    
    def __init__(self, collection_interval: float = 5.0):
        self.collection_interval = collection_interval
        self.metrics: Dict[str, Metric] = {}
        self._running = False
        self._collection_task: Optional[asyncio.Task] = None
        
        # 创建系统指标
        self._create_system_metrics()
    
    def _create_system_metrics(self):
        """创建系统指标"""
        self.metrics["cpu_usage"] = Gauge("cpu_usage", "CPU使用率 (%)")
        self.metrics["memory_usage"] = Gauge("memory_usage", "内存使用率 (%)")
        self.metrics["disk_usage"] = Gauge("disk_usage", "磁盘使用率 (%)")
        self.metrics["network_bytes_sent"] = Counter("network_bytes_sent", "网络发送字节数")
        self.metrics["network_bytes_recv"] = Counter("network_bytes_recv", "网络接收字节数")
        self.metrics["process_count"] = Gauge("process_count", "进程数量")
        self.metrics["load_average"] = Gauge("load_average", "系统负载")
    
    async def start_collection(self):
        """开始收集"""
        if self._running:
            return
        
        self._running = True
        self._collection_task = asyncio.create_task(self._collection_loop())
        logger.info("System metrics collection started")
    
    async def stop_collection(self):
        """停止收集"""
        if not self._running:
            return
        
        self._running = False
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        
        logger.info("System metrics collection stopped")
    
    async def _collection_loop(self):
        """收集循环"""
        while self._running:
            try:
                await asyncio.sleep(self.collection_interval)
                await self._collect_system_metrics()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"System metrics collection error: {e}")
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            self.metrics["cpu_usage"].set(cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.metrics["memory_usage"].set(memory.percent)
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.metrics["disk_usage"].set(disk_percent)
            
            # 网络IO
            network = psutil.net_io_counters()
            self.metrics["network_bytes_sent"].increment(network.bytes_sent)
            self.metrics["network_bytes_recv"].increment(network.bytes_recv)
            
            # 进程数量
            process_count = len(psutil.pids())
            self.metrics["process_count"].set(process_count)
            
            # 系统负载
            if hasattr(psutil, 'getloadavg'):
                load_avg = psutil.getloadavg()[0]  # 1分钟负载
                self.metrics["load_average"].set(load_avg)
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics: Dict[str, Metric] = {}
        self.alert_rules: Dict[str, AlertRule] = {}
        self.alerts: List[Alert] = []
        self.alert_handlers: List[Callable[[Alert], None]] = []
        
        # 系统指标收集器
        self.system_collector = SystemMetricsCollector()
        
        # 监控任务
        self._monitoring_task: Optional[asyncio.Task] = None
        self._running = False
        
        # 创建默认指标
        self._create_default_metrics()
        self._create_default_alert_rules()
    
    def _create_default_metrics(self):
        """创建默认指标"""
        # 应用指标
        self.metrics["request_count"] = Counter("request_count", "请求总数")
        self.metrics["request_duration"] = Histogram("request_duration", 
                                                   [0.1, 0.5, 1.0, 2.0, 5.0], 
                                                   "请求持续时间")
        self.metrics["error_count"] = Counter("error_count", "错误总数")
        self.metrics["active_connections"] = Gauge("active_connections", "活跃连接数")
        
        # 智能体指标
        self.metrics["agent_task_count"] = Counter("agent_task_count", "智能体任务总数")
        self.metrics["agent_task_duration"] = Timer("agent_task_duration", "智能体任务持续时间")
        self.metrics["agent_pool_size"] = Gauge("agent_pool_size", "智能体池大小")
        self.metrics["agent_utilization"] = Gauge("agent_utilization", "智能体利用率")
        
        # 缓存指标
        self.metrics["cache_hits"] = Counter("cache_hits", "缓存命中数")
        self.metrics["cache_misses"] = Counter("cache_misses", "缓存未命中数")
        self.metrics["cache_size"] = Gauge("cache_size", "缓存大小")
        
        # 任务调度指标
        self.metrics["scheduled_tasks"] = Gauge("scheduled_tasks", "调度任务数")
        self.metrics["completed_tasks"] = Counter("completed_tasks", "完成任务数")
        self.metrics["failed_tasks"] = Counter("failed_tasks", "失败任务数")
    
    def _create_default_alert_rules(self):
        """创建默认告警规则"""
        # CPU使用率告警
        self.add_alert_rule(
            "high_cpu_usage",
            "cpu_usage",
            "gt",
            80.0,
            AlertLevel.WARNING,
            duration=60.0,
            message_template="CPU使用率过高: {value:.1f}% > {threshold}%"
        )
        
        # 内存使用率告警
        self.add_alert_rule(
            "high_memory_usage",
            "memory_usage",
            "gt",
            85.0,
            AlertLevel.WARNING,
            duration=60.0,
            message_template="内存使用率过高: {value:.1f}% > {threshold}%"
        )
        
        # 错误率告警
        self.add_alert_rule(
            "high_error_rate",
            "error_count",
            "gt",
            100,
            AlertLevel.ERROR,
            duration=30.0,
            message_template="错误数量过多: {value} > {threshold}"
        )
    
    async def start_monitoring(self):
        """开始监控"""
        if self._running:
            return
        
        self._running = True
        
        # 启动系统指标收集
        await self.system_collector.start_collection()
        
        # 将系统指标添加到监控器
        self.metrics.update(self.system_collector.metrics)
        
        # 启动监控循环
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        logger.info("Performance monitoring started")
    
    async def stop_monitoring(self):
        """停止监控"""
        if not self._running:
            return
        
        self._running = False
        
        # 停止系统指标收集
        await self.system_collector.stop_collection()
        
        # 停止监控循环
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Performance monitoring stopped")
    
    def register_metric(self, metric: Metric):
        """注册指标"""
        self.metrics[metric.name] = metric
    
    def get_metric(self, name: str) -> Optional[Metric]:
        """获取指标"""
        return self.metrics.get(name)
    
    def increment_counter(self, name: str, amount: Union[int, float] = 1, 
                         labels: Dict[str, str] = None):
        """增加计数器"""
        metric = self.metrics.get(name)
        if isinstance(metric, Counter):
            metric.increment(amount, labels)
    
    def set_gauge(self, name: str, value: Union[int, float], 
                 labels: Dict[str, str] = None):
        """设置仪表值"""
        metric = self.metrics.get(name)
        if isinstance(metric, Gauge):
            metric.set(value, labels)
    
    def observe_histogram(self, name: str, value: Union[int, float], 
                         labels: Dict[str, str] = None):
        """观察直方图值"""
        metric = self.metrics.get(name)
        if isinstance(metric, Histogram):
            metric.observe(value, labels)
    
    def time_operation(self, name: str, labels: Dict[str, str] = None):
        """计时操作"""
        metric = self.metrics.get(name)
        if isinstance(metric, Timer):
            return metric.time(labels)
        return TimerContext(Timer(name), labels)
    
    def add_alert_rule(self, name: str, metric_name: str, condition: str,
                      threshold: Union[int, float], level: AlertLevel = AlertLevel.WARNING,
                      duration: float = 60.0, message_template: str = None):
        """添加告警规则"""
        rule = AlertRule(
            name=name,
            metric_name=metric_name,
            condition=condition,
            threshold=threshold,
            level=level,
            duration=duration,
            message_template=message_template
        )
        self.alert_rules[name] = rule
    
    def add_alert_handler(self, handler: Callable[[Alert], None]):
        """添加告警处理器"""
        self.alert_handlers.append(handler)
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self._running:
            try:
                await asyncio.sleep(10)  # 10秒检查间隔
                await self._check_alerts()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
    
    async def _check_alerts(self):
        """检查告警"""
        for rule in self.alert_rules.values():
            metric = self.metrics.get(rule.metric_name)
            if metric:
                alert = rule.evaluate(metric)
                if alert:
                    self.alerts.append(alert)
                    await self._handle_alert(alert)
    
    async def _handle_alert(self, alert: Alert):
        """处理告警"""
        logger.warning(f"Alert triggered: {alert.message}")
        
        # 调用告警处理器
        for handler in self.alert_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(alert)
                else:
                    handler(alert)
            except Exception as e:
                logger.error(f"Alert handler error: {e}")
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        summary = {}
        
        for name, metric in self.metrics.items():
            latest_value = metric.get_latest_value()
            stats = metric.get_statistics(since=time.time() - 3600)  # 最近1小时
            
            summary[name] = {
                "type": metric.type.value,
                "description": metric.description,
                "latest_value": latest_value.value if latest_value else None,
                "latest_timestamp": latest_value.timestamp if latest_value else None,
                "statistics": stats
            }
        
        return summary
    
    def get_alerts_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        active_alerts = [alert for alert in self.alerts if not alert.resolved]
        resolved_alerts = [alert for alert in self.alerts if alert.resolved]
        
        return {
            "total_alerts": len(self.alerts),
            "active_alerts": len(active_alerts),
            "resolved_alerts": len(resolved_alerts),
            "alerts_by_level": {
                level.value: len([a for a in active_alerts if a.level == level])
                for level in AlertLevel
            },
            "recent_alerts": [
                {
                    "alert_id": alert.alert_id,
                    "metric_name": alert.metric_name,
                    "level": alert.level.value,
                    "message": alert.message,
                    "timestamp": alert.timestamp,
                    "resolved": alert.resolved
                }
                for alert in sorted(self.alerts, key=lambda a: a.timestamp, reverse=True)[:10]
            ]
        }
    
    def export_metrics_prometheus(self) -> str:
        """导出Prometheus格式指标"""
        lines = []
        
        for name, metric in self.metrics.items():
            # 添加帮助信息
            if metric.description:
                lines.append(f"# HELP {name} {metric.description}")
            
            # 添加类型信息
            lines.append(f"# TYPE {name} {metric.type.value}")
            
            # 添加指标值
            latest_value = metric.get_latest_value()
            if latest_value:
                labels_str = ""
                if latest_value.labels:
                    label_pairs = [f'{k}="{v}"' for k, v in latest_value.labels.items()]
                    labels_str = "{" + ",".join(label_pairs) + "}"
                
                lines.append(f"{name}{labels_str} {latest_value.value} {int(latest_value.timestamp * 1000)}")
        
        return "\n".join(lines)


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


async def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器实例"""
    if not performance_monitor._running:
        await performance_monitor.start_monitoring()
    return performance_monitor


# 便捷函数
def increment_counter(name: str, amount: Union[int, float] = 1, labels: Dict[str, str] = None):
    """增加计数器"""
    performance_monitor.increment_counter(name, amount, labels)


def set_gauge(name: str, value: Union[int, float], labels: Dict[str, str] = None):
    """设置仪表值"""
    performance_monitor.set_gauge(name, value, labels)


def observe_histogram(name: str, value: Union[int, float], labels: Dict[str, str] = None):
    """观察直方图值"""
    performance_monitor.observe_histogram(name, value, labels)


def time_operation(name: str, labels: Dict[str, str] = None):
    """计时操作"""
    return performance_monitor.time_operation(name, labels)