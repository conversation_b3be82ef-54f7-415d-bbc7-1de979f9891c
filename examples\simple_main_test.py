#!/usr/bin/env python3
"""
简单的main.py功能测试
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from main import SystemManager, create_system_manager, LLMConfig


async def test_system_manager():
    """测试系统管理器基本功能"""
    print("🔧 测试系统管理器")
    print("-" * 20)
    
    # 创建管理器
    manager = create_system_manager()
    print(f"✅ 创建管理器: {type(manager).__name__}")
    
    # 检查初始状态
    print(f"🔍 初始状态: {manager.is_initialized()}")
    
    # 初始化系统
    print("🚀 初始化系统...")
    try:
        llm_config = LLMConfig(provider="moonshot")
        success = await manager.initialize(llm_config)
        print(f"✅ 初始化: {'成功' if success else '失败'}")
        
        if success:
            # 健康检查
            health = await manager.health_check()
            print(f"🏥 健康状态: {health['status']}")
            
            # 关闭系统
            await manager.shutdown()
            print("🔄 系统已关闭")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_cli_help():
    """测试CLI帮助功能"""
    print("\n⌨️  测试CLI功能")
    print("-" * 20)
    
    print("📖 可用的CLI命令:")
    print("  python src/main.py --help")
    print("  python src/main.py status")
    print("  python src/main.py agents list")
    print("  python src/main.py interactive")


async def main():
    """主测试函数"""
    print("🤖 main.py 重构功能测试")
    print("=" * 40)
    
    await test_system_manager()
    test_cli_help()
    
    print("\n=" * 40)
    print("✅ 测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
