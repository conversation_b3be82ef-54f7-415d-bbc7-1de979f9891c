#!/usr/bin/env python3
"""
使用kimi-k2-0711-preview模型的示例
"""

import asyncio
import os
from langgraph_system.llm import LLMConfig, LLMFactory

async def main():
    """主函数"""
    print("=== 使用kimi-k2-0711-preview模型示例 ===")
    
    # 配置kimi-k2-0711-preview
    config = LLMConfig.for_kimi_k2_0711_preview(
        temperature=0.7,
        max_tokens=2000
    )
    
    # 确保设置了API密钥
    if not os.getenv("MOONSHOT_API_KEY"):
        print("❌ 请先设置 MOONSHOT_API_KEY 环境变量")
        print("   示例: export MOONSHOT_API_KEY='your-api-key-here'")
        return
    
    # 创建客户端
    client = LLMFactory.get_client(config)
    
    # 测试对话
    messages = [
        {"role": "user", "content": "你好！请介绍一下kimi-k2-0711-preview模型的特点。"}
    ]
    
    print(f"🤖 使用模型: {config.model}")
    print(f"🌡️  温度参数: {config.temperature}")
    print(f"📊 最大token: {config.max_tokens}")
    
    try:
        # 生成回复
        formatted_messages = client.format_messages(messages)
        response = await client.generate(formatted_messages)
        
        print("\n💬 对话结果:")
        print(f"用户: {messages[0]['content']}")
        print(f"助手: {response.content}")
        
        # 显示使用统计
        stats = client.get_usage_stats()
        print(f"\n📈 使用统计: {stats}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    asyncio.run(main())
