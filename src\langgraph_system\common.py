#!/usr/bin/env python3
"""
共享数据结构和枚举
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional


class TaskType(str, Enum):
    """任务类型枚举"""
    ARCHITECTURE = "architecture"
    DEVELOPMENT = "development"
    REVIEW = "review"
    TESTING = "testing"
    DEBUGGING = "debugging"
    DOCUMENTATION = "documentation"


class AgentStatus(str, Enum):
    """智能体状态枚举"""
    IDLE = "idle"
    WORKING = "working"
    COMPLETED = "completed"
    FAILED = "failed"


class SimpleProjectState:
    """简化项目状态"""

    def __init__(self, project_name: str = "Untitled Project"):
        self.project_id = str(uuid.uuid4())
        self.project_name = project_name
        self.current_task: Optional[TaskType] = None
        self.current_agent: Optional[str] = None
        self.messages: List[Dict[str, Any]] = []
        self.agent_status: Dict[str, AgentStatus] = {}
        self.artifacts: Dict[str, Any] = {}
        self.files: Dict[str, str] = {}
        self.start_time = datetime.now()
        self.execution_status = "initialized"
        self.context: Dict[str, Any] = {}

    def add_message(self, sender: str, recipient: str, content: Any, msg_type: str = "task"):
        """添加消息"""
        self.messages.append({
            "sender": sender,
            "recipient": recipient,
            "content": content,
            "type": msg_type,
            "timestamp": datetime.now().isoformat()
        })

    def update_agent_status(self, agent_name: str, status: AgentStatus):
        """更新智能体状态"""
        self.agent_status[agent_name] = status

    def add_artifact(self, name: str, content: Any):
        """添加工件"""
        self.artifacts[name] = {
            "content": content,
            "created_at": datetime.now().isoformat()
        }

    def add_file(self, filename: str, content: str):
        """添加文件"""
        self.files[filename] = content

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "project_id": self.project_id,
            "project_name": self.project_name,
            "current_task": self.current_task.value if self.current_task else None,
            "current_agent": self.current_agent,
            "messages": self.messages,
            "agent_status": {k: v.value for k, v in self.agent_status.items()},
            "artifacts": self.artifacts,
            "files": list(self.files.keys()),  # 只返回文件名列表
            "execution_status": self.execution_status,
            "start_time": self.start_time.isoformat(),
            "context": self.context
        }