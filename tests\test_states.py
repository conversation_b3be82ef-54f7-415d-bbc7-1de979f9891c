"""测试状态管理系统"""

import pytest
from datetime import datetime
from langgraph_system.states import ProjectState, TaskType, AgentStatus, AgentMessage

class TestProjectState:
    """测试ProjectState类"""
    
    def test_project_state_creation(self):
        """测试项目状态创建"""
        state = ProjectState(project_name="TestProject")
        
        assert state.project_name == "TestProject"
        assert state.project_id is not None
        assert state.current_task is None
        assert state.execution_status == "initialized"
        
    def test_add_message(self):
        """测试添加消息"""
        state = ProjectState()
        message = state.add_message("sender", "recipient", "test content")
        
        assert len(state.messages) == 1
        assert state.messages[0].sender == "sender"
        assert state.messages[0].recipient == "recipient"
        assert state.messages[0].content == "test content"
        assert isinstance(message, AgentMessage)
        
    def test_update_agent_status(self):
        """测试更新智能体状态"""
        state = ProjectState()
        state.update_agent_status("test_agent", AgentStatus.WORKING)
        
        assert state.agent_status["test_agent"] == AgentStatus.WORKING
        
    def test_add_artifact(self):
        """测试添加工件"""
        state = ProjectState()
        artifact_id = state.add_artifact("code", {"content": "print('hello')"})
        
        assert artifact_id in state.artifacts
        assert state.artifacts[artifact_id]["type"] == "code"
        assert state.artifacts[artifact_id]["content"]["content"] == "print('hello')"
        
    def test_get_agent_messages(self):
        """测试获取智能体消息"""
        state = ProjectState()
        state.add_message("agent1", "agent2", "message1")
        state.add_message("agent2", "agent1", "message2")
        state.add_message("agent3", "agent1", "message3")
        
        agent1_messages = state.get_agent_messages("agent1")
        assert len(agent1_messages) == 2
        
    def test_to_dict(self):
        """测试转换为字典"""
        state = ProjectState(project_name="Test")
        state_dict = state.to_dict()
        
        assert state_dict["project_name"] == "Test"
        assert "project_id" in state_dict
        assert "messages" in state_dict
        
    def test_from_dict(self):
        """测试从字典创建"""
        original = ProjectState(project_name="Test")
        original.add_message("sender", "recipient", "content")
        
        state_dict = original.to_dict()
        restored = ProjectState.from_dict(state_dict)
        
        assert restored.project_name == "Test"
        assert len(restored.messages) == 1

class TestAgentMessage:
    """测试AgentMessage类"""
    
    def test_message_creation(self):
        """测试消息创建"""
        message = AgentMessage(
            sender="test_sender",
            recipient="test_recipient",
            content="test content",
            message_type="task"
        )
        
        assert message.sender == "test_sender"
        assert message.recipient == "test_recipient"
        assert message.content == "test content"
        assert message.message_type == "task"
        assert isinstance(message.timestamp, datetime)
        
    def test_message_with_metadata(self):
        """测试带元数据的消息"""
        message = AgentMessage(
            sender="sender",
            recipient="recipient",
            content="content",
            metadata={"priority": "high", "tags": ["important"]}
        )
        
        assert message.metadata["priority"] == "high"
        assert "important" in message.metadata["tags"]

class TestTaskType:
    """测试任务类型枚举"""
    
    def test_task_type_values(self):
        """测试任务类型值"""
        assert TaskType.ARCHITECTURE.value == "architecture"
        assert TaskType.DEVELOPMENT.value == "development"
        assert TaskType.REVIEW.value == "review"
        assert TaskType.TESTING.value == "testing"
        
    def test_task_type_iteration(self):
        """测试任务类型迭代"""
        task_types = [task.value for task in TaskType]
        assert "architecture" in task_types
        assert "development" in task_types

class TestAgentStatus:
    """测试智能体状态枚举"""
    
    def test_agent_status_values(self):
        """测试智能体状态值"""
        assert AgentStatus.IDLE.value == "idle"
        assert AgentStatus.WORKING.value == "working"
        assert AgentStatus.COMPLETED.value == "completed"
        assert AgentStatus.FAILED.value == "failed"
