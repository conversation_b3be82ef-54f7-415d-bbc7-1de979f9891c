#!/usr/bin/env python3
"""
智能体协作机制 (Agent Collaboration)
负责智能体间的协作、通信和工作流编排
"""

import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging

# from .base_agent import (
#     IAgent, BaseAgent, AgentMessage, MessageType, Priority,
#     TaskRequest, TaskResult, AgentCapability
# )

# Placeholder classes to avoid import errors
class IAgent: pass
class BaseAgent: pass
class AgentMessage: pass
class MessageType: pass
class Priority: pass
class TaskRequest: pass
class TaskResult: pass
class AgentCapability: pass

logger = logging.getLogger(__name__)

# ============================================================================
# 协作数据模型
# ============================================================================

class CollaborationType(Enum):
    """协作类型枚举"""
    SEQUENTIAL = "sequential"  # 顺序协作
    PARALLEL = "parallel"     # 并行协作
    CONDITIONAL = "conditional"  # 条件协作
    ITERATIVE = "iterative"   # 迭代协作
    PIPELINE = "pipeline"     # 流水线协作

class WorkflowStatus(Enum):
    """工作流状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskDependencyType(Enum):
    """任务依赖类型枚举"""
    PREREQUISITE = "prerequisite"  # 前置条件
    DATA_DEPENDENCY = "data_dependency"  # 数据依赖
    RESOURCE_DEPENDENCY = "resource_dependency"  # 资源依赖
    TIMING_DEPENDENCY = "timing_dependency"  # 时间依赖

@dataclass
class TaskDependency:
    """任务依赖关系"""
    id: str
    source_task_id: str
    target_task_id: str
    dependency_type: TaskDependencyType
    condition: Optional[str] = None  # 依赖条件
    data_mapping: Optional[Dict[str, str]] = None  # 数据映射

@dataclass
class WorkflowTask:
    """工作流任务"""
    id: str
    agent_id: str
    task_request: TaskRequest
    dependencies: List[str]  # 依赖的任务ID列表
    status: WorkflowStatus
    result: Optional[TaskResult] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class Workflow:
    """工作流定义"""
    id: str
    name: str
    description: str
    collaboration_type: CollaborationType
    tasks: List[WorkflowTask]
    dependencies: List[TaskDependency]
    status: WorkflowStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None

@dataclass
class CollaborationSession:
    """协作会话"""
    id: str
    participants: List[str]  # 参与的智能体ID列表
    workflow: Workflow
    messages: List[AgentMessage]
    shared_context: Dict[str, Any]
    created_at: datetime
    last_activity: datetime

# ============================================================================
# 智能体协作管理器
# ============================================================================

class AgentCollaborationManager:
    """
    智能体协作管理器
    负责管理智能体间的协作、工作流编排和消息路由
    """
    
    def __init__(self):
        self.agents: Dict[str, IAgent] = {}
        self.workflows: Dict[str, Workflow] = {}
        self.sessions: Dict[str, CollaborationSession] = {}
        self.message_handlers: Dict[MessageType, List[Callable]] = {}
        self.workflow_templates: Dict[str, Dict[str, Any]] = {}
        
        # 初始化消息处理器
        self._initialize_message_handlers()
        
        # 初始化工作流模板
        self._initialize_workflow_templates()
    
    def _initialize_message_handlers(self):
        """初始化消息处理器"""
        self.message_handlers = {
            MessageType.REQUEST: [self._handle_request_message],
            MessageType.RESPONSE: [self._handle_response_message],
            MessageType.NOTIFICATION: [self._handle_notification_message],
            MessageType.ERROR: [self._handle_error_message]
        }
    
    def _initialize_workflow_templates(self):
        """初始化工作流模板"""
        # 软件开发流程模板
        self.workflow_templates["software_development"] = {
            "name": "软件开发流程",
            "description": "从需求分析到部署的完整软件开发流程",
            "collaboration_type": CollaborationType.SEQUENTIAL,
            "tasks": [
                {
                    "agent_type": "ProductManagerAgent",
                    "task_type": "analyze_requirements",
                    "description": "需求分析"
                },
                {
                    "agent_type": "ArchitectAgent", 
                    "task_type": "design_system",
                    "description": "系统设计",
                    "dependencies": ["analyze_requirements"]
                },
                {
                    "agent_type": "DevOpsAgent",
                    "task_type": "setup_cicd",
                    "description": "CI/CD设置",
                    "dependencies": ["design_system"]
                },
                {
                    "agent_type": "QAAgent",
                    "task_type": "design_test_strategy",
                    "description": "测试策略设计",
                    "dependencies": ["design_system"]
                },
                {
                    "agent_type": "DocumentationAgent",
                    "task_type": "generate_documentation",
                    "description": "文档生成",
                    "dependencies": ["design_system", "design_test_strategy"]
                }
            ]
        }
        
        # API开发流程模板
        self.workflow_templates["api_development"] = {
            "name": "API开发流程",
            "description": "API设计、开发、测试和文档化流程",
            "collaboration_type": CollaborationType.PIPELINE,
            "tasks": [
                {
                    "agent_type": "ProductManagerAgent",
                    "task_type": "create_user_stories",
                    "description": "创建API用户故事"
                },
                {
                    "agent_type": "ArchitectAgent",
                    "task_type": "select_technology",
                    "description": "技术选型",
                    "dependencies": ["create_user_stories"]
                },
                {
                    "agent_type": "QAAgent",
                    "task_type": "create_test_cases",
                    "description": "API测试用例设计",
                    "dependencies": ["select_technology"]
                },
                {
                    "agent_type": "DocumentationAgent",
                    "task_type": "generate_api_docs",
                    "description": "API文档生成",
                    "dependencies": ["create_test_cases"]
                }
            ]
        }
    
    async def register_agent(self, agent: IAgent) -> bool:
        """注册智能体"""
        try:
            if agent.agent_id in self.agents:
                logger.warning(f"智能体 {agent.agent_id} 已存在，将被覆盖")
            
            self.agents[agent.agent_id] = agent
            logger.info(f"智能体 {agent.agent_id} 注册成功")
            return True
            
        except Exception as e:
            logger.error(f"注册智能体失败: {e}")
            return False
    
    async def unregister_agent(self, agent_id: str) -> bool:
        """注销智能体"""
        try:
            if agent_id in self.agents:
                del self.agents[agent_id]
                logger.info(f"智能体 {agent_id} 注销成功")
                return True
            else:
                logger.warning(f"智能体 {agent_id} 不存在")
                return False
                
        except Exception as e:
            logger.error(f"注销智能体失败: {e}")
            return False
    
    async def create_workflow(self, template_name: str, parameters: Dict[str, Any]) -> str:
        """创建工作流"""
        try:
            if template_name not in self.workflow_templates:
                raise ValueError(f"工作流模板 {template_name} 不存在")
            
            template = self.workflow_templates[template_name]
            workflow_id = f"workflow_{uuid.uuid4().hex[:8]}"
            
            # 创建工作流任务
            tasks = []
            for i, task_template in enumerate(template["tasks"]):
                task_id = f"task_{i+1}_{uuid.uuid4().hex[:4]}"
                
                # 创建任务请求
                task_request = TaskRequest(
                    type=task_template["task_type"],
                    description=task_template["description"],
                    parameters=parameters.get(task_template["task_type"], {}),
                    priority=Priority.MEDIUM
                )
                
                # 查找对应的智能体
                agent_id = self._find_agent_by_type(task_template["agent_type"])
                if not agent_id:
                    raise ValueError(f"未找到类型为 {task_template['agent_type']} 的智能体")
                
                workflow_task = WorkflowTask(
                    id=task_id,
                    agent_id=agent_id,
                    task_request=task_request,
                    dependencies=task_template.get("dependencies", []),
                    status=WorkflowStatus.PENDING
                )
                
                tasks.append(workflow_task)
            
            # 创建工作流
            workflow = Workflow(
                id=workflow_id,
                name=template["name"],
                description=template["description"],
                collaboration_type=CollaborationType(template["collaboration_type"]),
                tasks=tasks,
                dependencies=[],
                status=WorkflowStatus.PENDING,
                created_at=datetime.now(),
                metadata=parameters
            )
            
            self.workflows[workflow_id] = workflow
            logger.info(f"工作流 {workflow_id} 创建成功")
            
            return workflow_id
            
        except Exception as e:
            logger.error(f"创建工作流失败: {e}")
            raise
    
    def _find_agent_by_type(self, agent_type: str) -> Optional[str]:
        """根据类型查找智能体"""
        for agent_id, agent in self.agents.items():
            if agent.__class__.__name__ == agent_type:
                return agent_id
        return None
    
    async def execute_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """执行工作流"""
        try:
            if workflow_id not in self.workflows:
                raise ValueError(f"工作流 {workflow_id} 不存在")
            
            workflow = self.workflows[workflow_id]
            workflow.status = WorkflowStatus.RUNNING
            workflow.started_at = datetime.now()
            
            logger.info(f"开始执行工作流 {workflow_id}")
            
            if workflow.collaboration_type == CollaborationType.SEQUENTIAL:
                result = await self._execute_sequential_workflow(workflow)
            elif workflow.collaboration_type == CollaborationType.PARALLEL:
                result = await self._execute_parallel_workflow(workflow)
            elif workflow.collaboration_type == CollaborationType.PIPELINE:
                result = await self._execute_pipeline_workflow(workflow)
            else:
                raise ValueError(f"不支持的协作类型: {workflow.collaboration_type}")
            
            workflow.status = WorkflowStatus.COMPLETED
            workflow.completed_at = datetime.now()
            
            logger.info(f"工作流 {workflow_id} 执行完成")
            
            return {
                "workflow_id": workflow_id,
                "status": workflow.status.value,
                "execution_time": (workflow.completed_at - workflow.started_at).total_seconds(),
                "tasks_completed": len([t for t in workflow.tasks if t.status == WorkflowStatus.COMPLETED]),
                "total_tasks": len(workflow.tasks),
                "result": result
            }
            
        except Exception as e:
            logger.error(f"执行工作流失败: {e}")
            if workflow_id in self.workflows:
                self.workflows[workflow_id].status = WorkflowStatus.FAILED
            raise
    
    async def _execute_sequential_workflow(self, workflow: Workflow) -> Dict[str, Any]:
        """执行顺序工作流"""
        results = {}
        
        for task in workflow.tasks:
            # 检查依赖是否完成
            if not await self._check_task_dependencies(task, workflow.tasks):
                logger.warning(f"任务 {task.id} 的依赖未满足，跳过执行")
                continue
            
            # 执行任务
            task.status = WorkflowStatus.RUNNING
            task.start_time = datetime.now()
            
            try:
                agent = self.agents[task.agent_id]
                task_result = await agent.process_task(task.task_request)
                
                task.result = task_result
                task.status = WorkflowStatus.COMPLETED
                task.end_time = datetime.now()
                
                results[task.id] = task_result.result
                
                logger.info(f"任务 {task.id} 执行成功")
                
            except Exception as e:
                task.status = WorkflowStatus.FAILED
                task.end_time = datetime.now()
                logger.error(f"任务 {task.id} 执行失败: {e}")
                
                # 根据重试策略决定是否重试
                if task.retry_count < task.max_retries:
                    task.retry_count += 1
                    task.status = WorkflowStatus.PENDING
                    logger.info(f"任务 {task.id} 将进行第 {task.retry_count} 次重试")
                else:
                    raise
        
        return results
    
    async def _execute_parallel_workflow(self, workflow: Workflow) -> Dict[str, Any]:
        """执行并行工作流"""
        # 创建任务协程
        task_coroutines = []
        for task in workflow.tasks:
            if await self._check_task_dependencies(task, workflow.tasks):
                task_coroutines.append(self._execute_single_task(task))
        
        # 并行执行所有任务
        results = await asyncio.gather(*task_coroutines, return_exceptions=True)
        
        # 处理结果
        workflow_results = {}
        for i, (task, result) in enumerate(zip(workflow.tasks, results)):
            if isinstance(result, Exception):
                logger.error(f"任务 {task.id} 执行失败: {result}")
                task.status = WorkflowStatus.FAILED
            else:
                workflow_results[task.id] = result
                task.status = WorkflowStatus.COMPLETED
        
        return workflow_results
    
    async def _execute_pipeline_workflow(self, workflow: Workflow) -> Dict[str, Any]:
        """执行流水线工作流"""
        results = {}
        shared_context = {}
        
        # 按依赖关系排序任务
        sorted_tasks = self._topological_sort_tasks(workflow.tasks)
        
        for task in sorted_tasks:
            # 将前一个任务的结果作为输入
            if shared_context:
                task.task_request.parameters.update(shared_context)
            
            # 执行任务
            task.status = WorkflowStatus.RUNNING
            task.start_time = datetime.now()
            
            try:
                agent = self.agents[task.agent_id]
                task_result = await agent.process_task(task.task_request)
                
                task.result = task_result
                task.status = WorkflowStatus.COMPLETED
                task.end_time = datetime.now()
                
                # 更新共享上下文
                shared_context.update(task_result.result)
                results[task.id] = task_result.result
                
                logger.info(f"流水线任务 {task.id} 执行成功")
                
            except Exception as e:
                task.status = WorkflowStatus.FAILED
                task.end_time = datetime.now()
                logger.error(f"流水线任务 {task.id} 执行失败: {e}")
                raise
        
        return results
    
    async def _execute_single_task(self, task: WorkflowTask) -> Any:
        """执行单个任务"""
        task.status = WorkflowStatus.RUNNING
        task.start_time = datetime.now()
        
        try:
            agent = self.agents[task.agent_id]
            task_result = await agent.process_task(task.task_request)
            
            task.result = task_result
            task.end_time = datetime.now()
            
            return task_result.result
            
        except Exception as e:
            task.end_time = datetime.now()
            logger.error(f"任务 {task.id} 执行失败: {e}")
            raise
    
    async def _check_task_dependencies(self, task: WorkflowTask, all_tasks: List[WorkflowTask]) -> bool:
        """检查任务依赖是否满足"""
        if not task.dependencies:
            return True
        
        # 创建任务ID到任务的映射
        task_map = {t.id: t for t in all_tasks}
        
        for dep_task_id in task.dependencies:
            if dep_task_id in task_map:
                dep_task = task_map[dep_task_id]
                if dep_task.status != WorkflowStatus.COMPLETED:
                    return False
            else:
                # 依赖的任务不存在，检查是否是任务类型依赖
                dep_tasks = [t for t in all_tasks if t.task_request.type == dep_task_id]
                if not dep_tasks or not all(t.status == WorkflowStatus.COMPLETED for t in dep_tasks):
                    return False
        
        return True
    
    def _topological_sort_tasks(self, tasks: List[WorkflowTask]) -> List[WorkflowTask]:
        """对任务进行拓扑排序"""
        # 简单的拓扑排序实现
        sorted_tasks = []
        remaining_tasks = tasks.copy()
        
        while remaining_tasks:
            # 找到没有依赖或依赖已满足的任务
            ready_tasks = []
            for task in remaining_tasks:
                if not task.dependencies or all(
                    dep in [t.id for t in sorted_tasks] or 
                    dep in [t.task_request.type for t in sorted_tasks]
                    for dep in task.dependencies
                ):
                    ready_tasks.append(task)
            
            if not ready_tasks:
                # 如果没有可执行的任务，可能存在循环依赖
                logger.warning("检测到可能的循环依赖，按原顺序执行剩余任务")
                sorted_tasks.extend(remaining_tasks)
                break
            
            # 添加就绪的任务
            for task in ready_tasks:
                sorted_tasks.append(task)
                remaining_tasks.remove(task)
        
        return sorted_tasks
    
    async def send_message(self, message: AgentMessage) -> bool:
        """发送消息"""
        try:
            # 处理消息
            for handler in self.message_handlers.get(message.type, []):
                await handler(message)
            
            # 路由消息到目标智能体
            if message.recipient_id in self.agents:
                agent = self.agents[message.recipient_id]
                await agent.receive_message(message)
                return True
            else:
                logger.warning(f"目标智能体 {message.recipient_id} 不存在")
                return False
                
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False
    
    async def broadcast_message(self, message: AgentMessage, exclude_sender: bool = True) -> int:
        """广播消息"""
        sent_count = 0
        
        for agent_id, agent in self.agents.items():
            if exclude_sender and agent_id == message.sender_id:
                continue
            
            try:
                message_copy = AgentMessage(
                    id=f"{message.id}_{agent_id}",
                    type=message.type,
                    sender_id=message.sender_id,
                    recipient_id=agent_id,
                    content=message.content,
                    priority=message.priority,
                    timestamp=message.timestamp
                )
                
                await agent.receive_message(message_copy)
                sent_count += 1
                
            except Exception as e:
                logger.error(f"向智能体 {agent_id} 广播消息失败: {e}")
        
        return sent_count
    
    async def create_collaboration_session(self, participants: List[str], 
                                         workflow_id: str) -> str:
        """创建协作会话"""
        try:
            session_id = f"session_{uuid.uuid4().hex[:8]}"
            
            if workflow_id not in self.workflows:
                raise ValueError(f"工作流 {workflow_id} 不存在")
            
            session = CollaborationSession(
                id=session_id,
                participants=participants,
                workflow=self.workflows[workflow_id],
                messages=[],
                shared_context={},
                created_at=datetime.now(),
                last_activity=datetime.now()
            )
            
            self.sessions[session_id] = session
            logger.info(f"协作会话 {session_id} 创建成功")
            
            return session_id
            
        except Exception as e:
            logger.error(f"创建协作会话失败: {e}")
            raise
    
    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """获取工作流状态"""
        if workflow_id not in self.workflows:
            raise ValueError(f"工作流 {workflow_id} 不存在")
        
        workflow = self.workflows[workflow_id]
        
        # 统计任务状态
        task_status_counts = {}
        for status in WorkflowStatus:
            task_status_counts[status.value] = len([
                t for t in workflow.tasks if t.status == status
            ])
        
        # 计算进度
        completed_tasks = len([t for t in workflow.tasks if t.status == WorkflowStatus.COMPLETED])
        total_tasks = len(workflow.tasks)
        progress = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        return {
            "workflow_id": workflow_id,
            "name": workflow.name,
            "status": workflow.status.value,
            "progress": f"{progress:.1f}%",
            "task_status_counts": task_status_counts,
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "created_at": workflow.created_at.isoformat(),
            "started_at": workflow.started_at.isoformat() if workflow.started_at else None,
            "completed_at": workflow.completed_at.isoformat() if workflow.completed_at else None
        }
    
    async def _handle_request_message(self, message: AgentMessage):
        """处理请求消息"""
        logger.info(f"处理请求消息: {message.id}")
    
    async def _handle_response_message(self, message: AgentMessage):
        """处理响应消息"""
        logger.info(f"处理响应消息: {message.id}")
    
    async def _handle_notification_message(self, message: AgentMessage):
        """处理通知消息"""
        logger.info(f"处理通知消息: {message.id}")
    
    async def _handle_error_message(self, message: AgentMessage):
        """处理错误消息"""
        logger.error(f"处理错误消息: {message.id} - {message.content}")
    
    def get_registered_agents(self) -> List[Dict[str, Any]]:
        """获取已注册的智能体列表"""
        agents_info = []
        for agent_id, agent in self.agents.items():
            agents_info.append({
                "agent_id": agent_id,
                "name": agent.name,
                "description": agent.description,
                "type": agent.__class__.__name__,
                "capabilities": len(agent.capabilities) if hasattr(agent, 'capabilities') else 0
            })
        return agents_info
    
    def get_workflow_templates(self) -> List[Dict[str, Any]]:
        """获取工作流模板列表"""
        templates = []
        for template_name, template in self.workflow_templates.items():
            templates.append({
                "name": template_name,
                "display_name": template["name"],
                "description": template["description"],
                "collaboration_type": template["collaboration_type"],
                "task_count": len(template["tasks"])
            })
        return templates