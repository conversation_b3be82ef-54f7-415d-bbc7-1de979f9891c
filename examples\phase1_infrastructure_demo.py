"""
第一阶段基础设施演示
展示分布式状态管理、缓存系统、智能体池化、任务调度和性能监控的使用
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入基础设施组件
from src.langgraph_system.core.infrastructure import (
    initialize_infrastructure,
    shutdown_infrastructure,
    get_infrastructure_manager
)
from src.langgraph_system.states.project_state import ProjectState, TaskType
from src.langgraph_system.agents.base_agent import EnhancedAgentAdapter
from src.langgraph_system.core.task_scheduler import TaskPriority


class DemoAgent(EnhancedAgentAdapter):
    """演示智能体"""
    
    def __init__(self, agent_name: str = "demo_agent"):
        self.agent_name = agent_name
        self.capabilities = {
            "data_processing": 0.9,
            "analysis": 0.8,
            "reporting": 0.7
        }
    
    async def process(self, state: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理任务"""
        task_type = context.get("task_type", "general")
        processing_time = context.get("processing_time", 0.5)
        
        logger.info(f"Agent {self.agent_name} processing {task_type} task...")
        
        # 模拟处理时间
        await asyncio.sleep(processing_time)
        
        return {
            "status": "completed",
            "agent": self.agent_name,
            "task_type": task_type,
            "result": f"Task {task_type} completed successfully",
            "processing_time": processing_time,
            "timestamp": datetime.now().isoformat()
        }
    
    async def cleanup(self):
        """清理资源"""
        logger.info(f"Agent {self.agent_name} cleanup completed")


async def demo_state_management(infrastructure_manager):
    """演示状态管理功能"""
    logger.info("=== 演示状态管理功能 ===")
    
    state_manager = infrastructure_manager.get_component("state_manager")
    
    # 创建项目状态
    project_state = ProjectState(
        project_name="基础设施演示项目",
        current_task=TaskType.DEVELOPMENT,
        description="演示v0.3基础设施功能"
    )
    
    logger.info(f"创建项目状态: {project_state.project_name}")
    
    # 保存状态
    await state_manager.save_project_state(project_state, create_checkpoint=True)
    logger.info("项目状态已保存并创建检查点")
    
    # 修改状态
    project_state.description = "更新后的项目描述"
    project_state.current_task = TaskType.TESTING
    await state_manager.save_project_state(project_state, create_checkpoint=True)
    logger.info("项目状态已更新")
    
    # 获取版本历史
    versions = await state_manager.get_version_history(project_state.project_id)
    logger.info(f"版本历史: {len(versions)} 个版本")
    
    # 恢复到第一个版本
    if versions:
        first_version = versions[0]
        restored_state = await state_manager.restore_from_checkpoint(
            project_state.project_id,
            first_version.version_id
        )
        logger.info(f"已恢复到版本: {first_version.version_number}")
        logger.info(f"恢复后的描述: {restored_state.description}")
    
    return project_state.project_id


async def demo_cache_system(infrastructure_manager):
    """演示缓存系统功能"""
    logger.info("=== 演示缓存系统功能 ===")
    
    cache_manager = infrastructure_manager.get_component("cache_manager")
    
    # 基本缓存操作
    cache_key = "demo_data"
    cache_value = {
        "user_id": 12345,
        "preferences": {"theme": "dark", "language": "zh-CN"},
        "last_login": datetime.now().isoformat()
    }
    
    logger.info("设置缓存数据...")
    await cache_manager.set(cache_key, cache_value, ttl=300)
    
    logger.info("获取缓存数据...")
    cached_data = await cache_manager.get(cache_key)
    logger.info(f"缓存数据: {cached_data}")
    
    # 演示缓存装饰器
    @cache_manager.cache_result(ttl=60, key_prefix="expensive_calc")
    async def expensive_calculation(x: int, y: int) -> int:
        logger.info(f"执行复杂计算: {x} + {y}")
        await asyncio.sleep(1)  # 模拟耗时操作
        return x + y
    
    logger.info("第一次调用复杂计算...")
    start_time = time.time()
    result1 = await expensive_calculation(10, 20)
    time1 = time.time() - start_time
    logger.info(f"结果: {result1}, 耗时: {time1:.2f}s")
    
    logger.info("第二次调用复杂计算（应该使用缓存）...")
    start_time = time.time()
    result2 = await expensive_calculation(10, 20)
    time2 = time.time() - start_time
    logger.info(f"结果: {result2}, 耗时: {time2:.2f}s")
    
    # 获取缓存统计
    stats = cache_manager.get_statistics()
    logger.info(f"缓存统计: 命中率 {stats['statistics']['hit_rate']:.2%}")


async def demo_agent_pooling(infrastructure_manager):
    """演示智能体池化功能"""
    logger.info("=== 演示智能体池化功能 ===")
    
    pool_manager = infrastructure_manager.get_component("agent_pool")
    
    # 注册智能体类型
    def create_demo_agent():
        return DemoAgent(f"demo_agent_{int(time.time() * 1000) % 1000}")
    
    pool_manager.register_agent_type(
        agent_type="demo_agent",
        factory=create_demo_agent,
        min_size=2,
        max_size=5
    )
    
    logger.info("智能体类型已注册")
    
    # 并发执行多个任务
    async def execute_agent_task(task_id: int):
        async def agent_work(agent_instance):
            return await agent_instance.process(
                {},
                {
                    "task_type": f"task_{task_id}",
                    "processing_time": 0.5
                }
            )
        
        return await pool_manager.execute_with_agent("demo_agent", agent_work)
    
    logger.info("并发执行5个智能体任务...")
    tasks = [execute_agent_task(i) for i in range(5)]
    results = await asyncio.gather(*tasks)
    
    for i, result in enumerate(results):
        logger.info(f"任务 {i}: {result['result']} (智能体: {result['agent']})")
    
    # 获取池统计
    pool_stats = pool_manager.get_pool_stats("demo_agent")
    logger.info(f"智能体池统计: {pool_stats}")


async def demo_task_scheduling(infrastructure_manager):
    """演示任务调度功能"""
    logger.info("=== 演示任务调度功能 ===")
    
    scheduler = infrastructure_manager.get_component("task_scheduler")
    
    # 定义不同类型的任务
    async def quick_task(task_name: str):
        logger.info(f"执行快速任务: {task_name}")
        await asyncio.sleep(0.2)
        return f"快速任务 {task_name} 完成"
    
    async def slow_task(task_name: str):
        logger.info(f"执行慢速任务: {task_name}")
        await asyncio.sleep(1.0)
        return f"慢速任务 {task_name} 完成"
    
    async def data_processing_task(data_size: int):
        logger.info(f"处理数据: {data_size} 条记录")
        await asyncio.sleep(data_size * 0.1)
        return f"处理了 {data_size} 条数据"
    
    # 调度不同优先级的任务
    logger.info("调度不同优先级的任务...")
    
    task_ids = []
    
    # 高优先级任务
    task_id = await scheduler.schedule_task(
        "high_priority_task",
        quick_task,
        args=["重要任务"],
        priority=TaskPriority.HIGH
    )
    task_ids.append(task_id)
    
    # 普通优先级任务
    task_id = await scheduler.schedule_task(
        "normal_task",
        slow_task,
        args=["普通任务"],
        priority=TaskPriority.NORMAL
    )
    task_ids.append(task_id)
    
    # 低优先级任务
    task_id = await scheduler.schedule_task(
        "low_priority_task",
        data_processing_task,
        args=[100],
        priority=TaskPriority.LOW
    )
    task_ids.append(task_id)
    
    # 等待所有任务完成
    logger.info("等待任务完成...")
    for task_id in task_ids:
        result = await scheduler.wait_for_task(task_id, timeout=10.0)
        logger.info(f"任务完成: {result.status.value}")
    
    # 演示任务依赖
    logger.info("演示任务依赖...")
    
    execution_order = []
    
    async def dependent_task(task_name: str):
        execution_order.append(task_name)
        logger.info(f"执行依赖任务: {task_name}")
        await asyncio.sleep(0.3)
        return f"依赖任务 {task_name} 完成"
    
    # 创建依赖链: A -> B -> C
    task_a_id = await scheduler.schedule_task("task_a", dependent_task, args=["A"])
    task_b_id = await scheduler.schedule_task("task_b", dependent_task, args=["B"], dependencies=[task_a_id])
    task_c_id = await scheduler.schedule_task("task_c", dependent_task, args=["C"], dependencies=[task_b_id])
    
    await scheduler.wait_for_task(task_c_id, timeout=10.0)
    logger.info(f"依赖任务执行顺序: {execution_order}")
    
    # 获取调度器统计
    scheduler_stats = scheduler.get_scheduler_stats()
    logger.info(f"调度器统计: 已调度 {scheduler_stats['total_tasks_scheduled']} 个任务")


async def demo_performance_monitoring(infrastructure_manager):
    """演示性能监控功能"""
    logger.info("=== 演示性能监控功能 ===")
    
    monitor = infrastructure_manager.get_component("performance_monitor")
    
    # 记录各种指标
    logger.info("记录性能指标...")
    
    # 计数器指标
    for i in range(10):
        monitor.increment_counter("demo_requests")
        await asyncio.sleep(0.1)
    
    # 仪表指标
    monitor.set_gauge("demo_active_users", 42)
    monitor.set_gauge("demo_memory_usage", 75.5)
    
    # 直方图指标
    import random
    for _ in range(20):
        response_time = random.uniform(0.1, 2.0)
        monitor.observe_histogram("demo_response_time", response_time)
    
    # 计时器指标
    for i in range(5):
        with monitor.time_operation("demo_operation"):
            await asyncio.sleep(random.uniform(0.1, 0.5))
    
    # 获取指标摘要
    metrics_summary = monitor.get_metrics_summary()
    logger.info("指标摘要:")
    for metric_name, metric_data in metrics_summary.items():
        if metric_name.startswith("demo_"):
            latest_value = metric_data.get("latest_value")
            logger.info(f"  {metric_name}: {latest_value}")
    
    # 获取告警摘要
    alerts_summary = monitor.get_alerts_summary()
    logger.info(f"告警摘要: {alerts_summary['total_alerts']} 个告警")


async def demo_infrastructure_health(infrastructure_manager):
    """演示基础设施健康检查"""
    logger.info("=== 演示基础设施健康检查 ===")
    
    # 获取健康状态
    health_status = await infrastructure_manager.run_health_check()
    logger.info(f"整体健康状态: {health_status['overall_status']}")
    
    for component, check in health_status["checks"].items():
        logger.info(f"  {component}: {check['status']} - {check['message']}")
    
    # 获取详细状态
    detailed_status = await infrastructure_manager.get_detailed_status()
    logger.info(f"系统运行时间: {detailed_status['uptime_seconds']:.1f} 秒")
    
    # 显示各组件统计
    if "state_manager_stats" in detailed_status:
        stats = detailed_status["state_manager_stats"]
        logger.info(f"状态管理器: {stats.get('project_count', 0)} 个项目")
    
    if "cache_manager_stats" in detailed_status:
        stats = detailed_status["cache_manager_stats"]["statistics"]
        logger.info(f"缓存管理器: 命中率 {stats.get('hit_rate', 0):.2%}")
    
    if "task_scheduler_stats" in detailed_status:
        stats = detailed_status["task_scheduler_stats"]
        logger.info(f"任务调度器: {stats.get('total_tasks_completed', 0)} 个任务完成")


async def main():
    """主演示函数"""
    logger.info("🚀 开始LangGraph多智能体系统v0.3基础设施演示")
    
    # 配置
    config = {
        "redis_url": "redis://localhost:6379",
        "memory_cache_size": 1000,
        "cache_default_ttl": 300,
        "scheduler_max_workers": 5,
        "scheduler_max_queue_size": 100
    }
    
    try:
        # 初始化基础设施
        logger.info("初始化基础设施...")
        infrastructure_manager = await initialize_infrastructure(config)
        logger.info("✅ 基础设施初始化完成")
        
        # 运行各个演示
        await demo_state_management(infrastructure_manager)
        await demo_cache_system(infrastructure_manager)
        await demo_agent_pooling(infrastructure_manager)
        await demo_task_scheduling(infrastructure_manager)
        await demo_performance_monitoring(infrastructure_manager)
        await demo_infrastructure_health(infrastructure_manager)
        
        logger.info("🎉 所有演示完成！")
        
        # 等待一段时间以观察监控数据
        logger.info("等待5秒以收集更多监控数据...")
        await asyncio.sleep(5)
        
        # 最终状态检查
        final_health = await infrastructure_manager.run_health_check()
        logger.info(f"最终健康状态: {final_health['overall_status']}")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        raise
    
    finally:
        # 关闭基础设施
        logger.info("关闭基础设施...")
        await shutdown_infrastructure()
        logger.info("✅ 基础设施关闭完成")


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())