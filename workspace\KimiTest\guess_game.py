import random

def guess_number_game():
    """简单的猜数字游戏"""
    secret = random.randint(1, 100)
    attempts = 0
    max_attempts = 10
    
    print("欢迎来到猜数字游戏！")
    print("我已经想好了一个1到100之间的整数，你有10次机会来猜它。")
    
    while attempts < max_attempts:
        try:
            guess = int(input(f"第{attempts + 1}次尝试，请输入你的猜测（1-100）: "))
            
            if guess < 1 or guess > 100:
                print("请输入1到100之间的数字！")
                continue
                
            attempts += 1
            
            if guess == secret:
                print(f"恭喜你！猜对了！数字就是{secret}，你用了{attempts}次尝试。")
                return
            elif guess < secret:
                print("太小了！再试试。")
            else:
                print("太大了！再试试。")
                
        except ValueError:
            print("请输入有效的数字！")
    
    print(f"游戏结束！你没有猜中。正确的数字是{secret}。")

if __name__ == "__main__":
    guess_number_game()
