#!/usr/bin/env python3
"""
工作流监控器
提供实时监控、调试和性能分析能力
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import logging
from collections import defaultdict, deque
import threading
from concurrent.futures import Thread<PERSON>oolExecutor

from .workflow_engine import WorkflowExecution, WorkflowStatus, WorkflowNode

logger = logging.getLogger(__name__)


class MonitorEventType(Enum):
    """监控事件类型"""
    WORKFLOW_STARTED = "workflow_started"
    WORKFLOW_COMPLETED = "workflow_completed"
    WORKFLOW_FAILED = "workflow_failed"
    WORKFLOW_PAUSED = "workflow_paused"
    WORKFLOW_RESUMED = "workflow_resumed"
    WORKFLOW_CANCELLED = "workflow_cancelled"
    NODE_STARTED = "node_started"
    NODE_COMPLETED = "node_completed"
    NODE_FAILED = "node_failed"
    NODE_RETRIED = "node_retried"
    PERFORMANCE_ALERT = "performance_alert"
    RESOURCE_ALERT = "resource_alert"
    ERROR_OCCURRED = "error_occurred"


@dataclass
class MonitorEvent:
    """监控事件"""
    id: str
    event_type: MonitorEventType
    timestamp: datetime
    workflow_id: str
    execution_id: str
    node_id: Optional[str] = None
    data: Dict[str, Any] = field(default_factory=dict)
    severity: str = "info"  # info, warning, error, critical
    message: str = ""


@dataclass
class PerformanceMetrics:
    """性能指标"""
    execution_id: str
    workflow_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_duration: Optional[float] = None
    node_count: int = 0
    completed_nodes: int = 0
    failed_nodes: int = 0
    parallel_tasks_peak: int = 0
    memory_usage_peak: float = 0.0
    cpu_usage_avg: float = 0.0
    throughput: float = 0.0  # nodes per second
    error_rate: float = 0.0


@dataclass
class AlertRule:
    """告警规则"""
    id: str
    name: str
    condition: str
    threshold: float
    severity: str
    enabled: bool = True
    cooldown_seconds: int = 300
    last_triggered: Optional[datetime] = None


class WorkflowDebugger:
    """工作流调试器"""
    
    def __init__(self):
        self.breakpoints: Dict[str, Set[str]] = defaultdict(set)  # execution_id -> node_ids
        self.step_mode: Dict[str, bool] = {}  # execution_id -> step_mode
        self.execution_traces: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        self.variable_watches: Dict[str, List[str]] = defaultdict(list)  # execution_id -> variables
        
    def set_breakpoint(self, execution_id: str, node_id: str):
        """设置断点"""
        self.breakpoints[execution_id].add(node_id)
        logger.info(f"在执行 {execution_id} 的节点 {node_id} 设置断点")
    
    def remove_breakpoint(self, execution_id: str, node_id: str):
        """移除断点"""
        self.breakpoints[execution_id].discard(node_id)
        logger.info(f"移除执行 {execution_id} 的节点 {node_id} 断点")
    
    def enable_step_mode(self, execution_id: str):
        """启用单步模式"""
        self.step_mode[execution_id] = True
        logger.info(f"为执行 {execution_id} 启用单步模式")
    
    def disable_step_mode(self, execution_id: str):
        """禁用单步模式"""
        self.step_mode[execution_id] = False
        logger.info(f"为执行 {execution_id} 禁用单步模式")
    
    def should_pause_at_node(self, execution_id: str, node_id: str) -> bool:
        """检查是否应该在节点暂停"""
        # 检查断点
        if node_id in self.breakpoints.get(execution_id, set()):
            return True
        
        # 检查单步模式
        if self.step_mode.get(execution_id, False):
            return True
        
        return False
    
    def add_trace_entry(self, execution_id: str, node_id: str, action: str, data: Dict[str, Any]):
        """添加跟踪条目"""
        trace_entry = {
            "timestamp": datetime.now().isoformat(),
            "node_id": node_id,
            "action": action,
            "data": data
        }
        self.execution_traces[execution_id].append(trace_entry)
    
    def get_execution_trace(self, execution_id: str) -> List[Dict[str, Any]]:
        """获取执行跟踪"""
        return self.execution_traces.get(execution_id, [])
    
    def add_variable_watch(self, execution_id: str, variable_path: str):
        """添加变量监视"""
        self.variable_watches[execution_id].append(variable_path)
    
    def get_watched_variables(self, execution_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """获取监视的变量值"""
        watched = {}
        for var_path in self.variable_watches.get(execution_id, []):
            try:
                value = self._get_nested_value(context, var_path)
                watched[var_path] = value
            except:
                watched[var_path] = "<not found>"
        return watched
    
    def _get_nested_value(self, data: Dict[str, Any], path: str) -> Any:
        """获取嵌套值"""
        keys = path.split(".")
        value = data
        for key in keys:
            value = value[key]
        return value


class WorkflowMonitor:
    """工作流监控器"""
    
    def __init__(self, max_events: int = 10000):
        self.max_events = max_events
        self.events: deque = deque(maxlen=max_events)
        self.event_handlers: Dict[MonitorEventType, List[Callable]] = defaultdict(list)
        
        # 性能监控
        self.performance_metrics: Dict[str, PerformanceMetrics] = {}
        self.active_executions: Dict[str, WorkflowExecution] = {}
        
        # 告警系统
        self.alert_rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, List[MonitorEvent]] = defaultdict(list)
        
        # 调试器
        self.debugger = WorkflowDebugger()
        
        # 统计信息
        self.statistics = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0,
            "total_nodes_executed": 0,
            "error_rate": 0.0
        }
        
        # 监控线程
        self.monitoring_active = False
        self.monitoring_thread = None
        
        logger.info("工作流监控器初始化完成")
    
    def start_monitoring(self):
        """启动监控"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
            self.monitoring_thread.daemon = True
            self.monitoring_thread.start()
            logger.info("工作流监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("工作流监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                self._check_performance_alerts()
                self._update_statistics()
                time.sleep(1)  # 每秒检查一次
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
    
    def register_execution(self, execution: WorkflowExecution):
        """注册工作流执行"""
        self.active_executions[execution.id] = execution
        
        # 创建性能指标
        metrics = PerformanceMetrics(
            execution_id=execution.id,
            workflow_id=execution.workflow_id,
            start_time=execution.start_time or datetime.now(),
            node_count=len(execution.node_executions)
        )
        self.performance_metrics[execution.id] = metrics
        
        # 发送事件
        self._emit_event(
            MonitorEventType.WORKFLOW_STARTED,
            execution.workflow_id,
            execution.id,
            message=f"工作流执行开始: {execution.workflow_id}"
        )
    
    def update_execution(self, execution: WorkflowExecution):
        """更新工作流执行状态"""
        self.active_executions[execution.id] = execution
        
        # 更新性能指标
        if execution.id in self.performance_metrics:
            metrics = self.performance_metrics[execution.id]
            metrics.completed_nodes = len(execution.completed_nodes)
            metrics.failed_nodes = len(execution.failed_nodes)
            
            if execution.status == WorkflowStatus.COMPLETED:
                metrics.end_time = execution.end_time
                metrics.total_duration = execution.execution_time
                if metrics.total_duration and metrics.total_duration > 0:
                    metrics.throughput = metrics.completed_nodes / metrics.total_duration
                
                self._emit_event(
                    MonitorEventType.WORKFLOW_COMPLETED,
                    execution.workflow_id,
                    execution.id,
                    data={"duration": execution.execution_time},
                    message=f"工作流执行完成: {execution.workflow_id}"
                )
                
                # 移除活跃执行
                self.active_executions.pop(execution.id, None)
                
            elif execution.status == WorkflowStatus.FAILED:
                metrics.end_time = execution.end_time
                metrics.error_rate = metrics.failed_nodes / max(metrics.node_count, 1)
                
                self._emit_event(
                    MonitorEventType.WORKFLOW_FAILED,
                    execution.workflow_id,
                    execution.id,
                    data={"error": execution.error},
                    severity="error",
                    message=f"工作流执行失败: {execution.error}"
                )
                
                # 移除活跃执行
                self.active_executions.pop(execution.id, None)
    
    def on_node_started(self, execution_id: str, node_id: str, node: WorkflowNode):
        """节点开始执行"""
        execution = self.active_executions.get(execution_id)
        if not execution:
            return
        
        # 检查调试断点
        if self.debugger.should_pause_at_node(execution_id, node_id):
            logger.info(f"在节点 {node_id} 暂停执行")
            # 这里可以实现暂停逻辑
        
        # 添加跟踪
        self.debugger.add_trace_entry(
            execution_id, node_id, "started", 
            {"node_type": node.node_type.value, "agent_id": node.agent_id}
        )
        
        self._emit_event(
            MonitorEventType.NODE_STARTED,
            execution.workflow_id,
            execution_id,
            node_id=node_id,
            data={"node_name": node.name, "node_type": node.node_type.value},
            message=f"节点开始执行: {node.name}"
        )
    
    def on_node_completed(self, execution_id: str, node_id: str, result: Dict[str, Any]):
        """节点执行完成"""
        execution = self.active_executions.get(execution_id)
        if not execution:
            return
        
        # 添加跟踪
        self.debugger.add_trace_entry(
            execution_id, node_id, "completed", 
            {"result": result}
        )
        
        self._emit_event(
            MonitorEventType.NODE_COMPLETED,
            execution.workflow_id,
            execution_id,
            node_id=node_id,
            data={"result": result},
            message=f"节点执行完成: {node_id}"
        )
    
    def on_node_failed(self, execution_id: str, node_id: str, error: str):
        """节点执行失败"""
        execution = self.active_executions.get(execution_id)
        if not execution:
            return
        
        # 添加跟踪
        self.debugger.add_trace_entry(
            execution_id, node_id, "failed", 
            {"error": error}
        )
        
        self._emit_event(
            MonitorEventType.NODE_FAILED,
            execution.workflow_id,
            execution_id,
            node_id=node_id,
            data={"error": error},
            severity="error",
            message=f"节点执行失败: {node_id} - {error}"
        )
    
    def _emit_event(
        self, 
        event_type: MonitorEventType, 
        workflow_id: str, 
        execution_id: str,
        node_id: Optional[str] = None,
        data: Dict[str, Any] = None,
        severity: str = "info",
        message: str = ""
    ):
        """发送监控事件"""
        event = MonitorEvent(
            id=f"{execution_id}_{int(time.time() * 1000)}",
            event_type=event_type,
            timestamp=datetime.now(),
            workflow_id=workflow_id,
            execution_id=execution_id,
            node_id=node_id,
            data=data or {},
            severity=severity,
            message=message
        )
        
        self.events.append(event)
        
        # 调用事件处理器
        for handler in self.event_handlers[event_type]:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"事件处理器错误: {e}")
        
        # 检查告警规则
        self._check_alert_rules(event)
    
    def add_event_handler(self, event_type: MonitorEventType, handler: Callable):
        """添加事件处理器"""
        self.event_handlers[event_type].append(handler)
    
    def remove_event_handler(self, event_type: MonitorEventType, handler: Callable):
        """移除事件处理器"""
        if handler in self.event_handlers[event_type]:
            self.event_handlers[event_type].remove(handler)
    
    def add_alert_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.alert_rules[rule.id] = rule
        logger.info(f"添加告警规则: {rule.name}")
    
    def remove_alert_rule(self, rule_id: str):
        """移除告警规则"""
        if rule_id in self.alert_rules:
            del self.alert_rules[rule_id]
            logger.info(f"移除告警规则: {rule_id}")
    
    def _check_alert_rules(self, event: MonitorEvent):
        """检查告警规则"""
        for rule in self.alert_rules.values():
            if not rule.enabled:
                continue
            
            # 检查冷却时间
            if rule.last_triggered:
                cooldown_end = rule.last_triggered + timedelta(seconds=rule.cooldown_seconds)
                if datetime.now() < cooldown_end:
                    continue
            
            # 评估告警条件
            if self._evaluate_alert_condition(rule, event):
                self._trigger_alert(rule, event)
    
    def _evaluate_alert_condition(self, rule: AlertRule, event: MonitorEvent) -> bool:
        """评估告警条件"""
        # 这里可以实现更复杂的条件评估逻辑
        # 简化示例：检查执行时间超过阈值
        if rule.condition == "execution_time_exceeded":
            execution = self.active_executions.get(event.execution_id)
            if execution and execution.execution_time:
                return execution.execution_time > rule.threshold
        
        elif rule.condition == "error_rate_exceeded":
            metrics = self.performance_metrics.get(event.execution_id)
            if metrics:
                return metrics.error_rate > rule.threshold
        
        elif rule.condition == "node_failure":
            return event.event_type == MonitorEventType.NODE_FAILED
        
        return False
    
    def _trigger_alert(self, rule: AlertRule, event: MonitorEvent):
        """触发告警"""
        rule.last_triggered = datetime.now()
        
        alert_event = MonitorEvent(
            id=f"alert_{rule.id}_{int(time.time() * 1000)}",
            event_type=MonitorEventType.PERFORMANCE_ALERT,
            timestamp=datetime.now(),
            workflow_id=event.workflow_id,
            execution_id=event.execution_id,
            node_id=event.node_id,
            data={"rule_id": rule.id, "rule_name": rule.name, "original_event": event.id},
            severity=rule.severity,
            message=f"告警触发: {rule.name}"
        )
        
        self.events.append(alert_event)
        self.active_alerts[event.execution_id].append(alert_event)
        
        logger.warning(f"告警触发: {rule.name} - {alert_event.message}")
    
    def _check_performance_alerts(self):
        """检查性能告警"""
        for execution_id, execution in self.active_executions.items():
            metrics = self.performance_metrics.get(execution_id)
            if not metrics:
                continue
            
            # 检查执行时间过长
            if execution.start_time:
                current_duration = (datetime.now() - execution.start_time).total_seconds()
                if current_duration > 3600:  # 1小时
                    self._emit_event(
                        MonitorEventType.PERFORMANCE_ALERT,
                        execution.workflow_id,
                        execution_id,
                        severity="warning",
                        message=f"工作流执行时间过长: {current_duration:.1f}秒"
                    )
    
    def _update_statistics(self):
        """更新统计信息"""
        total_executions = len(self.performance_metrics)
        if total_executions == 0:
            return
        
        successful = sum(1 for m in self.performance_metrics.values() 
                        if m.end_time and m.failed_nodes == 0)
        failed = sum(1 for m in self.performance_metrics.values() 
                    if m.end_time and m.failed_nodes > 0)
        
        total_duration = sum(m.total_duration for m in self.performance_metrics.values() 
                           if m.total_duration)
        completed_executions = sum(1 for m in self.performance_metrics.values() 
                                 if m.total_duration)
        
        self.statistics.update({
            "total_executions": total_executions,
            "successful_executions": successful,
            "failed_executions": failed,
            "average_execution_time": total_duration / max(completed_executions, 1),
            "total_nodes_executed": sum(m.completed_nodes for m in self.performance_metrics.values()),
            "error_rate": failed / max(total_executions, 1)
        })
    
    def get_events(
        self, 
        execution_id: Optional[str] = None,
        event_type: Optional[MonitorEventType] = None,
        limit: int = 100
    ) -> List[MonitorEvent]:
        """获取监控事件"""
        events = list(self.events)
        
        # 过滤条件
        if execution_id:
            events = [e for e in events if e.execution_id == execution_id]
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        # 按时间倒序排列
        events.sort(key=lambda e: e.timestamp, reverse=True)
        
        return events[:limit]
    
    def get_performance_metrics(self, execution_id: str) -> Optional[PerformanceMetrics]:
        """获取性能指标"""
        return self.performance_metrics.get(execution_id)
    
    def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """获取执行状态"""
        execution = self.active_executions.get(execution_id)
        if not execution:
            return None
        
        metrics = self.performance_metrics.get(execution_id)
        watched_vars = self.debugger.get_watched_variables(execution_id, execution.context)
        
        return {
            "execution": {
                "id": execution.id,
                "workflow_id": execution.workflow_id,
                "status": execution.status.value,
                "progress": execution.progress,
                "current_nodes": list(execution.current_nodes),
                "completed_nodes": list(execution.completed_nodes),
                "failed_nodes": list(execution.failed_nodes)
            },
            "metrics": {
                "duration": metrics.total_duration if metrics else None,
                "throughput": metrics.throughput if metrics else None,
                "error_rate": metrics.error_rate if metrics else None
            },
            "debug": {
                "breakpoints": list(self.debugger.breakpoints.get(execution_id, set())),
                "step_mode": self.debugger.step_mode.get(execution_id, False),
                "watched_variables": watched_vars
            }
        }
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取监控仪表板数据"""
        return {
            "statistics": self.statistics,
            "active_executions": len(self.active_executions),
            "recent_events": [
                {
                    "id": e.id,
                    "type": e.event_type.value,
                    "timestamp": e.timestamp.isoformat(),
                    "workflow_id": e.workflow_id,
                    "execution_id": e.execution_id,
                    "severity": e.severity,
                    "message": e.message
                }
                for e in list(self.events)[-10:]
            ],
            "active_alerts": sum(len(alerts) for alerts in self.active_alerts.values()),
            "performance_summary": {
                "avg_execution_time": self.statistics["average_execution_time"],
                "success_rate": (self.statistics["successful_executions"] / 
                               max(self.statistics["total_executions"], 1)) * 100,
                "total_nodes": self.statistics["total_nodes_executed"]
            }
        }