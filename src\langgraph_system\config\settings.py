"""配置设置"""

from typing import Optional, Dict, Any
try:
    from pydantic.v1 import BaseSettings, Field
except ImportError:
    from pydantic import BaseSettings, Field
import os
from pathlib import Path

class Settings(BaseSettings):
    """系统配置"""
    
    # 基础配置
    app_name: str = Field(default="LangGraph Multi-Agent System")
    version: str = Field(default="0.1.0")
    debug: bool = Field(default=False)
    
    # LangGraph配置
    checkpoint_dir: str = Field(default="./checkpoints")
    max_retries: int = Field(default=3)
    timeout: int = Field(default=300)
    
    # LLM配置
    openai_api_key: Optional[str] = Field(default=None)
    anthropic_api_key: Optional[str] = Field(default=None)
    moonshot_api_key: Optional[str] = Field(default=None)
    default_model: str = Field(default="kimi-k2-0711-preview")
    default_provider: str = Field(default="moonshot")
    temperature: float = Field(default=0.7)
    
    # 日志配置
    log_level: str = Field(default="INFO")
    log_file: Optional[str] = Field(default=None)
    
    # 存储配置
    storage_backend: str = Field(default="memory")  # memory, sqlite, redis
    database_url: Optional[str] = Field(default=None)
    
    # MCP配置
    mcp_enabled: bool = Field(default=True)
    mcp_servers: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        env_file = ".env"
        env_prefix = "LANGGRAPH_"
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._setup_directories()
        
    def _setup_directories(self):
        """设置必要的目录"""
        Path(self.checkpoint_dir).mkdir(parents=True, exist_ok=True)
        
    def get_llm_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        return {
            "openai_api_key": self.openai_api_key,
            "anthropic_api_key": self.anthropic_api_key,
            "moonshot_api_key": self.moonshot_api_key,
            "default_model": self.default_model,
            "default_provider": self.default_provider,
            "temperature": self.temperature
        }
        
    def get_storage_config(self) -> Dict[str, Any]:
        """获取存储配置"""
        return {
            "backend": self.storage_backend,
            "database_url": self.database_url,
            "checkpoint_dir": self.checkpoint_dir
        }

# 全局配置实例
_settings = None

def get_settings() -> Settings:
    """获取配置实例"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings
