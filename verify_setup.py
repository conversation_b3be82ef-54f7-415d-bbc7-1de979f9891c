#!/usr/bin/env python3
"""
验证kimi-k2-0711-preview配置设置
"""

import os
import sys
from pathlib import Path

def verify_files():
    """验证必要的文件是否存在"""
    files_to_check = [
        "src/langgraph_system/llm/__init__.py",
        "src/langgraph_system/llm/config.py",
        "src/langgraph_system/llm/base_client.py",
        "src/langgraph_system/llm/moonshot_client.py",
        "src/langgraph_system/llm/factory.py",
        "src/langgraph_system/llm/openai_client.py",
        "src/langgraph_system/llm/anthropic_client.py",
        "src/langgraph_system/config/settings.py",
        ".env",
        "examples/use_kimi_k2.py",
        "docs/LLM_CONFIG.md"
    ]
    
    print("=== 文件验证 ===")
    all_exist = True
    
    for file_path in files_to_check:
        full_path = Path(file_path)
        if full_path.exists():
            print(f"[OK] {file_path}")
        else:
            print(f"[FAIL] {file_path}")
            all_exist = False
    
    return all_exist

def verify_env_config():
    """验证环境配置"""
    print("\n=== 环境配置验证 ===")
    
    # 检查.env文件
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if "kimi-k2-0711-preview" in content:
                print("[OK] .env文件已配置kimi-k2-0711-preview")
            else:
                print("[FAIL] .env文件未正确配置")
                return False
    else:
        print("[FAIL] .env文件不存在")
        return False
    
    # 检查配置内容
    expected_configs = [
        "LANGGRAPH_DEFAULT_PROVIDER=moonshot",
        "LANGGRAPH_DEFAULT_MODEL=kimi-k2-0711-preview",
        "LANGGRAPH_MOONSHOT_API_KEY="
    ]
    
    for config in expected_configs:
        if config in content:
            print(f"[OK] {config}")
        else:
            print(f"[WARN] {config} (需要设置)")
    
    return True

def verify_code_structure():
    """验证代码结构"""
    print("\n=== 代码结构验证 ===")
    
    # 验证LLM配置类
    try:
        with open("src/langgraph_system/llm/config.py", 'r', encoding='utf-8') as f:
            content = f.read()
            if "kimi-k2-0711-preview" in content:
                print("[OK] LLM配置包含kimi-k2-0711-preview")
            else:
                print("[FAIL] LLM配置不包含kimi-k2-0711-preview")
                return False
            
            if "MoonshotClient" in content or "moonshot" in content.lower():
                print("[OK] 支持Moonshot提供商")
            else:
                print("[FAIL] 不支持Moonshot提供商")
                return False
                
    except Exception as e:
        print(f"[FAIL] 读取配置文件失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=== kimi-k2-0711-preview 配置验证 ===")
    
    # 验证文件
    files_ok = verify_files()
    
    # 验证环境配置
    env_ok = verify_env_config()
    
    # 验证代码结构
    code_ok = verify_code_structure()
    
    print("\n" + "="*50)
    if files_ok and env_ok and code_ok:
        print("[SUCCESS] 配置验证成功！")
        print("\n下一步:")
        print("1. 安装依赖: pip install -r requirements.txt")
        print("2. 设置API密钥: 在.env文件中设置 MOONSHOT_API_KEY")
        print("3. 运行示例: python examples/use_kimi_k2.py")
        print("4. 查看文档: docs/LLM_CONFIG.md")
    else:
        print("[WARN] 配置验证失败，请检查上述问题")
    
    return files_ok and env_ok and code_ok

if __name__ == "__main__":
    main()
