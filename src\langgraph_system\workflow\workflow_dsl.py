#!/usr/bin/env python3
"""
工作流定义语言(DSL)
提供声明式的工作流定义和解析能力
"""

import yaml
import json
from typing import Dict, List, Any, Optional, Union
from dataclasses import asdict
import re
from datetime import datetime
import logging

from .workflow_engine import (
    WorkflowDefinition, WorkflowNode, NodeType, 
    WorkflowStatus, ExecutionMode
)

logger = logging.getLogger(__name__)


class WorkflowDSLParser:
    """工作流DSL解析器"""
    
    def __init__(self):
        self.node_type_mapping = {
            "task": NodeType.TASK,
            "parallel": NodeType.PARALLEL,
            "condition": NodeType.CONDITION,
            "loop": NodeType.LOOP,
            "merge": NodeType.MERGE,
            "split": NodeType.SPLIT,
            "delay": NodeType.DELAY,
            "human_approval": NodeType.HUMAN_APPROVAL
        }
        
        self.validators = {
            "workflow": self._validate_workflow,
            "node": self._validate_node,
            "condition": self._validate_condition,
            "edge": self._validate_edge
        }
    
    def parse_yaml(self, yaml_content: str) -> WorkflowDefinition:
        """解析YAML格式的工作流定义"""
        try:
            data = yaml.safe_load(yaml_content)
            return self._parse_workflow_data(data)
        except yaml.YAMLError as e:
            raise ValueError(f"YAML解析错误: {e}")
        except Exception as e:
            raise ValueError(f"工作流解析失败: {e}")
    
    def parse_json(self, json_content: str) -> WorkflowDefinition:
        """解析JSON格式的工作流定义"""
        try:
            data = json.loads(json_content)
            return self._parse_workflow_data(data)
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON解析错误: {e}")
        except Exception as e:
            raise ValueError(f"工作流解析失败: {e}")
    
    def _parse_workflow_data(self, data: Dict[str, Any]) -> WorkflowDefinition:
        """解析工作流数据"""
        # 验证工作流结构
        self._validate_workflow(data)
        
        # 创建工作流定义
        workflow_def = WorkflowDefinition(
            id=data["id"],
            name=data["name"],
            description=data.get("description", ""),
            version=data.get("version", "1.0.0"),
            global_timeout=data.get("global_timeout"),
            max_parallel_tasks=data.get("max_parallel_tasks", 10),
            metadata=data.get("metadata", {})
        )
        
        # 解析节点
        nodes_data = data.get("nodes", {})
        for node_id, node_data in nodes_data.items():
            node = self._parse_node(node_id, node_data)
            workflow_def.nodes[node_id] = node
        
        # 解析边
        edges_data = data.get("edges", [])
        for edge_data in edges_data:
            self._validate_edge(edge_data)
            workflow_def.edges.append(edge_data)
        
        # 设置开始和结束节点
        workflow_def.start_nodes = data.get("start_nodes", [])
        workflow_def.end_nodes = data.get("end_nodes", [])
        
        # 如果没有指定开始节点，自动推断
        if not workflow_def.start_nodes:
            workflow_def.start_nodes = self._infer_start_nodes(workflow_def)
        
        # 如果没有指定结束节点，自动推断
        if not workflow_def.end_nodes:
            workflow_def.end_nodes = self._infer_end_nodes(workflow_def)
        
        # 验证工作流完整性
        self._validate_workflow_integrity(workflow_def)
        
        return workflow_def
    
    def _parse_node(self, node_id: str, node_data: Dict[str, Any]) -> WorkflowNode:
        """解析节点数据"""
        self._validate_node(node_data)
        
        node_type_str = node_data.get("type", "task")
        node_type = self.node_type_mapping.get(node_type_str, NodeType.TASK)
        
        node = WorkflowNode(
            id=node_id,
            name=node_data.get("name", node_id),
            node_type=node_type,
            agent_id=node_data.get("agent_id"),
            task_config=node_data.get("task_config", {}),
            dependencies=node_data.get("dependencies", []),
            conditions=node_data.get("conditions", {}),
            timeout=node_data.get("timeout"),
            max_retries=node_data.get("max_retries", 3)
        )
        
        # 验证条件节点的条件配置
        if node_type == NodeType.CONDITION:
            self._validate_condition(node.conditions)
        
        return node
    
    def _infer_start_nodes(self, workflow_def: WorkflowDefinition) -> List[str]:
        """推断开始节点"""
        all_nodes = set(workflow_def.nodes.keys())
        target_nodes = set()
        
        for edge in workflow_def.edges:
            target_nodes.add(edge["to"])
        
        # 没有入边的节点是开始节点
        start_nodes = list(all_nodes - target_nodes)
        return start_nodes
    
    def _infer_end_nodes(self, workflow_def: WorkflowDefinition) -> List[str]:
        """推断结束节点"""
        all_nodes = set(workflow_def.nodes.keys())
        source_nodes = set()
        
        for edge in workflow_def.edges:
            source_nodes.add(edge["from"])
        
        # 没有出边的节点是结束节点
        end_nodes = list(all_nodes - source_nodes)
        return end_nodes
    
    # 验证方法
    def _validate_workflow(self, data: Dict[str, Any]):
        """验证工作流结构"""
        required_fields = ["id", "name"]
        for field in required_fields:
            if field not in data:
                raise ValueError(f"缺少必需字段: {field}")
        
        if not isinstance(data.get("nodes", {}), dict):
            raise ValueError("nodes字段必须是字典类型")
        
        if not isinstance(data.get("edges", []), list):
            raise ValueError("edges字段必须是列表类型")
    
    def _validate_node(self, node_data: Dict[str, Any]):
        """验证节点结构"""
        node_type = node_data.get("type", "task")
        if node_type not in self.node_type_mapping:
            raise ValueError(f"不支持的节点类型: {node_type}")
        
        # 任务节点必须指定agent_id
        if node_type == "task" and not node_data.get("agent_id"):
            raise ValueError("任务节点必须指定agent_id")
        
        # 条件节点必须有条件配置
        if node_type == "condition" and not node_data.get("conditions"):
            raise ValueError("条件节点必须配置conditions")
    
    def _validate_condition(self, conditions: Dict[str, Any]):
        """验证条件配置"""
        if not conditions:
            return
        
        for condition_name, condition_config in conditions.items():
            if condition_name == "default":
                continue
            
            if "condition" not in condition_config:
                raise ValueError(f"条件 {condition_name} 缺少condition配置")
            
            condition = condition_config["condition"]
            if "type" not in condition:
                raise ValueError(f"条件 {condition_name} 缺少type字段")
    
    def _validate_edge(self, edge_data: Dict[str, Any]):
        """验证边结构"""
        required_fields = ["from", "to"]
        for field in required_fields:
            if field not in edge_data:
                raise ValueError(f"边缺少必需字段: {field}")
    
    def _validate_workflow_integrity(self, workflow_def: WorkflowDefinition):
        """验证工作流完整性"""
        # 检查边引用的节点是否存在
        for edge in workflow_def.edges:
            from_node = edge["from"]
            to_node = edge["to"]
            
            if from_node not in workflow_def.nodes:
                raise ValueError(f"边引用了不存在的源节点: {from_node}")
            
            if to_node not in workflow_def.nodes:
                raise ValueError(f"边引用了不存在的目标节点: {to_node}")
        
        # 检查依赖关系
        for node_id, node in workflow_def.nodes.items():
            for dep_id in node.dependencies:
                if dep_id not in workflow_def.nodes:
                    raise ValueError(f"节点 {node_id} 依赖不存在的节点: {dep_id}")
        
        # 检查是否有环路
        if self._has_cycle(workflow_def):
            raise ValueError("工作流包含环路")
    
    def _has_cycle(self, workflow_def: WorkflowDefinition) -> bool:
        """检查是否有环路"""
        # 使用DFS检测环路
        visited = set()
        rec_stack = set()
        
        def dfs(node_id: str) -> bool:
            visited.add(node_id)
            rec_stack.add(node_id)
            
            # 获取邻接节点
            neighbors = []
            for edge in workflow_def.edges:
                if edge["from"] == node_id:
                    neighbors.append(edge["to"])
            
            for neighbor in neighbors:
                if neighbor not in visited:
                    if dfs(neighbor):
                        return True
                elif neighbor in rec_stack:
                    return True
            
            rec_stack.remove(node_id)
            return False
        
        for node_id in workflow_def.nodes:
            if node_id not in visited:
                if dfs(node_id):
                    return True
        
        return False


class WorkflowDSLGenerator:
    """工作流DSL生成器"""
    
    def __init__(self):
        self.node_type_reverse_mapping = {
            NodeType.TASK: "task",
            NodeType.PARALLEL: "parallel",
            NodeType.CONDITION: "condition",
            NodeType.LOOP: "loop",
            NodeType.MERGE: "merge",
            NodeType.SPLIT: "split",
            NodeType.DELAY: "delay",
            NodeType.HUMAN_APPROVAL: "human_approval"
        }
    
    def generate_yaml(self, workflow_def: WorkflowDefinition) -> str:
        """生成YAML格式的工作流定义"""
        data = self._workflow_to_dict(workflow_def)
        return yaml.dump(data, default_flow_style=False, allow_unicode=True)
    
    def generate_json(self, workflow_def: WorkflowDefinition) -> str:
        """生成JSON格式的工作流定义"""
        data = self._workflow_to_dict(workflow_def)
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    def _workflow_to_dict(self, workflow_def: WorkflowDefinition) -> Dict[str, Any]:
        """将工作流定义转换为字典"""
        data = {
            "id": workflow_def.id,
            "name": workflow_def.name,
            "description": workflow_def.description,
            "version": workflow_def.version,
            "nodes": {},
            "edges": workflow_def.edges,
            "start_nodes": workflow_def.start_nodes,
            "end_nodes": workflow_def.end_nodes
        }
        
        # 添加可选字段
        if workflow_def.global_timeout:
            data["global_timeout"] = workflow_def.global_timeout
        
        if workflow_def.max_parallel_tasks != 10:
            data["max_parallel_tasks"] = workflow_def.max_parallel_tasks
        
        if workflow_def.metadata:
            data["metadata"] = workflow_def.metadata
        
        # 转换节点
        for node_id, node in workflow_def.nodes.items():
            data["nodes"][node_id] = self._node_to_dict(node)
        
        return data
    
    def _node_to_dict(self, node: WorkflowNode) -> Dict[str, Any]:
        """将节点转换为字典"""
        data = {
            "name": node.name,
            "type": self.node_type_reverse_mapping[node.node_type]
        }
        
        # 添加可选字段
        if node.agent_id:
            data["agent_id"] = node.agent_id
        
        if node.task_config:
            data["task_config"] = node.task_config
        
        if node.dependencies:
            data["dependencies"] = node.dependencies
        
        if node.conditions:
            data["conditions"] = node.conditions
        
        if node.timeout:
            data["timeout"] = node.timeout
        
        if node.max_retries != 3:
            data["max_retries"] = node.max_retries
        
        return data


class WorkflowTemplateLibrary:
    """工作流模板库"""
    
    def __init__(self):
        self.templates = {}
        self._load_builtin_templates()
    
    def _load_builtin_templates(self):
        """加载内置模板"""
        # 软件开发工作流模板
        self.templates["software_development"] = {
            "id": "software_development_template",
            "name": "软件开发工作流",
            "description": "完整的软件开发生命周期工作流",
            "version": "1.0.0",
            "nodes": {
                "requirements_analysis": {
                    "name": "需求分析",
                    "type": "task",
                    "agent_id": "product_manager",
                    "task_config": {
                        "type": "analyze_requirements",
                        "description": "分析项目需求"
                    }
                },
                "architecture_design": {
                    "name": "架构设计",
                    "type": "task",
                    "agent_id": "architect",
                    "task_config": {
                        "type": "design_system",
                        "description": "设计系统架构"
                    },
                    "dependencies": ["requirements_analysis"]
                },
                "parallel_development": {
                    "name": "并行开发",
                    "type": "parallel"
                },
                "frontend_development": {
                    "name": "前端开发",
                    "type": "task",
                    "agent_id": "coder",
                    "task_config": {
                        "type": "generate_code",
                        "description": "开发前端代码",
                        "parameters": {"component": "frontend"}
                    },
                    "dependencies": ["architecture_design"]
                },
                "backend_development": {
                    "name": "后端开发",
                    "type": "task",
                    "agent_id": "coder",
                    "task_config": {
                        "type": "generate_code",
                        "description": "开发后端代码",
                        "parameters": {"component": "backend"}
                    },
                    "dependencies": ["architecture_design"]
                },
                "code_review": {
                    "name": "代码审查",
                    "type": "task",
                    "agent_id": "coder",
                    "task_config": {
                        "type": "review_code",
                        "description": "审查代码质量"
                    },
                    "dependencies": ["frontend_development", "backend_development"]
                },
                "quality_gate": {
                    "name": "质量门禁",
                    "type": "condition",
                    "conditions": {
                        "code_quality_pass": {
                            "condition": {
                                "type": "greater_than",
                                "field": "code_review.quality_score",
                                "value": 80
                            },
                            "next_nodes": ["testing"]
                        },
                        "default": {
                            "next_nodes": ["fix_issues"]
                        }
                    },
                    "dependencies": ["code_review"]
                },
                "fix_issues": {
                    "name": "修复问题",
                    "type": "task",
                    "agent_id": "coder",
                    "task_config": {
                        "type": "fix_code",
                        "description": "修复代码问题"
                    }
                },
                "testing": {
                    "name": "测试",
                    "type": "task",
                    "agent_id": "qa",
                    "task_config": {
                        "type": "create_test_cases",
                        "description": "执行测试"
                    }
                },
                "deployment": {
                    "name": "部署",
                    "type": "task",
                    "agent_id": "devops",
                    "task_config": {
                        "type": "deploy_infrastructure",
                        "description": "部署到生产环境"
                    },
                    "dependencies": ["testing"]
                },
                "documentation": {
                    "name": "文档生成",
                    "type": "task",
                    "agent_id": "documentation",
                    "task_config": {
                        "type": "generate_documentation",
                        "description": "生成项目文档"
                    },
                    "dependencies": ["deployment"]
                }
            },
            "edges": [
                {"from": "requirements_analysis", "to": "architecture_design"},
                {"from": "architecture_design", "to": "frontend_development"},
                {"from": "architecture_design", "to": "backend_development"},
                {"from": "frontend_development", "to": "code_review"},
                {"from": "backend_development", "to": "code_review"},
                {"from": "code_review", "to": "quality_gate"},
                {"from": "quality_gate", "to": "testing"},
                {"from": "quality_gate", "to": "fix_issues"},
                {"from": "fix_issues", "to": "code_review"},
                {"from": "testing", "to": "deployment"},
                {"from": "deployment", "to": "documentation"}
            ],
            "start_nodes": ["requirements_analysis"],
            "end_nodes": ["documentation"]
        }
        
        # 简单任务工作流模板
        self.templates["simple_task"] = {
            "id": "simple_task_template",
            "name": "简单任务工作流",
            "description": "单一任务的简单工作流",
            "version": "1.0.0",
            "nodes": {
                "main_task": {
                    "name": "主要任务",
                    "type": "task",
                    "agent_id": "coder",
                    "task_config": {
                        "type": "general",
                        "description": "执行主要任务"
                    }
                }
            },
            "edges": [],
            "start_nodes": ["main_task"],
            "end_nodes": ["main_task"]
        }
        
        # 并行处理工作流模板
        self.templates["parallel_processing"] = {
            "id": "parallel_processing_template",
            "name": "并行处理工作流",
            "description": "多任务并行处理工作流",
            "version": "1.0.0",
            "nodes": {
                "task_split": {
                    "name": "任务分割",
                    "type": "split"
                },
                "task_1": {
                    "name": "任务1",
                    "type": "task",
                    "agent_id": "coder",
                    "task_config": {
                        "type": "process_data",
                        "description": "处理数据集1"
                    },
                    "dependencies": ["task_split"]
                },
                "task_2": {
                    "name": "任务2",
                    "type": "task",
                    "agent_id": "coder",
                    "task_config": {
                        "type": "process_data",
                        "description": "处理数据集2"
                    },
                    "dependencies": ["task_split"]
                },
                "task_3": {
                    "name": "任务3",
                    "type": "task",
                    "agent_id": "coder",
                    "task_config": {
                        "type": "process_data",
                        "description": "处理数据集3"
                    },
                    "dependencies": ["task_split"]
                },
                "task_merge": {
                    "name": "任务合并",
                    "type": "merge",
                    "dependencies": ["task_1", "task_2", "task_3"]
                },
                "final_processing": {
                    "name": "最终处理",
                    "type": "task",
                    "agent_id": "coder",
                    "task_config": {
                        "type": "aggregate_results",
                        "description": "聚合处理结果"
                    },
                    "dependencies": ["task_merge"]
                }
            },
            "edges": [
                {"from": "task_split", "to": "task_1"},
                {"from": "task_split", "to": "task_2"},
                {"from": "task_split", "to": "task_3"},
                {"from": "task_1", "to": "task_merge"},
                {"from": "task_2", "to": "task_merge"},
                {"from": "task_3", "to": "task_merge"},
                {"from": "task_merge", "to": "final_processing"}
            ],
            "start_nodes": ["task_split"],
            "end_nodes": ["final_processing"]
        }
    
    def get_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """获取模板"""
        return self.templates.get(template_name)
    
    def list_templates(self) -> List[Dict[str, str]]:
        """列出所有模板"""
        return [
            {
                "name": template_name,
                "title": template_data["name"],
                "description": template_data["description"]
            }
            for template_name, template_data in self.templates.items()
        ]
    
    def add_template(self, template_name: str, template_data: Dict[str, Any]):
        """添加自定义模板"""
        self.templates[template_name] = template_data
    
    def create_workflow_from_template(
        self, 
        template_name: str, 
        workflow_id: str,
        customizations: Dict[str, Any] = None
    ) -> WorkflowDefinition:
        """从模板创建工作流"""
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"模板不存在: {template_name}")
        
        # 复制模板数据
        workflow_data = template.copy()
        workflow_data["id"] = workflow_id
        
        # 应用自定义配置
        if customizations:
            self._apply_customizations(workflow_data, customizations)
        
        # 解析为工作流定义
        parser = WorkflowDSLParser()
        return parser._parse_workflow_data(workflow_data)
    
    def _apply_customizations(self, workflow_data: Dict[str, Any], customizations: Dict[str, Any]):
        """应用自定义配置"""
        # 更新基本信息
        if "name" in customizations:
            workflow_data["name"] = customizations["name"]
        
        if "description" in customizations:
            workflow_data["description"] = customizations["description"]
        
        # 更新节点配置
        if "nodes" in customizations:
            for node_id, node_customizations in customizations["nodes"].items():
                if node_id in workflow_data["nodes"]:
                    workflow_data["nodes"][node_id].update(node_customizations)
        
        # 更新全局配置
        if "global_timeout" in customizations:
            workflow_data["global_timeout"] = customizations["global_timeout"]
        
        if "max_parallel_tasks" in customizations:
            workflow_data["max_parallel_tasks"] = customizations["max_parallel_tasks"]