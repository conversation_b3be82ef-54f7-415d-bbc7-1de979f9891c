#!/usr/bin/env python3
"""
测试LLM配置
"""

import sys
import os

# 添加src到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_llm_config():
    """测试LLM配置"""
    try:
        from langgraph_system.llm.config import LLMConfig
        
        # 测试kimi-k2-0711-preview配置
        config = LLMConfig.for_kimi_k2_0711_preview()
        
        print("✅ LLM配置测试成功")
        print(f"📋 模型: {config.model}")
        print(f"🔧 提供商: {config.provider}")
        print(f"🌡️  温度: {config.temperature}")
        
        # 测试客户端配置
        client_config = config.get_client_config()
        print("✅ 客户端配置生成成功")
        print(f"🔗 基础URL: {client_config.get('base_url', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_env_config():
    """测试环境变量配置"""
    try:
        from langgraph_system.config.settings import get_settings
        
        settings = get_settings()
        print("✅ 设置加载成功")
        print(f"🎯 默认提供商: {settings.default_provider}")
        print(f"🎯 默认模型: {settings.default_model}")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境配置测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== LLM配置测试 ===")
    
    success1 = test_llm_config()
    success2 = test_env_config()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("📖 查看文档: docs/LLM_CONFIG.md")
        print("🚀 运行示例: python examples/use_kimi_k2.py")
    else:
        print("\n⚠️  部分测试失败，请检查配置")
